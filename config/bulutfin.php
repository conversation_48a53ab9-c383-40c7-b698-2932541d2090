<?php
return [
    'base_url' => env('BULUTFIN_BASE_URL', 'http://www.bulutfin.com/api'),

    'endpoints' => [
        'yillikozet' => [
            'method' => 'post',
            'uri'    => '/bordro/yillikozet',
        ],
        'yillikozet_filtrele' => [
            'method' => 'post',
            'uri'    => '/bordro/yillikozet/filtrele',
        ],

        'sifirla' => [
            'method' => 'post',
            'uri'    => '/bordro/sifirla',
        ],

        'haksat' => [
            'method' => 'post',
            'uri'    => '/bordro/haksat',
        ],

        'haksatal' => [
            'method' => 'post',
            'uri'    => '/bordro/haksatal',
        ],

        'hakal' => [
            'method' => 'post',
            'uri'    => '/bordro/hakal',
        ],

    ],

    'required_params' => [
        '*'           => ['sicilNo'],
        'yillikozet'  => [],
        'yillikozet_filtrele'   => ['sicilNo','typ'],
        'sifirla'              => [],
        'haksat'              => ['hakCode','netBrut','tutar','kismiTam'],

        'haksatal'           => [
            'satHakCode',
            'alHakCode',
            'sicilNo',
            'alNetBrut',
            'alTutar',
        ],
        'hakal' => ['hakCode','sicilNo','netBrut','tutar'],
        'haksat' => ['hakCode','netBrut','tutar','kismiTam'],

    ],

    'param_resolvers' => [
        'sicilNo' => function() {
            return auth('member')->user()->identity_number;
        },
    ],
];

