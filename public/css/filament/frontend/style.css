@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';


@font-face {
  font-family: 'Poppins';
  src: url('public/assets/fonts/Poppins-Regular.ttf') format('ttf'),
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Poppins', sans-serif;
}

.main_login {
  min-height: calc(100vh - 180px);
}

#q3 {
  background: url("/images/select_arrow-down.png") no-repeat;
  background-size: 15px;
  background-position: calc(100% - 0.75rem) center !important;
  -moz-appearance: none !important;
  -webkit-appearance: none !important;
  appearance: none !important;
  padding-right: 2rem !important;

}

#brut_izin_sayisi {
  background: url("/images/arrow_down_grey.png") no-repeat;
  background-position: calc(100% - 0.75rem) center !important;
  -moz-appearance: none !important;
  -webkit-appearance: none !important;
  appearance: none !important;
}



.radio-container input:checked+.custom-radio-label {
  background-color: #0F56AF;
  background-image: url('data:image/svg+xml;utf8,<svg fill="white" height="100%" viewBox="0 0 24 24" width="100%" xmlns="http://www.w3.org/2000/svg"><path d="M9 16.2L4.8 12l-1.4 1.4 5.6 5.6L20.2 7l-1.4-1.4z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.sirket_araci_container input:checked+.custom-checkbox-label {
  background-color: #707070;
  background-image: url('data:image/svg+xml;utf8,<svg fill="white" height="100%" viewBox="0 0 24 24" width="100%" xmlns="http://www.w3.org/2000/svg"><path d="M9 16.2L4.8 12l-1.4 1.4 5.6 5.6L20.2 7l-1.4-1.4z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

#staticModalYanHakSepeti {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

#staticModalYanHakSepeti::-webkit-scrollbar {
  display: none;
}




#yan_hak_sepeti {
  -webkit-overflow-scrolling: touch;
}

/* Hide scrollbar in Firefox */
#yan_hak_sepeti{
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Hide scrollbar in WebKit (Chrome, Safari) */
#yan_hak_sepeti::-webkit-scrollbar {
  display: none !important; /* WebKit */
}

/* Hide scrollbar in IE and Edge */
#yan_hak_sepeti {
  -ms-overflow-style: none !important; /* IE and Edge */
  scrollbar-width: none !important; /* Firefox */
  overflow-y: scroll;
}
#birey_field_p_adet_brut{
  padding-right: 0 !important;
}



