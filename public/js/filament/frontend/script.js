// LOGIN PASSWORD
$(document).ready(function () {
    $("#password_eye").on("click", function () {
        var passwordInput = $("#password");
        if (passwordInput.attr("type") === "password") {
            passwordInput.attr("type", "text");
        } else {
            passwordInput.attr("type", "password");
        }
    });
  });
  // LOGIN PASSWORD

  //NAVBAR
  $(document).ready(function () {
    $("#hamburger_menu").on("click", function () {
        $("#mobile_navbar").removeClass("scale-0");
        $("#mobile_navbar").addClass("scale-1");
    });
  });
  $(document).ready(function () {
    $("#mobile_close_btn").on("click", function () {
        $("#mobile_navbar").removeClass("scale-1");
        $("#mobile_navbar").addClass("scale-0");
    });
  });
  //NAVBAR

  //HOME ACCORDION
  // $(document).ready(function () {
  //   $(".accordion_btn").on("click", function () {
  //       let accordionItem = $(this)
  //           .closest(".accordion_div")
  //           .find(".accordion_item");
  //       // $(".accordion_item").not(accordionItem).removeClass("max-h-fit").addClass("max-h-0");
  //       accordionItem.toggleClass("max-h-0 max-h-fit");

  //       if (accordionItem.find(".max-h-fit").length !== 0) {
  //           let whiteArrow = $(this)
  //               .closest(".accordion_div")
  //               .find(".white-arrow");
  //           whiteArrow.toggleClass("rotate-0 rotate-180");
  //           accordionItem.find(".white-arrow").removeClass("rotate-180");
  //       }
  //   });
  $(document).ready(function () {
      $(document).on("click", ".accordion_btn", function () {
        let accordionDiv = $(this).closest(".accordion_div");
        let accordionItem = accordionDiv.find(".accordion_item");
        let whiteArrow = accordionDiv.find(".white-arrow");

        // "max-h-fit" sınıfının durumuna bağlı olarak işlem yap
        if (accordionItem.hasClass("max-h-fit")) {
            whiteArrow.removeClass("rotate-180").addClass("rotate-0");
        } else {
            whiteArrow.removeClass("rotate-0").addClass("rotate-180");
        }

        // "max-h-0" ve "max-h-fit" sınıflarını toggle et
        accordionItem.toggleClass("max-h-0 max-h-fit");
    });
});



//   // //HOME ACCORDION
//   // let cartSecondPartTotal1 = parseFloat($("#cart_second_part_total").text());
//   // let cartThirdPartTotal = parseFloat($("#cart_third_part_total").text());
//   // let cartFourthPartTotal = parseFloat($("#cart_fourth_part_total").text());
//   // let balance =
//   //   12551.67 -
//   //   (cartSecondPartTotal1 + cartThirdPartTotal + cartFourthPartTotal + 166.67);
//   // $("#balance").text(balance.toFixed(2));

//   // function updateBalance() {
//   //   let cartSecondPartTotal1 = parseFloat($("#cart_second_part_total").text());
//   //   let cartThirdPartTotal = parseFloat($("#cart_third_part_total").text());
//   //   let cartFourthPartTotal = parseFloat($("#cart_fourth_part_total").text());

//   //   balance =
//   //       12551.67 -
//   //       (cartSecondPartTotal1 +
//   //           cartThirdPartTotal +
//   //           cartFourthPartTotal +
//   //           166.67);

//   //   $("#balance").text(balance.toFixed(2));
//   // }

//   // let zero = 0.0;
//   // //BRÜT İZİN BUTONU
//   // let cartSecondPartTotal = parseFloat($("#cart_second_part_total").text());
//   // let izin = 0;
//   // let mealTicketValue = 0;



//   //   $("#leave_update_btn").click(function () {
//   //       let izinHakki = 0;
//   //       let existingDiv = $("#cart_second_part").find(".cart_izin_hakki");
//   //       if (existingDiv.length > 0) {
//   //           let content = `Net: 100,00 TL - Brüt ${total} TL`;
//   //           existingDiv.find(".brut_fiyat_kismi").text(content);
//   //       } else {
//   //           let newDiv = $("<div>").addClass(
//   //               "border-b border-slate-300/[0.5] pb-1 flex items-center justify-between cart_item cart_izin_hakki"
//   //           );
//   //           let innerDiv = $("<div>");
//   //           let title = $("<p>")
//   //               .addClass("text-black text-[11px] font-semibold")
//   //               .text("İzin hakkı");
//   //           let content = $("<p>")
//   //               .addClass("text-[#5F5F5F] text-[11px] brut_fiyat_kismi")
//   //               .text(
//   //                   `Net: 100,00 TL - Brüt ${
//   //                       total == 0 ? daily_wage.toFixed(2) : total
//   //                   } TL`
//   //               );
//   //           innerDiv.append(title, content);
//   //           let image = $("<img>")
//   //               .attr("src", "images/close.png")
//   //               .attr("alt", "close")
//   //               .addClass("w-[10px] cursor-pointer cart_close_btn");
//   //           newDiv.append(innerDiv, image);
//   //           $("#cart_second_part").prepend(newDiv);
//   //       }
//   //       izinHakki = total == 0 ? parseFloat(daily_wage) : parseFloat(total);
//   //       izin = parseFloat(izinHakki);
//   //       cartSecondPartTotal = mealTicketValue + izin;
//   //       $("#cart_second_part_total").text(
//   //           `${cartSecondPartTotal.toFixed(2)} TL`
//   //       );
//   //       updateBalance();
//   //   });
//   // });
//   // //BRÜT İZİN BUTONU


  // //ŞİRKET ARACI CHECKBOX

  $("#sirket_araci").prop("checked",true);
  $(`#sirket_araci`).closest(".checkbox-container").addClass("bg-[#707070]/[0.3]");
  $(`#sirket_araci`).prop("checked", true);
  $(`#sirket_araci`)
    .closest(".checkbox-container")
    .addClass("bg-[#707070]/[0.3]");
  const updateCheckboxContainerBackground = (checkbox) => {
    const checkboxContainer = checkbox.closest(".checkbox-container");

    if (
        checkboxContainer.parentElement.classList.contains(
            "sirket_araci_container"
        )
    ) {
        if (checkbox.checked) {
            checkboxContainer.classList.add("bg-[#707070]/[0.3]");
        } else {
            checkboxContainer.classList.remove("bg-[#707070]/[0.3]");
        }
    }
  };
  // //ŞİRKET ARACI CHECKBOX

  // //MEAL TICKET RADIO FUNCTION
  const updateRadioContainerBackground = (radio) => {
    const radioContainers2 = document.querySelectorAll(".meal_ticket_container .radio-container");

    const radioContainers3 = document.querySelectorAll(".plan_container .radio-container");
    radioContainers2.forEach((container) => {
        container.classList.remove("bg-[#0F56AF]/[0.3]");
    });
    radioContainers3.forEach((container) => {
        container.classList.remove("bg-[#0F56AF]/[0.3]");
    });

    const radioContainer2 = radio.closest(".radio-container");
    radioContainer2.classList.add("bg-[#0F56AF]/[0.3]");
    const radioContainer3 = radio.closest(".radio-container");
    radioContainer3.classList.add("bg-[#0F56AF]/[0.3]");
  };
  // //**************** */

//   // const cartContainer = $("#cart_second_part");
//   // $("input[name='meal_ticket']").on("change", function () {
//   //   if ($(this).is(":checked")) {
//   //       const selectedValue = parseFloat(
//   //           $(this)
//   //               .closest(".meal_ticket_container")
//   //               .parent()
//   //               .find(".meal_ticket_fee")
//   //               .text()
//   //               .trim()
//   //       );

//   //       const existingItem = cartContainer.find(".meal_ticket_cart_item");

//   //       if (existingItem.length > 0) {
//   //           existingItem
//   //               .find(".fiyat_kismi")
//   //               .text(
//   //                   `Net: ${selectedValue.toFixed(
//   //                       2
//   //                   )} TL - Brüt: ${selectedValue.toFixed(2)} TL`
//   //               );
//   //       } else {
//   //           const newDiv = $("<div>").addClass(
//   //               "border-b border-slate-300/[0.5] py-1 flex items-center justify-between cart_item meal_ticket_cart_item"
//   //           );
//   //           const innerDiv = $("<div>");
//   //           const title = $("<p>")
//   //               .addClass("text-black text-[11px] font-semibold")
//   //               .text("Yemek kartı - Ticket");
//   //           const content = $("<p>")
//   //               .addClass("text-[#5F5F5F] text-[11px] fiyat_kismi")
//   //               .text(
//   //                   `Net: ${selectedValue.toFixed(
//   //                       2
//   //                   )} TL - Brüt: ${selectedValue.toFixed(2)} TL`
//   //               );
//   //           innerDiv.append(title, content);
//   //           const image = $("<img>")
//   //               .attr("src", "images/close.png")
//   //               .attr("alt", "close")
//   //               .addClass("w-[10px] cursor-pointer cart_close_btn");
//   //           newDiv.append(innerDiv, image);

//   //           cartContainer.append(newDiv);
//   //       }
//   //       mealTicketValue = selectedValue;
//   //       cartSecondPartTotal = mealTicketValue + izin;
//   //       $("#cart_second_part_total").text(
//   //           `${cartSecondPartTotal.toFixed(2)} TL`
//   //       );
//   //       updateBalance();
//   //   }
//   // });

//   // //MEAL TICKET RADIO FUNCTION

//   // //************** */

//   // //PLAN

//   // $('input[name="plan"]').change(function () {
//   //   let selectedValue = parseFloat(
//   //       $(this)
//   //           .closest(".plan_container")
//   //           .parent()
//   //           .find(".private_health_policy_value")
//   //           .text()
//   //           .trim()
//   //   );

//   //   let newCartItem = `
//   //     <div class="pb-1 flex items-center justify-between cart_item private_health_policy_div">
//   //         <div>
//   //             <p class="text-black text-[11px] font-semibold">
//   //                 Özel Sağlık Sigortası
//   //             </p>
//   //             <p class="text-[#5F5F5F] text-[11px] private_health_policy_fee">
//   //                 Net: 3000,00 TL - Brüt: ${selectedValue.toFixed(2)}
//   //             </p>
//   //         </div>
//   //         <img src="images/close.png" alt="close" class="w-[11px] cursor-pointer cart_close_btn">
//   //     </div>
//   // `;

//   //   // Check if there's an existing private_health_policy_div
//   //   let existingDiv = $("#cart_third_part .private_health_policy_div");

//   //   if (existingDiv.length > 0) {
//   //       // Update the content of the existing private_health_policy_fee
//   //       existingDiv.replaceWith(newCartItem);
//   //   } else {
//   //       // Append the new div to the cart_third_part div using jQuery
//   //       $("#cart_third_part").append(newCartItem);
//   //   }
//   //   $("#cart_third_part_total").text(`${selectedValue.toFixed(2)} TL`);
//   //   updateBalance();
//   // });

//   // //PLAN

//   // //YAN HAK FIRSATI
//   // let totalSum = 0;

//   // // Function to update totalSum when a div is removed
//   // function updateTotalSumOnRemove(removedValue) {
//   //   totalSum -= removedValue;
//   //   $("#cart_fourth_part_total").text(`${totalSum.toFixed(2)} TL`);
//   // }

//   // $(".yan_hak_fırsatları_update_button").on("click", function () {
//   //   let inputValue;
//   //   let title;

//   //   if ($(window).width() >= 768) {
//   //       inputValue = $(this).parent().find(".yan_hak_input").val();
//   //       inputValue = Number(inputValue);
//   //       title = $(this).parent().parent().find(".yan_hak_tittle").text();
//   //       $(".yan_hak_input").val("")
//   //   } else {
//   //       inputValue = $(this).parent().find(".yan_hak_input_mobile").val();
//   //       inputValue = Number(inputValue);
//   //       title = $(this).parent().prev().find(".yan_hak_tittle").text();
//   //       $(".yan_hak_input_mobile").val("");
//   //   }

//   //   let existingDiv = $("#cart_fourth_part").find(
//   //       `.cart_item:has(.tittle_part:contains('${title}'))`
//   //   );

//   //   if (existingDiv.length > 0) {
//   //       let existingInput = existingDiv.find(".inputValue");
//   //       let existingValue = Number(
//   //           existingInput.text().match(/Brüt: (\d+\.\d+)/)[1]
//   //       );

//   //       // Subtract the existingValue from totalSum before updating
//   //       totalSum -= existingValue;

//   //       // Add the new inputValue to totalSum
//   //       totalSum += inputValue;

//   //       existingInput.text(
//   //           `Net: 100.00 TL - Brüt: ${inputValue.toFixed(2)} TL`
//   //       );
//   //   } else {
//   //       if (inputValue !== 0) {
//   //           let newDiv = `
//   //               <div class="border-b border-slate-300/[0.5] py-1 flex items-center justify-between cart_item">
//   //                   <div>
//   //                       <p class="text-black text-[11px] font-semibold tittle_part">
//   //                           ${title}
//   //                       </p>
//   //                       <p class="text-[#5F5F5F] text-[11px] inputValue">
//   //                           Net: 100.00 TL - Brüt: ${inputValue.toFixed(2)} TL
//   //                       </p>
//   //                   </div>
//   //                   <img src="images/close.png" alt="close" class="w-[11px] cursor-pointer cart_close_btn">
//   //               </div>
//   //           `;

//   //           $("#cart_fourth_part").append(newDiv);

//   //           // Add the inputValue to totalSum
//   //           totalSum += inputValue;
//   //       }
//   //   }

//   //   // Update #cart_fourth_part_total with the current totalSum
//   //   $("#cart_fourth_part_total").text(`${totalSum.toFixed(2)} TL`);
//   //   updateBalance();
//   // });

//   // // Event delegation for dynamically added elements
//   // $("#cart_fourth_part").on("click", ".cart_close_btn", function () {
//   //   let removedValue = parseFloat(
//   //       $(this)
//   //           .closest(".cart_item")
//   //           .find(".inputValue")
//   //           .text()
//   //           .match(/Brüt: (\d+\.\d+)/)[1]
//   //   );

//   //   // Remove the div
//   //   $(this).closest(".cart_item").remove();

//   //   // Update totalSum on removal
//   //   updateTotalSumOnRemove(removedValue);
//   // });

//   // //YAN HAK FIRSATI


//   // //********************************************* */
//   // //SIDEBAR
//   $(document).ready(function () {
//     $("#cart_btn").click(function () {
//         if ($("#yan_hak_sepeti").hasClass("right-[-100%]")) {
//             $("#yan_hak_sepeti").removeClass("right-[-100%]");
//             $("#yan_hak_sepeti").addClass("right-0");
//         } else {
//             $("#yan_hak_sepeti").removeClass("right-0");
//             $("#yan_hak_sepeti").addClass("right-[-100%]");
//         }
//     });

//     $("#yan_haklar_sepeti_close_btn").click(function () {
//         $("#yan_hak_sepeti").removeClass("right-0");
//         $("#yan_hak_sepeti").addClass("right-[-100%]");
//     });
//     $("#alısverisi_tamamla").click(function () {
//         $("#yan_hak_sepeti").removeClass("right-0");
//         $("#yan_hak_sepeti").addClass("right-[-100%]");
//     });
//     $("#sepeti_sifirla").click(function () {
//         $("#yan_hak_sepeti").removeClass("right-0");
//         $("#yan_hak_sepeti").addClass("right-[-100%]");
//     });
//   });

//   // //SEPETTEKİ X BUTONUNA BASINCA O ITEM IN SİLİNMESİ
//   // $(document).ready(function () {
//   //   $(".cart_close_btn").on("click", function () {
//   //       const accordionDiv = $(this).closest(".accordion_div");
//   //       $(this).closest(".cart_item").remove();

//   //       if (accordionDiv.find(".cart_item").length === 0) {
//   //           accordionDiv.find(".white-arrow").removeClass("rotate-180");
//   //       }
//   //   });

//   //   $("#cart_second_part").on("click", ".cart_close_btn", function () {
//   //       $(this).closest(".cart_item").remove();
//   //       $("#cart_second_part")
//   //           .closest(".accordion_div")
//   //           .find($(".white-arrow"))
//   //           .removeClass("rotate-180");
//   //       $("input[name='meal_ticket']").prop("checked", false);
//   //       $("input[name='meal_ticket']")
//   //           .closest(".radio-container")
//   //           .removeClass("bg-[#0F56AF]/[0.3]");
//   //       if ($(this).closest(".cart_item").hasClass("meal_ticket_cart_item")) {
//   //           cartSecondPartTotal1 -= mealTicketValue;
//   //           cartSecondPartTotal -= mealTicketValue;
//   //       } else {
//   //           cartSecondPartTotal1 -= izin;
//   //           cartSecondPartTotal -= izin;
//   //       }

//   //       $("#cart_second_part_total").text(
//   //           `${cartSecondPartTotal.toFixed(2)} TL`
//   //       );
//   //       updateBalance();
//   //   });
//   //   $("#cart_third_part").on("click", ".cart_close_btn", function () {
//   //       $(this).closest(".cart_item").remove();
//   //       $("#cart_third_part")
//   //           .closest(".accordion_div")
//   //           .find($(".white-arrow"))
//   //           .removeClass("rotate-180");
//   //       $("input[name='plan']").prop("checked", false);
//   //       $("input[name='plan']")
//   //           .closest(".radio-container")
//   //           .removeClass("bg-[#0F56AF]/[0.3]");

//   //       $("#cart_third_part_total").text(`${zero.toFixed(2)} TL`);
//   //       updateBalance();
//   //   });
//   //   $("#cart_fourth_part").on("click", ".cart_close_btn", function () {
//   //       $(this).closest(".cart_item").remove();
//   //       $("#cart_fourth_part")
//   //           .closest(".accordion_div")
//   //           .find($(".white-arrow"))
//   //           .removeClass("rotate-180");
//   //       $(".yan_hak_input").val("");
//   //       updateBalance();
//   //   });
//   // });

//   // //SEPETTEKİ SIFIRLA
//   // $(document).ready(function () {
//   //   $("#remove_cart_btn").on("click", function () {
//   //       $(".cart_item").remove();
//   //       $(".white-arrow").removeClass("rotate-180");
//   //       $("#cart_second_part_total").text(`${zero.toFixed(2)} TL`);
//   //       $("#cart_third_part_total").text(`${zero.toFixed(2)} TL`);
//   //       $("#cart_fourth_part_total").text(`${zero.toFixed(2)} TL`);
//   //       $(".yan_hak_input").val("");
//   //   updateBalance();
//   //   });
//   // });


//   //********************************************************************* */
//   let storedSecondCartArray = JSON.parse(localStorage.getItem('secondCartArray')) || [];
//   let storedThirdCartArray = JSON.parse(localStorage.getItem('thirdCartArray')) || [];
//   let storedFourthCartArray = JSON.parse(localStorage.getItem('fourthCartArray')) || [];
//   let allArrays = storedSecondCartArray.concat(storedThirdCartArray, storedFourthCartArray);


//    let totalCash=Number((12551.67-166.67).toFixed(2));
//    totalCash=(parseFloat(totalCash))
//    $("#balance").text(totalCash +" TL")
//   //  console.log(totalCash,"1")


//   // Function to update the second cart
//   function updateSecondCart(selectedItems) {
//     const cartContainer = $("#cart_second_part");

//     // Clear existing cart items
//     cartContainer.empty();

//     // Add the selected items to the cart
//     selectedItems.forEach(selectedItem => {
//       const newDiv = $("<div>").addClass("border-b border-slate-300/[0.5] pb-1 flex items-center justify-between cart_item");
//       const innerDiv = $("<div>");
//       const title = $("<p>").addClass("text-black text-[11px] font-semibold").text(selectedItem.title);
//       const content = $("<p>").addClass("text-[#5F5F5F] text-[11px] meal_content").text(`Net: 100,00 TL - Brüt ${selectedItem.content}`);
//       const image = $("<img>").attr("src", "images/close.png").attr("alt", "close").addClass("w-[10px] cursor-pointer cart_close_btn");

//       innerDiv.append(title, content);
//       newDiv.append(innerDiv, image);

//       // Append the new item to the cart
//       cartContainer.append(newDiv);
//     });
//   }

//   // Function to update the second cart array and trigger the update
//   function updateSecondCartArray(title, content, imgSrc) {
//     let storedSecondCartArray = JSON.parse(localStorage.getItem('secondCartArray')) || [];

//     // Check if the item with the same title already exists
//     const existingItemIndex = storedSecondCartArray.findIndex(item => item.title === title);

//     if (existingItemIndex !== -1) {
//       // If the item exists, update its content
//       storedSecondCartArray[existingItemIndex].content = content;
//     } else {
//       // If the item doesn't exist, create a new item and add it to the array
//       const newItem = { title, content, imgSrc };
//       storedSecondCartArray.push(newItem);
//     }


//     // Update local storage with the modified array
//     localStorage.setItem('secondCartArray', JSON.stringify(storedSecondCartArray));

//     // Update the cart display
//     updateSecondCart(storedSecondCartArray);
//     $("#cart_second_part_total").text(calculateTotalContent(storedSecondCartArray) + " TL")
//     totalCash=totalCash-Number(calculateTotalContent(storedSecondCartArray))
//     // console.log(totalCash,"2")


//   }

//   // On page load, get the selected items for the second cart and update the cart
//   $(document).ready(function () {
//     let storedSecondCartArray = JSON.parse(localStorage.getItem('secondCartArray')) || [];

//     if (storedSecondCartArray.length > 0) {
//       // Update the cart with the stored items
//       updateSecondCart(storedSecondCartArray);
//       $("#cart_second_part_total").text(calculateTotalContent(storedSecondCartArray) + " TL")
//     }
//   });

//   // When a radio button for the second cart is clicked, update the cart and local storage
//   $("input[name='meal_ticket']").on("change", function () {


//     if ($(this).is(":checked")) {
//       const title = $(".yemek_karti_title").text().trim();
//       const content = parseFloat($(this)
//         .closest(".meal_ticket_container")
//         .parent()
//         .find(".meal_ticket_fee")
//         .text()
//         .trim());
//       const imgSrc = $(".yemek_karti_img").attr("src");

//       const selectedItemInfo = {
//         title: title,
//         content: content.toFixed(2) + " TL",
//         imgSrc: imgSrc,
//         value: $(this).val() // Store the value of the selected radio button
//       };

//       updateSecondCartArray(selectedItemInfo.title, selectedItemInfo.content, selectedItemInfo.imgSrc);
//     }
//   });

//   // On button click, update the second cart array for "İzin Hakkı"
//   $("#leave_update_btn").click(function () {
//     // $("#brut_izin_sayisi").change(function () {
//     //   if($("#brut_izin_sayisi").val() == 2){
//     //     $("#brut_izin_tutari").val("500.00")
//     //   }else{
//     //     $("#brut_izin_tutari").val("250.00")
//     //   }
//     // })
//     const title = $(".izin_hakki_title").text().trim();
//     const content = parseFloat($("#brut_izin_tutari").val());
//     // console.log(content,"şşş")
//     const imgSrc = $(".izin_hakki_img").attr("src");

//     const izinHakkiItemInfo = {
//       title: title,
//       content: content.toFixed(2) + " TL",
//       imgSrc: imgSrc,
//       value: $(this).val() // Store the value of the selected radio button
//     };

//     // Update local storage and the cart
//     updateSecondCartArray(izinHakkiItemInfo.title, izinHakkiItemInfo.content, izinHakkiItemInfo.imgSrc);

//   });







//   $(document).on("click", ".cart_close_btn", function () {
//     $(this).closest(".cart_item").remove();
//     localStorage.setItem('secondCartArray', JSON.stringify([]));
//     $("input[name='meal_ticket']").prop('checked', false);
//     $(".radio-container").removeClass("bg-[#0F56AF]/[0.3]");
//     if( $("#cart_second_part")
//               .closest(".accordion_div")
//               .find(".cart_item").length === 0){
//                 $("#cart_second_part")
//               .closest(".accordion_div").find(".white-arrow").removeClass("rotate-180");
//               $("#cart_second_part_total")
//               .text("0.00 TL")
//               }

//   });

//   //*********************************************************************************** */
//   let sum3=0
//   // Function to update the third cart
//   function updateThirdCart(selectedItem) {
//     const cartContainer = $("#cart_third_part");

//     // Clear existing cart items
//     cartContainer.empty();

//     // Add the selected item to the cart
//     const newDiv = $("<div>").addClass("border-b border-slate-300/[0.5] pb-1 flex items-center justify-between cart_item");
//     const innerDiv = $("<div>");
//     const title = $("<p>").addClass("text-black text-[11px] font-semibold").text(selectedItem.title);
//     const content = $("<p>").addClass("text-[#5F5F5F] text-[11px]").text(`Net: 100,00 TL - Brüt ${selectedItem.content}`);
//     const image = $("<img>").attr("src", "images/close.png").attr("alt", "close").addClass("w-[10px] cursor-pointer cart_close_btn");

//     innerDiv.append(title, content);
//     newDiv.append(innerDiv, image);

//     cartContainer.append(newDiv);
//     sum3=parseFloat(selectedItem.content).toFixed(2)
//     $("#cart_third_part_total").text(`${sum3} TL`)
//     totalCash=totalCash-Number(sum3)
//     // console.log(totalCash,"3")

//   }
//   $(document).ready(function () {
//     let storedThirdCartArray = JSON.parse(localStorage.getItem('thirdCartArray')) || [];

//     if (storedThirdCartArray.length > 0) {
//       // Update the cart with the first item in the stored array
//       updateThirdCart(storedThirdCartArray[0]);




//       // Assuming that the unique identifier for the third cart is stored in the 'planId' property
//       const selectedPlanId = storedThirdCartArray[0].value;

//       // Use a class or attribute that uniquely identifies your radio buttons
//       $(`input[name='plan'][value='${selectedPlanId}']`).prop('checked', true);
//       $(`input[name='plan'][value='${selectedPlanId}']`).closest(".radio_container").addClass("bg-[#0F56AF]/[0.3]");
//     }
//   });


//   // When an input with name "plan" is changed, update the third cart and local storage
//   $('input[name="plan"]').change(function () {
//     // Assuming you have some way to get the title, content, and imgSrc for the thirdCartArray
//     const title = 'Özel Sağlık Sigortası';
//     const content = $(this)
//       .closest(".plan_container")
//       .parent()
//       .find(".private_health_policy_value")
//       .text()
//       .trim();
//     const imgSrc = $(".heart-img").attr("src");

//     const selectedItemInfo = {
//       title: title,
//       content: content,
//       imgSrc: imgSrc,
//       value: $(this).val()
//     };

//     // Update local storage
//     localStorage.setItem('thirdCartArray', JSON.stringify([selectedItemInfo]));

//     // Update the cart
//     updateThirdCart(selectedItemInfo);
//   });
//   $(document).on("click", ".cart_close_btn", function () {
//     $(this).closest(".cart_item").remove();
//     localStorage.setItem('thirdCartArray', JSON.stringify([]));
//     $("input[name='plan']").prop('checked', false);
//     $(".radio-container").removeClass("bg-[#0F56AF]/[0.3]");
//     if( $("#cart_third_part")
//               .closest(".accordion_div")
//               .find(".cart_item").length === 0){
//                 $("#cart_third_part")
//               .closest(".accordion_div").find(".white-arrow").removeClass("rotate-180");
//               }
//               $("#cart_third_part_total")
//               .text("0.00 TL")


//   });

//   //**************************************************** */

//   // Function to update the fourth cart
//   function updateFourthCart(selectedItem) {
//     const cartContainer = $("#cart_fourth_part");

//     // Clear existing cart items
//     cartContainer.empty();

//     // Iterate over all selected items and add them to the cart
//     for (const item of selectedItem) {
//       const newDiv = $("<div>").addClass("border-b border-slate-300/[0.5] py-1 flex items-center justify-between cart_item");
//       const innerDiv = $("<div>");
//       const title = $("<p>").addClass("text-black text-[11px] font-semibold").text(item.title);
//       const content = $("<p>").addClass("text-[#5F5F5F] text-[11px]").text(`Net: 100,00 TL - Brüt ${item.content}`);
//       const image = $("<img>").attr("src", "images/close.png").attr("alt", "close").addClass("w-[10px] cursor-pointer cart_close_btn");

//       innerDiv.append(title, content);
//       newDiv.append(innerDiv, image);

//       cartContainer.append(newDiv);

//     }
//   }

//   $(document).ready(function () {
//     let storedFourthCartArray = JSON.parse(localStorage.getItem('fourthCartArray')) || [];

//     if (storedFourthCartArray.length > 0) {
//       // Update the cart with the stored array
//       updateFourthCart(storedFourthCartArray);


//     }
//   });
//   let sum=0;
//   // When an input with name "yourInputName" is changed, update the fourth cart and local storage
//   // Function to update the fourth cart
//   function updateFourthCart(selectedItems) {
//     const cartContainer = $("#cart_fourth_part");

//     // Clear existing cart items
//     cartContainer.empty();
//     sum=0
//     // Iterate over all selected items and add them to the cart
//     for (const selectedItem of selectedItems) {
//       const newDiv = $("<div>").addClass("border-b border-slate-300/[0.5] pb-1 flex items-center justify-between cart_item");
//       const innerDiv = $("<div>");
//       const title = $("<p>").addClass("text-black text-[11px] font-semibold").text(selectedItem.title);
//       const content = $("<p>").addClass("text-[#5F5F5F] text-[11px]").text(`Net: 100,00 TL - Brüt ${selectedItem.content} TL`);
//       const image = $("<img>").attr("src", "images/close.png").attr("alt", "close").addClass("w-[10px] cursor-pointer cart_close_btn");
//   sum+=parseFloat(selectedItem.content)
//   totalCash=totalCash-sum
//   $("#cart_fourth_part_total").text(`${sum.toFixed(2)} TL`)
//       innerDiv.append(title, content);
//       newDiv.append(innerDiv, image);

//       cartContainer.append(newDiv);

//     }
//   }

//   // When the update button is clicked, get the input value and update the fourth cart
//   $(".yan_hak_fırsatları_update_button").on("click", function () {
//    let inputValue ;
//    let title ;
//     if ($(window).width() >= 768) {
//             inputValue = $(this).parent().find(".yan_hak_input").val();
//             inputValue = Number(inputValue);
//             title = $(this).parent().parent().find(".yan_hak_tittle").text();
//             $(".yan_hak_input").val("")
//         } else {
//             inputValue = $(this).parent().find(".yan_hak_input_mobile").val();
//             inputValue = Number(inputValue);
//             title = $(this).parent().prev().find(".yan_hak_tittle").text();
//             $(".yan_hak_input_mobile").val("");
//         }
//     // Assuming your input has an ID "yourInputId"


//     // Assuming you have some way to get the title, content, and imgSrc for the fourthCartArray

//     const content = inputValue
//     const imgSrc = $(this).closest(".yan_hak_parent_div").find(".yan_hak_firsati_img").attr("src")

//     const selectedItemInfo = {
//       title: title,
//       content: content,
//       imgSrc: imgSrc,
//       value: inputValue
//     };


//     // Update local storage
//     let storedFourthCartArray = JSON.parse(localStorage.getItem('fourthCartArray')) || [];

//     // Check if an item with the same value already exists and update it
//     const existingItemIndex = storedFourthCartArray.findIndex(item => item.title === title);
//     if (existingItemIndex !== -1) {
//       storedFourthCartArray[existingItemIndex] = selectedItemInfo;
//     } else {
//       storedFourthCartArray.push(selectedItemInfo);
//     }

//     localStorage.setItem('fourthCartArray', JSON.stringify(storedFourthCartArray));

//     // Update the cart
//     updateFourthCart(storedFourthCartArray);
//     $(this).parent().find(".yan_hak_input").val("")
//   });

//   $(document).on("click", ".cart_close_btn", function () {
//     $(this).closest(".cart_item").remove();
//     localStorage.setItem('fourthCartArray', JSON.stringify([]));
//     if( $("#cart_fourth_part")
//               .closest(".accordion_div")
//               .find(".cart_item").length === 0){
//                 $("#cart_fourth_part")
//               .closest(".accordion_div").find(".white-arrow").removeClass("rotate-180");

//                 $("#cart_fourth_part_total")
//               .text("0.00 TL")

//               }
//   });



//   function calculateTotalContent(cartArray) {
//     let total = 0;
//     for (let i = 0; i < cartArray.length; i++) {
//       let contentValue = parseFloat(cartArray[i].content.replace(' TL', ''));

//       total += contentValue;
//     }
//     return total.toFixed(2);

//   }





//   /*************************************APROVAL */

//   // // Retrieve arrays from localStorage
//   // let storedSecondCartArray = JSON.parse(localStorage.getItem('secondCartArray')) || [];
//   // let storedThirdCartArray = JSON.parse(localStorage.getItem('thirdCartArray')) || [];
//   // let storedFourthCartArray = JSON.parse(localStorage.getItem('fourthCartArray')) || [];

//   // Concatenate the arrays
//   let combinedArr = storedSecondCartArray.concat(storedThirdCartArray);
//   // Assume combinedArr contains data like { title: 'İzin Hakkı', net: 100.00, brut: 166.67 }
//   $(document).ready(function() {
//     let totalContent=0;
//     for (let i = 0; i < combinedArr.length; i++) {
//       let item = combinedArr[i];

//       // Create a new div element with jQuery
//       let newDiv = $('<div>', {
//         class: 'flex justify-start items-center gap-3 border-b border-slate-300/[0.5] pt-3 pb-5 px-2 sm:px-5'
//       });

//       // Create an img element with jQuery
//       let img = $('<img>', {
//         src: item.imgSrc,
//         alt: 'sail',
//         class: 'w-[46px] h-[46px] object-cover'
//       });

//       // Create a div element for text content with jQuery
//       let textDiv = $('<div>', {
//         class: 'w-full'
//       });

//       // Create a paragraph element for the title with jQuery
//       let titleParagraph = $('<p>', {
//         class: 'text-[#000] font-semibold text-[15px] md:text-[16px]',
//         text: item.title
//       });

//       // Create a paragraph element for the net and brut values with jQuery
//       let valueParagraph = $('<p>', {
//         class: 'text-[14px] text-[#5F5F5F]',
//         text: `Net: 100.00 TL - Brüt: ${item.content}`
//       });
//       totalContent+=parseFloat(item.content);
//       // coitem.content)
//       // Append the img and text elements to the newDiv
//       textDiv.append(titleParagraph, valueParagraph);
//       newDiv.append(img, textDiv);

//       // Append the newDiv to the #fourthCartArray div using jQuery
//       $('#secondCartArrayDiv').append(newDiv);

//       // Create a copy of the newDiv (if you want identical elements in both divs)
//       let newDiv2 = newDiv.clone();

//       // Append the newDiv (or its copy) to the #fourthCartArrayDiv2 using jQuery
//       $('#secondCartArrayDiv2').append(newDiv2);
//       $('#second_content_total').text(totalContent.toFixed(2) +" TL");
//       $('#second_content_total2').text(totalContent.toFixed(2) +" TL");
//     }
//   });


//   //******************** */
//   $(document).ready(function() {
//     let totalContent=0;
//     for (let i = 0; i < storedFourthCartArray.length; i++) {
//       let item = storedFourthCartArray[i];

//       // Create a new div element with jQuery
//       let newDiv = $('<div>', {
//         class: 'flex justify-start items-center gap-3 border-b border-slate-300/[0.5] pt-3 pb-5 px-2 sm:px-5'
//       });

//       // Create an img element with jQuery
//       let img = $('<img>', {
//         src: item.imgSrc,
//         alt: 'sail',
//         class: 'w-[46px] h-[46px] object-cover'
//       });

//       // Create a div element for text content with jQuery
//       let textDiv = $('<div>', {
//         class: 'w-full'
//       });

//       // Create a paragraph element for the title with jQuery
//       let titleParagraph = $('<p>', {
//         class: 'text-[#000] font-semibold text-[15px] md:text-[16px]',
//         text: item.title
//       });

//       // Create a paragraph element for the net and brut values with jQuery
//       let valueParagraph = $('<p>', {
//         class: 'text-[14px] text-[#5F5F5F]',
//         text: `Net: 100.00 TL - Brüt: ${item.content} TL`
//       });
//       totalContent+=item.content;


//       // Append the img and text elements to the newDiv
//       textDiv.append(titleParagraph, valueParagraph);
//       newDiv.append(img, textDiv);

//       // Append the newDiv to the #fourthCartArray div using jQuery
//       $('#fourthCartArrayDiv').append(newDiv);

//       // Create a copy of the newDiv (if you want identical elements in both divs)
//       let newDiv2 = newDiv.clone();

//       // Append the newDiv (or its copy) to the #fourthCartArrayDiv2 using jQuery
//       $('#fourthCartArrayDiv2').append(newDiv2);
//       $('#fourth_content_total').text(totalContent.toFixed(2) +" TL");
//       $('#fourth_content_total2').text(totalContent.toFixed(2) +" TL");
//     }
//   });




//   // Sepeti sıfırla butonuna basıldığında çalışacak fonksiyon
//   function resetCart() {
//     // Tüm array'leri sıfırla
//     storedSecondCartArray = [];
//     storedThirdCartArray = [];
//     storedFourthCartArray = [];

//     // localStorage'ı güncelle
//     localStorage.setItem('secondCartArray', JSON.stringify(storedSecondCartArray));
//     localStorage.setItem('thirdCartArray', JSON.stringify(storedThirdCartArray));
//     localStorage.setItem('fourthCartArray', JSON.stringify(storedFourthCartArray));
//     $(".cart_item").remove();




//     // İlgili DOM elemanlarını güncelle (örneğin, sepetin görüntüsünü temizle)
//     updateSecondCart([]);  // Burada updateSecondCart fonksiyonunu kendi ihtiyaçlarınıza göre çağırın
//     // Diğer güncellemeleri de yapabilirsiniz

//     // Diğer işlemleri ekleyebilirsiniz (örneğin, toplam tutarı sıfırla)
//     $("#balance").text((12551.67-166.67).toFixed(2) + " TL");
//     window.location.reload()
//   }

//   // Örnek olarak bir HTML butonu olsun:
//   // <button id="resetCartButton">Sepeti Sıfırla</button>
//   // Butona tıklanınca resetCart fonksiyonunu çağırıyoruz.
//   $("#resetCartButton").on("click", resetCart);




//   // function calculateTotalContentForAllArrays() {

//   //   // Tüm array'leri birleştir
//   //   let allArrays = storedSecondCartArray.concat(storedThirdCartArray, storedFourthCartArray);

//   //   // Toplam değeri hesapla
//   //   let totalContent = allArrays.reduce((total, item) => {
//   //     // Her bir öğenin content özelliğini parseFloat ile al ve toplama ekle
//   //     let contentValue = parseFloat(String(item.content).replace(' TL', ''));
//   //     return total + contentValue;
//   //   }, 0);

//   //   return totalContent.toFixed(2) + " TL";
//   // }
//   // console.log($("#cart_second_part_total").text())
//   // console.log($("#cart_third_part_total").text())
//   // console.log($("#cart_fourth_part_total").text())
//   // $(document).ready(function() {
//   //   totalCash =
//   //   Number((
//   //     parseFloat($("#cart_second_part_total").text().replace(" TL", "")) +
//   //     parseFloat($("#cart_third_part_total").text().replace(" TL", "")) +
//   //     parseFloat($("#cart_fourth_part_total").text().replace(" TL", ""))
//   //   ).toFixed(2));

//   // console.log(parseFloat($("#cart_second_part_total").text().replace(" TL", "")));



//   // });

//   // console.log(totalCash)
//   $("#balance").text((totalCash).toFixed(2)+ " TL")









  $(document).ready(function() {
    $('#kvvk').change(function() {
      $("#popup_overlay").toggleClass("flex", this.checked).toggleClass("hidden", !this.checked);
    });

    $("#kvkk_close_btn").on("click", function() {
      $("#popup_overlay").addClass("hidden").removeClass("flex");
    });
  });


  $(document).ready(function() {
    $('#consent_cb').change(function() {
      $("#consent_overlay").toggleClass("flex", this.checked).toggleClass("hidden", !this.checked);
    });

    $("#consent_close_btn").on("click", function() {
      $("#consent_overlay").addClass("hidden").removeClass("flex");
    });
  });


