/*
! tailwindcss v3.3.6 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
    box-sizing: border-box;
    /* 1 */
    border-width: 0;
    /* 2 */
    border-style: solid;
    /* 2 */
    border-color: #e5e7eb;
    /* 2 */
}

::before,
::after {
    --tw-content: "";
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
*/

html {
    line-height: 1.5;
    /* 1 */
    -webkit-text-size-adjust: 100%;
    /* 2 */
    -moz-tab-size: 4;
    /* 3 */
    -o-tab-size: 4;
    tab-size: 4;
    /* 3 */
    font-family: Figtree, ui-sans-serif, system-ui, -apple-system,
        BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
        "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
        "Segoe UI Symbol", "Noto Color Emoji";
    /* 4 */
    font-feature-settings: normal;
    /* 5 */
    font-variation-settings: normal;
    /* 6 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
    margin: 0;
    /* 1 */
    line-height: inherit;
    /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
    height: 0;
    /* 1 */
    color: inherit;
    /* 2 */
    border-top-width: 1px;
    /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
    color: inherit;
    text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
    font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
        "Liberation Mono", "Courier New", monospace;
    /* 1 */
    font-feature-settings: normal;
    /* 2 */
    font-variation-settings: normal;
    /* 3 */
    font-size: 1em;
    /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
    font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sub {
    bottom: -0.25em;
}

sup {
    top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
    text-indent: 0;
    /* 1 */
    border-color: inherit;
    /* 2 */
    border-collapse: collapse;
    /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
    font-family: inherit;
    /* 1 */
    font-feature-settings: inherit;
    /* 1 */
    font-variation-settings: inherit;
    /* 1 */
    font-size: 100%;
    /* 1 */
    font-weight: inherit;
    /* 1 */
    line-height: inherit;
    /* 1 */
    color: inherit;
    /* 1 */
    margin: 0;
    /* 2 */
    padding: 0;
    /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
    text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
[type="button"],
[type="reset"],
[type="submit"] {
    -webkit-appearance: button;
    /* 1 */
    background-color: transparent;
    /* 2 */
    background-image: none;
    /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
    outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
    box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
    vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type="search"] {
    -webkit-appearance: textfield;
    /* 1 */
    outline-offset: -2px;
    /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
    -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
    -webkit-appearance: button;
    /* 1 */
    font: inherit;
    /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
    display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
    margin: 0;
}

fieldset {
    margin: 0;
    padding: 0;
}

legend {
    padding: 0;
}

ol,
ul,
menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
    padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
    resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder,
textarea::-moz-placeholder {
    opacity: 1;
    /* 1 */
    color: #9ca3af;
    /* 2 */
}

input::placeholder,
textarea::placeholder {
    opacity: 1;
    /* 1 */
    color: #9ca3af;
    /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
    cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
    cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
    display: block;
    /* 1 */
    vertical-align: middle;
    /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
    max-width: 100%;
    height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
    display: none;
}

[type="text"],
[type="email"],
[type="url"],
[type="password"],
[type="number"],
[type="date"],
[type="datetime-local"],
[type="month"],
[type="search"],
[type="tel"],
[type="time"],
[type="week"],
[multiple],
textarea,
select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border-color: #6b7280;
    border-width: 1px;
    border-radius: 0px;
    padding-top: 0.5rem;
    padding-right: 0.75rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
    --tw-shadow: 0 0 #0000;
}

[type="text"]:focus,
[type="email"]:focus,
[type="url"]:focus,
[type="password"]:focus,
[type="number"]:focus,
[type="date"]:focus,
[type="datetime-local"]:focus,
[type="month"]:focus,
[type="search"]:focus,
[type="tel"]:focus,
[type="time"]:focus,
[type="week"]:focus,
[multiple]:focus,
textarea:focus,
select:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-inset: var(--tw-empty, /*!*/ /*!*/);
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #2563eb;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow);
    border-color: #2563eb;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    color: #6b7280;
    opacity: 1;
}

input::placeholder,
textarea::placeholder {
    color: #6b7280;
    opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
}

::-webkit-date-and-time-value {
    min-height: 1.5em;
}

::-webkit-datetime-edit,
::-webkit-datetime-edit-year-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-minute-field,
::-webkit-datetime-edit-second-field,
::-webkit-datetime-edit-millisecond-field,
::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
}

select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}

[multiple] {
    background-image: initial;
    background-position: initial;
    background-repeat: unset;
    background-size: initial;
    padding-right: 0.75rem;
    -webkit-print-color-adjust: unset;
    print-color-adjust: unset;
}

[type="checkbox"],
[type="radio"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding: 0;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    display: inline-block;
    vertical-align: middle;
    background-origin: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    flex-shrink: 0;
    height: 1rem;
    width: 1rem;
    color: #2563eb;
    background-color: #fff;
    border-color: #6b7280;
    border-width: 1px;
    --tw-shadow: 0 0 #0000;
}

[type="checkbox"] {
    border-radius: 0px;
}

[type="radio"] {
    border-radius: 100%;
}

[type="checkbox"]:focus,
[type="radio"]:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-inset: var(--tw-empty, /*!*/ /*!*/);
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #2563eb;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow);
}

[type="checkbox"]:checked,
[type="radio"]:checked {
    border-color: transparent;
    background-color: currentColor;
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

[type="checkbox"]:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

[type="radio"]:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

[type="checkbox"]:checked:hover,
[type="checkbox"]:checked:focus,
[type="radio"]:checked:hover,
[type="radio"]:checked:focus {
    border-color: transparent;
    background-color: currentColor;
}

[type="checkbox"]:indeterminate {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
    border-color: transparent;
    background-color: currentColor;
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

[type="checkbox"]:indeterminate:hover,
[type="checkbox"]:indeterminate:focus {
    border-color: transparent;
    background-color: currentColor;
}

[type="file"] {
    background: unset;
    border-color: inherit;
    border-width: 0;
    border-radius: 0;
    padding: 0;
    font-size: unset;
    line-height: inherit;
}

[type="file"]:focus {
    outline: 1px solid ButtonText;
    outline: 1px auto -webkit-focus-ring-color;
}

*,
::before,
::after {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
}

.container {
    width: 100%;
}

@media (min-width: 640px) {
    .container {
        max-width: 640px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 768px;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 1024px;
    }
}

@media (min-width: 1280px) {
    .container {
        max-width: 1280px;
    }
}

@media (min-width: 1536px) {
    .container {
        max-width: 1536px;
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

.pointer-events-none {
    pointer-events: none;
}

.visible {
    visibility: visible;
}

.invisible {
    visibility: hidden;
}

.collapse {
    visibility: collapse;
}

.static {
    position: static;
}

.fixed {
    position: fixed;
}

.absolute {
    position: absolute;
}

.relative {
    position: relative;
}

.sticky {
    position: sticky;
}

.inset-0 {
    inset: 0px;
}

.bottom-0 {
    bottom: 0px;
}

.bottom-1 {
    bottom: 0.25rem;
}

.bottom-3 {
    bottom: 0.75rem;
}

.bottom-\[-12px\] {
    bottom: -12px;
}

.bottom-\[-18px\] {
    bottom: -18px;
}

.bottom-\[25\%\] {
    bottom: 25%;
}

.bottom-\[30\%\] {
    bottom: 30%;
}

.bottom-\[60px\] {
    bottom: 60px;
}

.end-1 {
    inset-inline-end: 0.25rem;
}

.left-0 {
    left: 0px;
}

.left-1 {
    left: 0.25rem;
}

.left-1\/2 {
    left: 50%;
}

.left-2 {
    left: 0.5rem;
}

.left-5 {
    left: 1.25rem;
}

.left-\[-15px\] {
    left: -15px;
}

.left-\[10\%\] {
    left: 10%;
}

.left-\[50\%\] {
    left: 50%;
}

.right-0 {
    right: 0px;
}

.right-3 {
    right: 0.75rem;
}

.right-8 {
    right: 2rem;
}

.right-\[-100\%\] {
    right: -100%;
}

.right-\[-15px\] {
    right: -15px;
}

.right-\[-25px\] {
    right: -25px;
}

.right-\[10px\] {
    right: 10px;
}

.right-\[15px\] {
    right: 15px;
}

.start-1 {
    inset-inline-start: 0.25rem;
}

.top-0 {
    top: 0px;
}

.top-1\/2 {
    top: 50%;
}

.top-2 {
    top: 0.5rem;
}

.top-3 {
    top: 0.75rem;
}

.top-5 {
    top: 1.25rem;
}

.top-\[15px\] {
    top: 15px;
}

.top-\[18px\] {
    top: 18px;
}

.top-\[50\%\] {
    top: 50%;
}

.top-\[6rem\] {
    top: 6rem;
}

.top-\[8rem\] {
    top: 8rem;
}

.z-0 {
    z-index: 0;
}

.z-10 {
    z-index: 10;
}

.z-20 {
    z-index: 20;
}

.z-30 {
    z-index: 30;
}

.z-40 {
    z-index: 40;
}

.z-50 {
    z-index: 50;
}

.z-\[10000\] {
    z-index: 10000;
}

.z-\[1000\] {
    z-index: 1000;
}

.z-\[1\] {
    z-index: 1;
}

.z-\[9\] {
    z-index: 9;
}

.m-1 {
    margin: 0.25rem;
}

.m-2 {
    margin: 0.5rem;
}

.m-20 {
    margin: 5rem;
}

.m-3 {
    margin: 0.75rem;
}

.m-4 {
    margin: 1rem;
}

.m-auto {
    margin: auto;
}

.mx-6 {
    margin-left: 1.5rem;
    margin-right: 1.5rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.-ml-px {
    margin-left: -1px;
}

.-mr-0 {
    margin-right: -0px;
}

.-mr-0\.5 {
    margin-right: -0.125rem;
}

.-mr-2 {
    margin-right: -0.5rem;
}

.-mt-px {
    margin-top: -1px;
}

.mb-0 {
    margin-bottom: 0px;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-5 {
    margin-bottom: 1.25rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.ml-3 {
    margin-left: 0.75rem;
}

.ml-4 {
    margin-left: 1rem;
}

.ml-6 {
    margin-left: 1.5rem;
}

.ml-auto {
    margin-left: auto;
}

.mr-1 {
    margin-right: 0.25rem;
}

.mr-1\.5 {
    margin-right: 0.375rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.mr-3 {
    margin-right: 0.75rem;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-16 {
    margin-top: 4rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 0.75rem;
}

.mt-4 {
    margin-top: 1rem;
}

.mt-5 {
    margin-top: 1.25rem;
}

.mt-6 {
    margin-top: 1.5rem;
}

.mt-8 {
    margin-top: 2rem;
}

.mt-\[-2px\] {
    margin-top: -2px;
}

.mt-\[-8px\] {
    margin-top: -8px;
}

.block {
    display: block;
}

.inline-block {
    display: inline-block;
}

.inline {
    display: inline;
}

.flex {
    display: flex;
}

.inline-flex {
    display: inline-flex;
}

.table {
    display: table;
}

.inline-table {
    display: inline-table;
}

.table-caption {
    display: table-caption;
}

.table-cell {
    display: table-cell;
}

.table-column {
    display: table-column;
}

.table-column-group {
    display: table-column-group;
}

.table-footer-group {
    display: table-footer-group;
}

.table-header-group {
    display: table-header-group;
}

.table-row-group {
    display: table-row-group;
}

.table-row {
    display: table-row;
}

.grid {
    display: grid;
}

.inline-grid {
    display: inline-grid;
}

.contents {
    display: contents;
}

.list-item {
    display: list-item;
}

.hidden {
    display: none;
}

.h-1 {
    height: 0.25rem;
}

.h-10 {
    height: 2.5rem;
}

.h-16 {
    height: 4rem;
}

.h-20 {
    height: 5rem;
}

.h-4 {
    height: 1rem;
}

.h-5 {
    height: 1.25rem;
}

.h-6 {
    height: 1.5rem;
}

.h-7 {
    height: 1.75rem;
}

.h-72 {
    height: 18rem;
}

.h-9 {
    height: 2.25rem;
}

.h-\[100vh\] {
    height: 100vh;
}

.h-\[15px\] {
    height: 15px;
}

.h-\[16px\] {
    height: 16px;
}

.h-\[17px\] {
    height: 17px;
}

.h-\[18px\] {
    height: 18px;
}

.h-\[23px\] {
    height: 23px;
}

.h-\[30px\] {
    height: 30px;
}

.h-\[37px\] {
    height: 37px;
}

.h-\[39px\] {
    height: 39px;
}

.h-\[40\.44px\] {
    height: 40.44px;
}

.h-\[415px\] {
    height: 415px;
}

.h-\[430px\] {
    height: 430px;
}

.h-\[46px\] {
    height: 46px;
}

.h-\[4px\] {
    height: 4px;
}

.h-\[500px\] {
    height: 500px;
}

.h-\[50px\] {
    height: 50px;
}

.h-\[600px\] {
    height: 600px;
}

.h-\[70px\] {
    height: 70px;
}

.h-\[82\%\] {
    height: 82%;
}

.h-\[90px\] {
    height: 90px;
}

.h-\[calc\(100\%-1rem\)\] {
    height: calc(100% - 1rem);
}

.h-fit {
    height: -moz-fit-content;
    height: fit-content;
}

.h-full {
    height: 100%;
}

.max-h-0 {
    max-height: 0px;
}

.max-h-\[400px\] {
    max-height: 400px;
}

.max-h-\[500px\] {
    max-height: 500px;
}

.max-h-\[540px\] {
    max-height: 540px;
}

.max-h-\[550px\] {
    max-height: 550px;
}

.max-h-\[570px\] {
    max-height: 570px;
}

.max-h-fit {
    max-height: -moz-fit-content;
    max-height: fit-content;
}

.max-h-full {
    max-height: 100%;
}

.min-h-screen {
    min-height: 100vh;
}

.w-1\/2 {
    width: 50%;
}

.w-10 {
    width: 2.5rem;
}

.w-16 {
    width: 4rem;
}

.w-20 {
    width: 5rem;
}

.w-4 {
    width: 1rem;
}

.w-48 {
    width: 12rem;
}

.w-5 {
    width: 1.25rem;
}

.w-6 {
    width: 1.5rem;
}

.w-6\/12 {
    width: 50%;
}

.w-64 {
    width: 16rem;
}

.w-7 {
    width: 1.75rem;
}

.w-\[100\%\] {
    width: 100%;
}
.w-\[35px\] {
    width: 35px;
}
.w-\[135px\] {
    width: 135px;
}
.w-\[90px\] {
    width: 90px;
}

.w-\[100px\] {
    width: 100px;
}

.w-\[104px\] {
    width: 104px;
}

.w-\[10px\] {
    width: 10px;
}

.w-\[11px\] {
    width: 11px;
}
.w-\[90px\] {
    width: 90px;
}

.w-\[130px\] {
    width: 130px;
}

.w-\[13px\] {
    width: 13px;
}

.w-\[14px\] {
    width: 14px;
}

.w-\[15px\] {
    width: 15px;
}

.w-\[16px\] {
    width: 16px;
}

.w-\[17px\] {
    width: 17px;
}

.w-\[18px\] {
    width: 18px;
}

.w-\[23px\] {
    width: 23px;
}

.w-\[27px\] {
    width: 27px;
}

.w-\[329\.14px\] {
    width: 329.14px;
}

.w-\[380px\] {
    width: 380px;
}

.w-\[46px\] {
    width: 46px;
}

.w-\[50px\] {
    width: 50px;
}

.w-\[70px\] {
    width: 70px;
}

.w-\[80px\] {
    width: 80px;
}

.w-auto {
    width: auto;
}

.w-full {
    width: 100%;
}

.max-w-7xl {
    max-width: 80rem;
}

.max-w-\[11px\] {
    max-width: 11px;
}

.max-w-\[121px\] {
    max-width: 121px;
}

.max-w-\[125px\] {
    max-width: 125px;.
}

.max-w-\[156px\] {
    max-width: 156px;
}

.max-w-\[167px\] {
    max-width: 167px;
}

.max-w-\[175px\] {
    max-width: 175px;
}

.max-w-\[195px\] {
    max-width: 195px;
}

.max-w-\[219px\] {
    max-width: 219px;
}
.max-w-\[210px\] {
    max-width: 210px;
}

.max-w-\[220px\] {
    max-width: 220px;
}

.max-w-\[250px\] {
    max-width: 250px;
}

.max-w-\[25px\] {
    max-width: 25px;
}

.max-w-\[270px\] {
    max-width: 270px;
}

.max-w-\[300px\] {
    max-width: 300px;
}

.max-w-\[305px\] {
    max-width: 305px;
}

.max-w-\[310px\] {
    max-width: 310px;
}

.max-w-\[323px\] {
    max-width: 323px;
}

.max-w-\[324px\] {
    max-width: 324px;
}

.max-w-\[33px\] {
    max-width: 33px;
}

.max-w-\[372px\] {
    max-width: 372px;
}

.max-w-\[380px\] {
    max-width: 380px;
}

.max-w-\[400px\] {
    max-width: 400px;
}

.max-w-\[410px\] {
    max-width: 410px;
}

.max-w-\[420px\] {
    max-width: 420px;
}

.max-w-\[430px\] {
    max-width: 430px;
}

.max-w-\[457px\] {
    max-width: 457px;
}

.max-w-\[500px\] {
    max-width: 500px;
}

.max-w-\[50px\] {
    max-width: 50px;
}

.max-w-\[550px\] {
    max-width: 550px;
}

.max-w-\[582px\] {
    max-width: 582px;
}

.max-w-\[610px\] {
    max-width: 610px;
}

.max-w-\[630px\] {
    max-width: 630px;
}

.max-w-\[679px\] {
    max-width: 679px;
}

.max-w-\[680px\] {
    max-width: 680px;
}

.max-w-\[683px\] {
    max-width: 683px;
}

.max-w-\[721px\] {
    max-width: 721px;
}

.max-w-\[725px\] {
    max-width: 725px;
}

.max-w-\[80px\] {
    max-width: 80px;
}

.max-w-\[85px\] {
    max-width: 85px;
}

.max-w-\[90px\] {
    max-width: 90px;
}

.max-w-\[960px\] {
    max-width: 960px;
}

.max-w-xl {
    max-width: 36rem;
}

.flex-1 {
    flex: 1 1 0%;
}

.flex-shrink {
    flex-shrink: 1;
}

.shrink {
    flex-shrink: 1;
}

.shrink-0 {
    flex-shrink: 0;
}

.flex-grow {
    flex-grow: 1;
}

.grow {
    flex-grow: 1;
}

.border-collapse {
    border-collapse: collapse;
}

.origin-top {
    transform-origin: top;
}

.origin-top-left {
    transform-origin: top left;
}

.origin-top-right {
    transform-origin: top right;
}

.-translate-x-1\/2 {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full {
    --tw-translate-x: -100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-full {
    --tw-translate-y: -100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-15px\] {
    --tw-translate-x: -15px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-16px\] {
    --tw-translate-x: -16px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
    --tw-translate-x: 100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4 {
    --tw-translate-y: 1rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-full {
    --tw-translate-y: 100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-0 {
    --tw-rotate: 0deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
    --tw-rotate: 180deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90 {
    --tw-rotate: 90deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-0 {
    --tw-scale-x: 0;
    --tw-scale-y: 0;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95 {
    --tw-scale-x: 0.95;
    --tw-scale-y: 0.95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-none {
    transform: none;
}

.cursor-default {
    cursor: default;
}

.cursor-not-allowed {
    cursor: not-allowed;
}

.cursor-pointer {
    cursor: pointer;
}

.resize-none {
    resize: none;
}

.resize {
    resize: both;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
}

.flex-col {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.place-items-center {
    place-items: center;
}

.items-start {
    align-items: flex-start;
}

.items-end {
    align-items: flex-end;
}

.items-center {
    align-items: center;
}

.justify-start {
    justify-content: flex-start;
}

.justify-end {
    justify-content: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-items-center {
    justify-items: center;
}
.gap-1 {
    gap: 0.25rem;
}
.gap-2 {
    gap: 0.5rem;
}

.gap-3 {
    gap: 0.75rem;
}

.gap-4 {
    gap: 1rem;
}

.gap-5 {
    gap: 1.25rem;
}

.gap-6 {
    gap: 1.5rem;
}

.gap-\[2px\] {
    gap: 2px;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.self-start {
    align-self: flex-start;
}

.self-end {
    align-self: flex-end;
}

.self-center {
    align-self: center;
}

.overflow-hidden {
    overflow: hidden;
}

.overflow-y-auto {
    overflow-y: auto;
}

.overflow-x-hidden {
    overflow-x: hidden;
}

.overflow-y-scroll {
    overflow-y: scroll;
}

.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.break-all {
    word-break: break-all;
}

.rounded {
    border-radius: 0.25rem;
}

.rounded-\[10px\] {
    border-radius: 10px;
}

.rounded-\[20px\] {
    border-radius: 20px;
}

.rounded-\[3px\] {
    border-radius: 3px;
}

.rounded-\[5px\] {
    border-radius: 5px;
}

.rounded-full {
    border-radius: 9999px;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.rounded-md {
    border-radius: 0.375rem;
}

.rounded-sm {
    border-radius: 0.125rem;
}

.rounded-xl {
    border-radius: 0.75rem;
}

.rounded-b {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.rounded-l-\[10px\] {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

.rounded-l-lg {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
}

.rounded-l-md {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.rounded-r-lg {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}

.rounded-r-md {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.rounded-t {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
}

.rounded-bl-\[10px\] {
    border-bottom-left-radius: 10px;
}

.rounded-br-\[10px\] {
    border-bottom-right-radius: 10px;
}

.rounded-tl-\[10px\] {
    border-top-left-radius: 10px;
}

.rounded-tr-\[10px\] {
    border-top-right-radius: 10px;
}

.border {
    border-width: 1px;
}

.border-0 {
    border-width: 0px;
}

.border-2 {
    border-width: 2px;
}

.border-b {
    border-bottom-width: 1px;
}

.border-b-2 {
    border-bottom-width: 2px;
}

.border-l-4 {
    border-left-width: 4px;
}

.border-t {
    border-top-width: 1px;
}

.border-none {
    border-style: none;
}

.border-\[\#0F56AF\] {
    --tw-border-opacity: 1;
    border-color: rgb(15 86 175 / var(--tw-border-opacity));
}

.border-\[\#7F35B2\] {
    --tw-border-opacity: 1;
    border-color: rgb(127 53 178 / var(--tw-border-opacity));
}

.border-\[\#E4E6E8\] {
    --tw-border-opacity: 1;
    border-color: rgb(228 230 232 / var(--tw-border-opacity));
}

.border-blue-600 {
    --tw-border-opacity: 1;
    border-color: rgb(37 99 235 / var(--tw-border-opacity));
}

.border-blue-700 {
    --tw-border-opacity: 1;
    border-color: rgb(29 78 216 / var(--tw-border-opacity));
}

.border-gray-100 {
    --tw-border-opacity: 1;
    border-color: rgb(243 244 246 / var(--tw-border-opacity));
}

.border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-border-opacity));
}

.border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.border-indigo-400 {
    --tw-border-opacity: 1;
    border-color: rgb(129 140 248 / var(--tw-border-opacity));
}

.border-slate-300\/\[0\.3\] {
    border-color: rgb(203 213 225 / 0.3);
}

.border-slate-300\/\[0\.5\] {
    border-color: rgb(203 213 225 / 0.5);
}

.border-slate-500 {
    --tw-border-opacity: 1;
    border-color: rgb(100 116 139 / var(--tw-border-opacity));
}

.border-transparent {
    border-color: transparent;
}

.border-white {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.bg-\[\#0F56AF\]\/\[0\.3\] {
    background-color: rgb(15 86 175 / 0.3);
}

.bg-\[\#48106D\]\/\[0\.04\] {
    background-color: rgb(72 16 109 / 0.04);
}

.bg-\[\#48106D\]\/\[0\.1\] {
    background-color: rgb(72 16 109 / 0.1);
}

.bg-\[\#585858\]\/\[0\.1\] {
    background-color: rgb(88 88 88 / 0.1);
}

.bg-\[\#707070\] {
    --tw-bg-opacity: 1;
    background-color: rgb(112 112 112 / var(--tw-bg-opacity));
}

.bg-\[\#707070\]\/\[0\.3\] {
    background-color: rgb(112 112 112 / 0.3);
}

.bg-\[\#7F35B2\] {
    --tw-bg-opacity: 1;
    background-color: rgb(127 53 178 / var(--tw-bg-opacity));
}

.bg-\[\#7F35B2\]\/\[0\.9\] {
    background-color: rgb(127 53 178 / 0.9);
}

.bg-\[\#7f35b2\]\/\[0\.1\] {
    background-color: rgb(127 53 178 / 0.1);
}

.bg-\[\#F6F4F8\] {
    --tw-bg-opacity: 1;
    background-color: rgb(246 244 248 / var(--tw-bg-opacity));
}

.bg-\[\#F7F5F9\] {
    --tw-bg-opacity: 1;
    background-color: rgb(247 245 249 / var(--tw-bg-opacity));
}

.bg-\[\#FBFAFC\] {
    --tw-bg-opacity: 1;
    background-color: rgb(251 250 252 / var(--tw-bg-opacity));
}

.bg-\[\#fff\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-\[rgba\(0\2c 0\2c 0\2c 0\.5\)\] {
    background-color: rgba(0, 0, 0, 0.5);
}

.bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-blue-700 {
    --tw-bg-opacity: 1;
    background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.bg-gray-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.bg-gray-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.bg-gray-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.bg-gray-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.bg-gray-900\/50 {
    background-color: rgb(17 24 39 / 0.5);
}

.bg-indigo-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(238 242 255 / var(--tw-bg-opacity));
}

.bg-red-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 242 242 / var(--tw-bg-opacity));
}

.bg-red-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}

.bg-transparent {
    background-color: transparent;
}

.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-white\/50 {
    background-color: rgb(255 255 255 / 0.5);
}

.bg-opacity-50 {
    --tw-bg-opacity: 0.5;
}

.from-gray-700\/50 {
    --tw-gradient-from: rgb(55 65 81 / 0.5) var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(55 65 81 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-transparent {
    --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from),
        transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.bg-center {
    background-position: center;
}

.fill-current {
    fill: currentColor;
}

.stroke-gray-400 {
    stroke: #9ca3af;
}

.stroke-red-500 {
    stroke: #ef4444;
}

.object-contain {
    -o-object-fit: contain;
    object-fit: contain;
}

.object-cover {
    -o-object-fit: cover;
    object-fit: cover;
}

.p-1 {
    padding: 0.25rem;
}

.p-2 {
    padding: 0.5rem;
}

.p-2\.5 {
    padding: 0.625rem;
}

.p-3 {
    padding: 0.75rem;
}

.p-4 {
    padding: 1rem;
}

.p-5 {
    padding: 1.25rem;
}

.p-6 {
    padding: 1.5rem;
}

.p-\[10px\] {
    padding: 10px;
}

.p-\[1rem\] {
    padding: 1rem;
}

.px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
}

.px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}

.px-\[1rem\] {
    padding-left: 1rem;
    padding-right: 1rem;
}

.px-\[2rem\] {
    padding-left: 2rem;
    padding-right: 2rem;
}

.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.py-2\.5 {
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
}

.py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
}

.py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}

.py-7 {
    padding-top: 1.75rem;
    padding-bottom: 1.75rem;
}

.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.py-9 {
    padding-top: 2.25rem;
    padding-bottom: 2.25rem;
}

.py-\[1px\] {
    padding-top: 1px;
    padding-bottom: 1px;
}

.py-\[1rem\] {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.py-\[4px\] {
    padding-top: 4px;
    padding-bottom: 4px;
}

.py-\[5rem\] {
    padding-top: 5rem;
    padding-bottom: 5rem;
}

.py-\[6px\] {
    padding-top: 6px;
    padding-bottom: 6px;
}

.pb-1 {
    padding-bottom: 0.25rem;
}

.pb-2 {
    padding-bottom: 0.5rem;
}

.pb-3 {
    padding-bottom: 0.75rem;
}

.pb-4 {
    padding-bottom: 1rem;
}

.pb-5 {
    padding-bottom: 1.25rem;
}

.pb-7 {
    padding-bottom: 1.75rem;
}

.pb-8 {
    padding-bottom: 2rem;
}

.pb-\[5rem\] {
    padding-bottom: 5rem;
}

.pl-2 {
    padding-left: 0.5rem;
}

.pl-3 {
    padding-left: 0.75rem;
}

.pl-4 {
    padding-left: 1rem;
}

.pl-8 {
    padding-left: 2rem;
}

.pl-\[2px\] {
    padding-left: 2px;
}

.pr-4 {
    padding-right: 1rem;
}

.pt-1 {
    padding-top: 0.25rem;
}

.pt-12 {
    padding-top: 3rem;
}

.pt-2 {
    padding-top: 0.5rem;
}

.pt-3 {
    padding-top: 0.75rem;
}

.pt-4 {
    padding-top: 1rem;
}

.pt-5 {
    padding-top: 1.25rem;
}

.pt-6 {
    padding-top: 1.5rem;
}

.pt-7 {
    padding-top: 1.75rem;
}

.pt-8 {
    padding-top: 2rem;
}

.pt-\[10rem\] {
    padding-top: 10rem;
}

.pt-\[2px\] {
    padding-top: 2px;
}

.pt-\[30px\] {
    padding-top: 30px;
}

.pt-\[3rem\] {
    padding-top: 3rem;
}

.text-left {
    text-align: left;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-justify {
    text-align: justify;
}

.font-sans {
    font-family: Figtree, ui-sans-serif, system-ui, -apple-system,
        BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
        "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
        "Segoe UI Symbol", "Noto Color Emoji";
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}

.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
}

.text-\[10px\] {
    font-size: 10px;
}

.text-\[11px\] {
    font-size: 11px;
}

.text-\[12px\] {
    font-size: 12px;
}

.text-\[13px\] {
    font-size: 13px;
}

.text-\[14px\] {
    font-size: 14px;
}

.text-\[15px\] {
    font-size: 15px;
}

.text-\[16px\] {
    font-size: 16px;
}

.text-\[18px\] {
    font-size: 18px;
}

.text-\[20px\] {
    font-size: 20px;
}

.text-\[22px\] {
    font-size: 22px;
}

.text-\[26px\] {
    font-size: 26px;
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}

.font-bold {
    font-weight: 700;
}

.font-medium {
    font-weight: 500;
}

.font-normal {
    font-weight: 400;
}

.font-semibold {
    font-weight: 600;
}

.uppercase {
    text-transform: uppercase;
}

.lowercase {
    text-transform: lowercase;
}

.capitalize {
    text-transform: capitalize;
}

.italic {
    font-style: italic;
}

.ordinal {
    --tw-ordinal: ordinal;
    font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero)
        var(--tw-numeric-figure) var(--tw-numeric-spacing)
        var(--tw-numeric-fraction);
}

.leading-4 {
    line-height: 1rem;
}

.leading-5 {
    line-height: 1.25rem;
}

.leading-6 {
    line-height: 1.5rem;
}

.leading-9 {
    line-height: 2.25rem;
}

.leading-\[16px\] {
    line-height: 16px;
}

.leading-\[17px\] {
    line-height: 17px;
}

.leading-\[18px\] {
    line-height: 18px;
}

.leading-\[19\.5px\] {
    line-height: 19.5px;
}

.leading-\[22px\] {
    line-height: 22px;
}

.leading-\[26px\] {
    line-height: 26px;
}

.leading-relaxed {
    line-height: 1.625;
}

.leading-tight {
    line-height: 1.25;
}

.tracking-widest {
    letter-spacing: 0.1em;
}

.text-\[\#000\] {
    --tw-text-opacity: 1;
    color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-\[\#00A7FF\] {
    --tw-text-opacity: 1;
    color: rgb(0 167 255 / var(--tw-text-opacity));
}

.text-\[\#1363E3\] {
    --tw-text-opacity: 1;
    color: rgb(19 99 227 / var(--tw-text-opacity));
}

.text-\[\#169F22\] {
    --tw-text-opacity: 1;
    color: rgb(22 159 34 / var(--tw-text-opacity));
}

.text-\[\#2F2C31\] {
    --tw-text-opacity: 1;
    color: rgb(47 44 49 / var(--tw-text-opacity));
}

.text-\[\#48106D\] {
    --tw-text-opacity: 1;
    color: rgb(72 16 109 / var(--tw-text-opacity));
}

.text-\[\#5F5F5F\] {
    --tw-text-opacity: 1;
    color: rgb(95 95 95 / var(--tw-text-opacity));
}

.text-\[\#707070\] {
    --tw-text-opacity: 1;
    color: rgb(112 112 112 / var(--tw-text-opacity));
}

.text-\[\#7F35B2\] {
    --tw-text-opacity: 1;
    color: rgb(127 53 178 / var(--tw-text-opacity));
}

.text-\[\#959595\] {
    --tw-text-opacity: 1;
    color: rgb(149 149 149 / var(--tw-text-opacity));
}

.text-\[\#C90A0A\] {
    --tw-text-opacity: 1;
    color: rgb(201 10 10 / var(--tw-text-opacity));
}

.text-\[\#EFF1F3\] {
    --tw-text-opacity: 1;
    color: rgb(239 241 243 / var(--tw-text-opacity));
}

.text-black {
    --tw-text-opacity: 1;
    color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-blue-600 {
    --tw-text-opacity: 1;
    color: rgb(37 99 235 / var(--tw-text-opacity));
}

.text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity));
}

.text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity));
}

.text-gray-600 {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity));
}

.text-gray-700 {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity));
}

.text-gray-800 {
    --tw-text-opacity: 1;
    color: rgb(31 41 55 / var(--tw-text-opacity));
}

.text-gray-900 {
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity));
}

.text-green-600 {
    --tw-text-opacity: 1;
    color: rgb(22 163 74 / var(--tw-text-opacity));
}

.text-indigo-600 {
    --tw-text-opacity: 1;
    color: rgb(79 70 229 / var(--tw-text-opacity));
}

.text-indigo-700 {
    --tw-text-opacity: 1;
    color: rgb(67 56 202 / var(--tw-text-opacity));
}

.text-red-600 {
    --tw-text-opacity: 1;
    color: rgb(220 38 38 / var(--tw-text-opacity));
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
}

.underline {
    text-decoration-line: underline;
}

.overline {
    text-decoration-line: overline;
}

.line-through {
    text-decoration-line: line-through;
}

.decoration-2 {
    text-decoration-thickness: 2px;
}

.underline-offset-4 {
    text-underline-offset: 4px;
}

.antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.subpixel-antialiased {
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
}

.placeholder-\[\#9A9A9A\]::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(154 154 154 / var(--tw-placeholder-opacity));
}

.placeholder-\[\#9A9A9A\]::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(154 154 154 / var(--tw-placeholder-opacity));
}

.accent-\[\#7F35B2\] {
    accent-color: #7f35b2;
}

.opacity-0 {
    opacity: 0;
}

.opacity-100 {
    opacity: 1;
}

.opacity-25 {
    opacity: 0.25;
}

.opacity-70 {
    opacity: 0.7;
}

.opacity-75 {
    opacity: 0.75;
}

.shadow {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color),
        0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
    --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0px_0px_10px_\#00000010\] {
    --tw-shadow: 0px 0px 10px #00000010;
    --tw-shadow-colored: 0px 0px 10px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0px_0px_10px_\#00000017\] {
    --tw-shadow: 0px 0px 10px #00000017;
    --tw-shadow-colored: 0px 0px 10px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0px_0px_10px_\#00000021\] {
    --tw-shadow: 0px 0px 10px #00000021;
    --tw-shadow-colored: 0px 0px 10px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0px_0px_20px_\#00000017\;\] {
    --tw-shadow: 0px 0px 20px #00000017;
    --tw-shadow-colored: 0px 0px 20px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0px_0px_20px_\#00000017\] {
    --tw-shadow: 0px 0px 20px #00000017;
    --tw-shadow-colored: 0px 0px 20px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0px_0px_20px_\#00000029\] {
    --tw-shadow: 0px 0px 20px #00000029;
    --tw-shadow-colored: 0px 0px 20px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),
        0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color),
        0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1),
        0 2px 4px -2px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
        0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1),
        0 8px 10px -6px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color),
        0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-gray-500\/20 {
    --tw-shadow-color: rgb(107 114 128 / 0.2);
    --tw-shadow: var(--tw-shadow-colored);
}

.outline {
    outline-style: solid;
}

.outline-\[\#7F35B2\] {
    outline-color: #7f35b2;
}

.ring {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.ring-1 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.ring-black {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity));
}

.ring-gray-300 {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}

.ring-opacity-5 {
    --tw-ring-opacity: 0.05;
}

.blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
        var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
        var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow {
    --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1))
        drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
        var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
        var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.grayscale {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
        var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
        var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
        var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
        var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.sepia {
    --tw-sepia: sepia(100%);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
        var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
        var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
        var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
        var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-filter {
    -webkit-backdrop-filter: var(--tw-backdrop-blur)
        var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
        var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
        var(--tw-backdrop-invert) var(--tw-backdrop-opacity)
        var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
        var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
        var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
        var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
        var(--tw-backdrop-sepia);
}

.transition {
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, backdrop-filter;
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.duration-150 {
    transition-duration: 150ms;
}

.duration-200 {
    transition-duration: 200ms;
}

.duration-300 {
    transition-duration: 300ms;
}

.duration-500 {
    transition-duration: 500ms;
}

.duration-700 {
    transition-duration: 700ms;
}

.duration-75 {
    transition-duration: 75ms;
}

.duration-\[2s\] {
    transition-duration: 2s;
}

.ease-in {
    transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-linear {
    transition-timing-function: linear;
}

.ease-out {
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.\[a-z_\:\.\\-\] {
    a-z_: .\-;
}

@font-face {
    font-family: "Poppins";

    src: url("../fonts/Poppins-Regular.ttf") format("truetype"),
        url("../fonts/Poppins-Regular.woff") format("woff"),
        url("../fonts/Poppins-Regular.woff2") format("woff2"),
        url("../fonts/Poppins-Regular.svg") format("svg"),
        url("../fonts/Poppins-Regular.eot") format("embedded-opentype");

    font-weight: normal;

    font-style: normal;
}

@font-face {
    font-family: "Poppins-Medium";

    src: url("../fonts/Poppins-Medium.ttf") format("truetype"),
        url("../fonts/Poppins-Medium.woff") format("woff"),
        url("../fonts/Poppins-Medium.woff2") format("woff2"),
        url("../fonts/Poppins-Medium.svg") format("svg"),
        url("../fonts/Poppins-Medium.eot") format("embedded-opentype");

    font-weight: 500;

    font-style: normal;
}

@font-face {
    font-family: "Poppins-SemiBold";

    src: url("../fonts/Poppins-SemiBold.ttf") format("truetype"),
        url("../fonts/Poppins-SemiBold.woff") format("woff"),
        url("../fonts/Poppins-SemiBold.woff2") format("woff2"),
        url("../fonts/Poppins-SemiBold.svg") format("svg"),
        url("../fonts/Poppins-SemiBold.eot") format("embedded-opentype");

    font-weight: 600;

    font-style: normal;
}

@font-face {
    font-family: "Poppins-Bold";

    src: url("../fonts/Poppins-Bold.ttf") format("truetype"),
        url("../fonts/Poppins-Bold.woff") format("woff"),
        url("../fonts/Poppins-Bold.woff2") format("woff2"),
        url("../fonts/Poppins-Bold.svg") format("svg"),
        url("../fonts/Poppins-Bold.eot") format("embedded-opentype");

    font-weight: 700;

    font-style: normal;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Poppins", sans-serif;
}

.main_login {
    min-height: calc(100vh - 180px);
}

#q3 {
    background: url("/images/select_arrow-down.png") no-repeat;
    background-size: 15px;
    background-position: calc(100% - 0.75rem) center !important;
    -moz-appearance: none !important;
    -webkit-appearance: none !important;
    appearance: none !important;
    padding-right: 2rem !important;
}

#brut_izin_sayisi {
    background: url("/images/arrow_down_grey.png") no-repeat;
    background-position: calc(100% - 0.75rem) center !important;
    -moz-appearance: none !important;
    -webkit-appearance: none !important;
    appearance: none !important;
}

.radio-container input:checked + .custom-radio-label {
    background-color: #0f56af;
    background-image: url('data:image/svg+xml;utf8,<svg fill="white" height="100%" viewBox="0 0 24 24" width="100%" xmlns="http://www.w3.org/2000/svg"><path d="M9 16.2L4.8 12l-1.4 1.4 5.6 5.6L20.2 7l-1.4-1.4z"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

.sirket_araci_container input:checked + .custom-checkbox-label {
    background-color: #707070;
    background-image: url('data:image/svg+xml;utf8,<svg fill="white" height="100%" viewBox="0 0 24 24" width="100%" xmlns="http://www.w3.org/2000/svg"><path d="M9 16.2L4.8 12l-1.4 1.4 5.6 5.6L20.2 7l-1.4-1.4z"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

#staticModalYanHakSepeti {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

#staticModalYanHakSepeti::-webkit-scrollbar {
    display: none;
}

#yan_hak_sepeti {
    -webkit-overflow-scrolling: touch;
}

/* Hide scrollbar in Firefox */

#yan_hak_sepeti {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE and Edge */
}

/* Hide scrollbar in WebKit (Chrome, Safari) */

#yan_hak_sepeti::-webkit-scrollbar {
    display: none !important;
    /* WebKit */
}

/* Hide scrollbar in IE and Edge */

#yan_hak_sepeti {
    -ms-overflow-style: none !important;
    /* IE and Edge */
    scrollbar-width: none !important;
    /* Firefox */
    overflow-y: scroll;
}

/******************************************************/

.center {
    text-align: center;
}

.kvvk_popup::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */

.kvvk_popup {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

.core_benefit_text {
    width: 100%;
    max-width: 300px;
}

.temel_hak_p {
    width: 90%;
}

.slider_title {
    max-width: 370px;
    left: 10%;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

@media (max-width: 500px) {
    .core_benefit_text {
        max-width: 150px;
    }

    .slider_title {
        max-width: 277px;
        left: 12%;
    }
}

/* HELP PAGE */

.help-desc strong {
    color: #7f35b2;
}

.help-desc + p strong {
    color: #7f35b2;
}

.help-desc + p {
    font-size: 14px;
}

/* HELP PAGE */

/* DASHBOARD */

#welcomeModal {
    z-index: 999999999999;
}

strong {
    color: #7f35b2 !important;
}

#relativeModal {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

#frmSecenekli_123 .further_details {
    cursor: pointer;
}

#accordion15 .further_details,
#accordion18 .further_details {
    display: none;
}

/*SLIDER STYLE */

#slider {
    position: relative;
    overflow: hidden;
    background-color: #fff;
    border-radius: 15px;
}

#slides {
    display: flex;
    transition: transform 0.5s ease-in-out;
}

.slide {
    min-width: 100%;
    box-sizing: border-box;
    padding: 2.5rem 1rem;
}

.control_next {
    position: absolute;
    bottom: 0px;
    width: auto;
    margin-top: -25px;
    padding: 16px;
    color: #fff;
    font-size: 30px;
    transition: background-color 0.3s;
    text-decoration: none;
    right: 0;
}

/*SLIDER STYLE */

@media (max-width: 768px) {
    #frmSecenekli_123 .mobile_meal_ticket,
    #accordion15 .mobile_meal_ticket,
    #accordion18 .mobile_meal_ticket {
        display: none !important;
    }

    #frmSecenekli_123 .desktop_meal_ticket,
    #accordion15 .desktop_meal_ticket,
    #accordion18 .desktop_meal_ticket {
        display: block !important;
    }

    #frmSecenekli_159 .mobile_meal_ticket {
        display: none !important;
    }

    #frmSecenekli_159 .desktop_meal_ticket {
        display: block !important;
    }
}

/* ******** */

.dark .sirket_araci_container [type="checkbox"]:checked,
.dark .sirket_araci_container [type="radio"]:checked,
.sirket_araci_container [type="checkbox"]:checked,
.sirket_araci_container [type="radio"]:checked {
    background-color: #707070;
}

.sirket_araci_container input:checked + .custom-checkbox-label {
    background-color: #707070;
}

.login_parent [type="checkbox"]:checked,
.login_parent [type="radio"]:checked,
#okudum_anladim_link [type="radio"]:checked,
input[name="do_not_show_again"]:checked,
input[name="do_not_show_again_slider"]:checked {
    background-color: #7f35b2;
}

input[name="do_not_show_again_slider"],
input[name="do_not_show_again"],
.login_parent [type="checkbox"],
.login_parent [type="radio"] {
    accent-color: #7f35b2;
}

.negative-balance {
    color: red;
}

.details_inner_div p {
    color: #2f2c31;
    font-size: 13px;
}

.details_inner_div::-webkit-scrollbar {
    display: none;
}

.details_inner_div {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.negative-balance-btn {
    background-color: red;
}

.triangle-container {
    display: none;
    position: absolute;
    right: -230px;
    top: -1rem;
    z-index: 50;
    background-color: #fff;
    /* Red color */
    width: 195px;
    border-radius: 8px;
    color: #7f35b2;
    box-shadow: 3px 3px 5px -3px rgba(127, 53, 178, 1);
}

.info-box {
    max-width: 100%;
    font-size: 12px;
    padding: 10px;
}

/* Triangle styles */

.triangle-container::before {
    content: "";
    position: absolute;
    top: 8px;
    right: 100%;
    border-width: 7px;
    border-style: solid;
    border-color: transparent #fff transparent transparent;
}

input[type="number"] {
    -moz-appearance: textfield;
}

/* For WebKit-based browsers (Chrome, Safari) */

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

@media (max-width: 1200px) {
    .triangle-container {
        right: 0px;
    }
}

/* DASHBOARD */

/* LOGIN */

.popup_content {
    overflow-y: auto;
}

/* LOGIN */

/* PAYMENT */

.negative-balance {
    color: red;
}

#sepet_scroll_part::-webkit-scrollbar {
    display: none;
}

#sepet_scroll_part {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

/* PAYMENT */

/* SIDE_BENEFIT_APPROVAL */

.kvvk_popup::-webkit-scrollbar {
    display: none;
}

.kvvk_popup {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.side_benefit_approval strong {
    color: #7f35b2 !important;
}

/* SIDE_BENEFIT_APPROVAL */

/* CLOSED_PAGE_BODY */

.closed_page_body {
    height: 100%;
    margin: 0;
    background-image: url('{{ asset("storage/" . $siteMovementData->image) }}');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

/* CLOSED_PAGE_BODY */

/* ESNEK HAK */

#esnekA a {
    color: #7f35b2 !important;
    font-weight: 600;
}

/* ESNEK HAK */

/* SEÇENEKLİ HAK */

#secenekliA a {
    color: #7f35b2 !important;
}

#secenekliA strong {
    color: #7f35b2 !important;
}

/* SEÇENEKLİ HAK */

.red {
    color: red !important;
}

.green {
    color: green !important;
}

.bg-red {
    background-color: #ff000038 !important;
}

.bg-green {
    background-color: #00800038 !important;
}

.null {
    color: #7f35b2 !important;
}

.font-medium {
    font-family: "Poppins-Medium", sans-serif;
}

.font-semibold {
    font-family: "Poppins-SemiBold", sans-serif;
}

.font-bold {
    font-family: "Poppins-Bold", sans-serif;
}

.selection\:bg-red-500 *::-moz-selection {
    --tw-bg-opacity: 1;
    background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.selection\:bg-red-500 *::selection {
    --tw-bg-opacity: 1;
    background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.selection\:text-white *::-moz-selection {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
}

.selection\:text-white *::selection {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
}

.selection\:bg-red-500::-moz-selection {
    --tw-bg-opacity: 1;
    background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.selection\:bg-red-500::selection {
    --tw-bg-opacity: 1;
    background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.selection\:text-white::-moz-selection {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
}

.selection\:text-white::selection {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:border-gray-300:hover {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.hover\:bg-blue-800:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(30 64 175 / var(--tw-bg-opacity));
}

.hover\:bg-gray-100:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.hover\:bg-gray-50:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.hover\:bg-gray-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.hover\:bg-red-500:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.hover\:bg-white:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.hover\:text-blue-600:hover {
    --tw-text-opacity: 1;
    color: rgb(37 99 235 / var(--tw-text-opacity));
}

.hover\:text-gray-400:hover {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity));
}

.hover\:text-gray-500:hover {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity));
}

.hover\:text-gray-600:hover {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity));
}

.hover\:text-gray-700:hover {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity));
}

.hover\:text-gray-800:hover {
    --tw-text-opacity: 1;
    color: rgb(31 41 55 / var(--tw-text-opacity));
}

.hover\:text-gray-900:hover {
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity));
}

.hover\:text-slate-300:hover {
    --tw-text-opacity: 1;
    color: rgb(203 213 225 / var(--tw-text-opacity));
}

.focus\:z-10:focus {
    z-index: 10;
}

.focus\:rounded-sm:focus {
    border-radius: 0.125rem;
}

.focus\:border-\[\#7F35B2\]:focus {
    --tw-border-opacity: 1;
    border-color: rgb(127 53 178 / var(--tw-border-opacity));
}

.focus\:border-blue-300:focus {
    --tw-border-opacity: 1;
    border-color: rgb(147 197 253 / var(--tw-border-opacity));
}

.focus\:border-gray-300:focus {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.focus\:border-indigo-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(99 102 241 / var(--tw-border-opacity));
}

.focus\:border-indigo-700:focus {
    --tw-border-opacity: 1;
    border-color: rgb(67 56 202 / var(--tw-border-opacity));
}

.focus\:bg-gray-100:focus {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.focus\:bg-gray-50:focus {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.focus\:bg-gray-700:focus {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.focus\:bg-indigo-100:focus {
    --tw-bg-opacity: 1;
    background-color: rgb(224 231 255 / var(--tw-bg-opacity));
}

.focus\:text-gray-500:focus {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity));
}

.focus\:text-gray-700:focus {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity));
}

.focus\:text-gray-800:focus {
    --tw-text-opacity: 1;
    color: rgb(31 41 55 / var(--tw-text-opacity));
}

.focus\:text-indigo-800:focus {
    --tw-text-opacity: 1;
    color: rgb(55 48 163 / var(--tw-text-opacity));
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.focus\:outline:focus {
    outline-style: solid;
}

.focus\:outline-2:focus {
    outline-width: 2px;
}

.focus\:outline-red-500:focus {
    outline-color: #ef4444;
}

.focus\:ring:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-1:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-4:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-\[\#7F35B2\]:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(127 53 178 / var(--tw-ring-opacity));
}

.focus\:ring-blue-300:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity));
}

.focus\:ring-gray-200:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity));
}

.focus\:ring-indigo-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity));
}

.focus\:ring-red-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity));
}

.focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
}

.active\:bg-gray-100:active {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.active\:bg-gray-900:active {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

.active\:bg-red-700:active {
    --tw-bg-opacity: 1;
    background-color: rgb(185 28 28 / var(--tw-bg-opacity));
}

.active\:text-gray-500:active {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity));
}

.active\:text-gray-700:active {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity));
}

.disabled\:opacity-25:disabled {
    opacity: 0.25;
}

.group:hover .group-hover\:stroke-gray-600 {
    stroke: #4b5563;
}

:is([dir="rtl"] .rtl\:rotate-180) {
    --tw-rotate: 180deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@media (prefers-reduced-motion: no-preference) {
    .motion-safe\:hover\:scale-\[1\.01\]:hover {
        --tw-scale-x: 1.01;
        --tw-scale-y: 1.01;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y))
            rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
            skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
            scaleY(var(--tw-scale-y));
    }
}

@media (prefers-color-scheme: dark) {
    .dark\:border-blue-500 {
        --tw-border-opacity: 1;
        border-color: rgb(59 130 246 / var(--tw-border-opacity));
    }

    .dark\:border-gray-600 {
        --tw-border-opacity: 1;
        border-color: rgb(75 85 99 / var(--tw-border-opacity));
    }

    .dark\:border-gray-700 {
        --tw-border-opacity: 1;
        border-color: rgb(55 65 81 / var(--tw-border-opacity));
    }

    .dark\:border-transparent {
        border-color: transparent;
    }

    .dark\:bg-blue-600 {
        --tw-bg-opacity: 1;
        background-color: rgb(37 99 235 / var(--tw-bg-opacity));
    }

    .dark\:bg-gray-600 {
        --tw-bg-opacity: 1;
        background-color: rgb(75 85 99 / var(--tw-bg-opacity));
    }

    .dark\:bg-gray-700 {
        --tw-bg-opacity: 1;
        background-color: rgb(55 65 81 / var(--tw-bg-opacity));
    }

    .dark\:bg-gray-800 {
        --tw-bg-opacity: 1;
        background-color: rgb(31 41 55 / var(--tw-bg-opacity));
    }

    .dark\:bg-gray-800\/50 {
        background-color: rgb(31 41 55 / 0.5);
    }

    .dark\:bg-gray-900 {
        --tw-bg-opacity: 1;
        background-color: rgb(17 24 39 / var(--tw-bg-opacity));
    }

    .dark\:bg-gray-900\/80 {
        background-color: rgb(17 24 39 / 0.8);
    }

    .dark\:bg-red-800\/20 {
        background-color: rgb(153 27 27 / 0.2);
    }

    .dark\:bg-gradient-to-bl {
        background-image: linear-gradient(
            to bottom left,
            var(--tw-gradient-stops)
        );
    }

    .dark\:stroke-gray-600 {
        stroke: #4b5563;
    }

    .dark\:text-blue-500 {
        --tw-text-opacity: 1;
        color: rgb(59 130 246 / var(--tw-text-opacity));
    }

    .dark\:text-gray-400 {
        --tw-text-opacity: 1;
        color: rgb(156 163 175 / var(--tw-text-opacity));
    }

    .dark\:text-white {
        --tw-text-opacity: 1;
        color: rgb(255 255 255 / var(--tw-text-opacity));
    }

    .dark\:shadow-none {
        --tw-shadow: 0 0 #0000;
        --tw-shadow-colored: 0 0 #0000;
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
            var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    }

    .dark\:ring-1 {
        --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
            var(--tw-ring-offset-width) var(--tw-ring-offset-color);
        --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
            calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
        box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
            var(--tw-shadow, 0 0 #0000);
    }

    .dark\:ring-inset {
        --tw-ring-inset: inset;
    }

    .dark\:ring-white\/5 {
        --tw-ring-color: rgb(255 255 255 / 0.05);
    }

    .dark\:hover\:bg-blue-700:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(29 78 216 / var(--tw-bg-opacity));
    }

    .dark\:hover\:bg-gray-600:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(75 85 99 / var(--tw-bg-opacity));
    }

    .dark\:hover\:bg-gray-800:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(31 41 55 / var(--tw-bg-opacity));
    }

    .dark\:hover\:text-blue-500:hover {
        --tw-text-opacity: 1;
        color: rgb(59 130 246 / var(--tw-text-opacity));
    }

    .dark\:hover\:text-gray-300:hover {
        --tw-text-opacity: 1;
        color: rgb(209 213 219 / var(--tw-text-opacity));
    }

    .dark\:hover\:text-white:hover {
        --tw-text-opacity: 1;
        color: rgb(255 255 255 / var(--tw-text-opacity));
    }

    .group:hover .dark\:group-hover\:stroke-gray-400 {
        stroke: #9ca3af;
    }
}

@media (min-width: 640px) {
    .sm\:static {
        position: static;
    }

    .sm\:fixed {
        position: fixed;
    }

    .sm\:right-0 {
        right: 0px;
    }

    .sm\:right-\[2\.5rem\] {
        right: 2.5rem;
    }

    .sm\:top-0 {
        top: 0px;
    }

    .sm\:top-\[2\.5rem\] {
        top: 2.5rem;
    }

    .sm\:-my-px {
        margin-top: -1px;
        margin-bottom: -1px;
    }

    .sm\:mx-auto {
        margin-left: auto;
        margin-right: auto;
    }

    .sm\:mb-0 {
        margin-bottom: 0px;
    }

    .sm\:ml-0 {
        margin-left: 0px;
    }

    .sm\:ml-10 {
        margin-left: 2.5rem;
    }

    .sm\:ml-6 {
        margin-left: 1.5rem;
    }

    .sm\:mt-5 {
        margin-top: 1.25rem;
    }

    .sm\:flex {
        display: flex;
    }

    .sm\:hidden {
        display: none;
    }

    .sm\:h-\[108px\] {
        height: 108px;
    }

    .sm\:w-\[130px\] {
        width: 130px;
    }

    .sm\:w-\[222px\] {
        width: 222px;
    }
    .md\:w-\[222px\] {
        width: 222px;
    }
    .md\:max-w-\[75px\] {
        max-width: 75px;
    }
    .sm\:w-auto {
        width: auto;
    }

    .sm\:w-full {
        width: 100%;
    }

    .sm\:max-w-2xl {
        max-width: 42rem;
    }

    .sm\:max-w-\[130px\] {
        max-width: 130px;
    }

    .sm\:max-w-\[156px\] {
        max-width: 156px;
    }

    .sm\:max-w-\[160px\] {
        max-width: 160px;
    }

    .sm\:max-w-\[230px\] {
        max-width: 230px;
    }

    .sm\:max-w-\[249px\] {
        max-width: 249px;
    }

    .sm\:max-w-\[280px\] {
        max-width: 280px;
    }

    .sm\:max-w-\[320px\] {
        max-width: 320px;
    }

    .sm\:max-w-\[330px\] {
        max-width: 330px;
    }

    .sm\:max-w-\[415px\] {
        max-width: 415px;
    }

    .sm\:max-w-\[490px\] {
        max-width: 490px;
    }

    .sm\:max-w-\[580px\] {
        max-width: 580px;
    }

    .sm\:max-w-lg {
        max-width: 32rem;
    }

    .sm\:max-w-md {
        max-width: 28rem;
    }

    .sm\:max-w-none {
        max-width: none;
    }

    .sm\:max-w-sm {
        max-width: 24rem;
    }

    .sm\:max-w-xl {
        max-width: 36rem;
    }

    .sm\:flex-1 {
        flex: 1 1 0%;
    }

    .sm\:translate-y-0 {
        --tw-translate-y: 0px;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y))
            rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
            skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
            scaleY(var(--tw-scale-y));
    }

    .sm\:scale-100 {
        --tw-scale-x: 1;
        --tw-scale-y: 1;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y))
            rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
            skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
            scaleY(var(--tw-scale-y));
    }

    .sm\:scale-95 {
        --tw-scale-x: 0.95;
        --tw-scale-y: 0.95;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y))
            rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
            skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
            scaleY(var(--tw-scale-y));
    }

    .sm\:flex-row {
        flex-direction: row;
    }

    .sm\:items-center {
        align-items: center;
    }

    .sm\:justify-start {
        justify-content: flex-start;
    }

    .sm\:justify-end {
        justify-content: flex-end;
    }

    .sm\:justify-center {
        justify-content: center;
    }

    .sm\:justify-between {
        justify-content: space-between;
    }

    .sm\:gap-0 {
        gap: 0px;
    }
    .sm\:gap-2 {
        gap: 0.5rem;
    }

    .sm\:gap-7 {
        gap: 1.75rem;
    }

    .sm\:gap-\[7px\] {
        gap: 7px;
    }

    .sm\:rounded-lg {
        border-radius: 0.5rem;
    }

    .sm\:p-5 {
        padding: 1.25rem;
    }

    .sm\:p-8 {
        padding: 2rem;
    }

    .sm\:p-\[2\.5rem\] {
        padding: 2.5rem;
    }

    .sm\:px-0 {
        padding-left: 0px;
        padding-right: 0px;
    }

    .sm\:px-5 {
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }

    .sm\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .sm\:px-\[2rem\] {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .sm\:py-5 {
        padding-top: 1.25rem;
        padding-bottom: 1.25rem;
    }

    .sm\:py-\[5rem\] {
        padding-top: 5rem;
        padding-bottom: 5rem;
    }

    .sm\:pl-10 {
        padding-left: 2.5rem;
    }

    .sm\:pl-4 {
        padding-left: 1rem;
    }

    .sm\:pl-5 {
        padding-left: 1.25rem;
    }

    .sm\:pt-0 {
        padding-top: 0px;
    }

    .sm\:text-left {
        text-align: left;
    }

    .sm\:text-right {
        text-align: right;
    }

    .sm\:text-\[13px\] {
        font-size: 13px;
    }

    .sm\:text-\[14px\] {
        font-size: 14px;
    }

    .sm\:text-\[16px\] {
        font-size: 16px;
    }

    .sm\:text-\[18px\] {
        font-size: 18px;
    }

    .sm\:text-\[26px\] {
        font-size: 26px;
    }
}

@media (min-width: 768px) {
    .md\:inset-0 {
        inset: 0px;
    }

    .md\:mb-0 {
        margin-bottom: 0px;
    }

    .md\:block {
        display: block;
    }

    .md\:inline-block {
        display: inline-block;
    }

    .md\:flex {
        display: flex;
    }

    .md\:hidden {
        display: none;
    }

    .md\:h-96 {
        height: 24rem;
    }

    .md\:h-\[144px\] {
        height: 144px;
    }

    .md\:h-\[276px\] {
        height: 276px;
    }

    .md\:w-\[130px\] {
        width: 130px;
    }

    .md\:w-\[141px\] {
        width: 141px;
    }

    .md\:w-\[143px\] {
        width: 143px;
    }

    .md\:w-\[144px\] {
        width: 144px;
    }

    .md\:w-\[156px\] {
        width: 156px;
    }

    .md\:w-\[756px\] {
        width: 756px;
    }

    .md\:max-w-\[201px\] {
        max-width: 201px;
    }

    .md\:max-w-\[210px\] {
        max-width: 210px;
    }

    .md\:max-w-\[220px\] {
        max-width: 220px;
    }

    .md\:max-w-\[329\.14px\] {
        max-width: 329.14px;
    }

    .md\:max-w-\[400px\] {
        max-width: 400px;
    }

    .md\:max-w-\[503px\] {
        max-width: 503px;
    }

    .md\:max-w-\[600px\] {
        max-width: 600px;
    }

    .md\:max-w-\[650px\] {
        max-width: 650px;
    }

    .md\:max-w-\[683px\] {
        max-width: 683px;
    }

    .md\:max-w-\[721px\] {
        max-width: 721px;
    }

    .md\:max-w-\[756px\] {
        max-width: 756px;
    }

    .md\:max-w-\[914px\] {
        max-width: 914px;
    }

    .md\:translate-x-0 {
        --tw-translate-x: 0px;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y))
            rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
            skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
            scaleY(var(--tw-scale-y));
    }

    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\:flex-row {
        flex-direction: row;
    }

    .md\:items-start {
        align-items: flex-start;
    }

    .md\:items-center {
        align-items: center;
    }

    .md\:justify-start {
        justify-content: flex-start;
    }

    .md\:gap-0 {
        gap: 0px;
    }

    .md\:gap-4 {
        gap: 1rem;
    }

    .md\:gap-5 {
        gap: 1.25rem;
    }

    .md\:border-0 {
        border-width: 0px;
    }

    .md\:p-9 {
        padding: 2.25rem;
    }

    .md\:px-5 {
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }
    .md\:pr-4 {
        padding-right: 1rem;
    }

    .md\:px-\[1rem\] {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .md\:py-10 {
        padding-top: 2.5rem;
        padding-bottom: 2.5rem;
    }

    .md\:py-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    .md\:pb-1 {
        padding-bottom: 0.25rem;
    }

    .md\:pb-2 {
        padding-bottom: 0.5rem;
    }

    .md\:pb-8 {
        padding-bottom: 2rem;
    }

    .md\:pl-0 {
        padding-left: 0px;
    }

    .md\:pl-10 {
        padding-left: 2.5rem;
    }

    .md\:pl-3 {
        padding-left: 0.75rem;
    }

    .md\:pl-4 {
        padding-left: 1rem;
    }

    .md\:pl-9 {
        padding-left: 2.25rem;
    }

    .md\:pt-10 {
        padding-top: 2.5rem;
    }

    .md\:pt-9 {
        padding-top: 2.25rem;
    }

    .md\:text-left {
        text-align: left;
    }

    .md\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem;
    }

    .md\:text-\[13px\] {
        font-size: 13px;
    }

    .md\:text-\[14px\] {
        font-size: 14px;
    }

    .md\:text-\[16px\] {
        font-size: 16px;
    }

    .md\:text-\[17px\] {
        font-size: 17px;
    }

    .md\:text-\[20px\] {
        font-size: 20px;
    }

    .md\:text-\[23px\] {
        font-size: 23px;
    }

    .md\:text-\[26px\] {
        font-size: 26px;
    }
}

@media (min-width: 1024px) {
    .lg\:sticky {
        position: sticky;
    }

    .lg\:top-\[1rem\] {
        top: 1rem;
    }

    .lg\:block {
        display: block;
    }

    .lg\:flex {
        display: flex;
    }

    .lg\:grid {
        display: grid;
    }

    .lg\:hidden {
        display: none;
    }

    .lg\:w-\[914px\] {
        width: 914px;
    }

    .lg\:justify-center {
        justify-content: center;
    }

    .lg\:gap-8 {
        gap: 2rem;
    }

    .lg\:p-8 {
        padding: 2rem;
    }

    .lg\:p-9 {
        padding: 2.25rem;
    }

    .lg\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .lg\:px-\[2rem\] {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .lg\:px-\[3rem\] {
        padding-left: 3rem;
        padding-right: 3rem;
    }

    .lg\:py-4 {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    .lg\:py-\[1rem\] {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    .lg\:pl-10 {
        padding-left: 2.5rem;
    }

    .lg\:text-\[16px\] {
        font-size: 16px;
    }

    .lg\:text-\[26px\] {
        font-size: 26px;
    }
}

@media (min-width: 1280px) {
    .xl\:m-0 {
        margin: 0px;
    }
}

@media (min-width: 1536px) {
    .\32xl\:mt-5 {
        margin-top: 1.25rem;
    }

    .\32xl\:h-auto {
        height: auto;
    }

    .\32xl\:max-w-\[167px\] {
        max-width: 167px;
    }

    .\32xl\:max-w-\[950px\] {
        max-width: 950px;
    }

    .\32xl\:px-\[50px\] {
        padding-left: 50px;
        padding-right: 50px;
    }

    .\32xl\:py-4 {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    .\32xl\:py-\[11px\] {
        padding-top: 11px;
        padding-bottom: 11px;
    }

    .\32xl\:py-\[5rem\] {
        padding-top: 5rem;
        padding-bottom: 5rem;
    }

    .\32xl\:py-\[8px\] {
        padding-top: 8px;
        padding-bottom: 8px;
    }

    .\32xl\:pb-8 {
        padding-bottom: 2rem;
    }

    .\32xl\:pb-9 {
        padding-bottom: 2.25rem;
    }

    .\32xl\:pt-5 {
        padding-top: 1.25rem;
    }

    .\32xl\:pt-\[70px\] {
        padding-top: 70px;
    }

    .\32xl\:text-\[16px\] {
        font-size: 16px;
    }

    .\32xl\:text-\[26px\] {
        font-size: 26px;
    }

    .\32xl\:leading-\[20px\] {
        line-height: 20px;
    }

    .\32xl\:leading-\[29px\] {
        line-height: 29px;
    }
}

#birey_field_p_adet_brut {
    padding-right: 0 !important;
}
.outline-none {
  outline: none !important;
  }

  .border-\[\#7F35B2\] {
    border-color: #7F35B2;
  }

  .ring-1 {
    --tw-ring-inset: var(--tw-empty, /*!*/ /*!*/);
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(127 53 178 / var(--tw-ring-opacity));
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  }

  .ring-\[\#7F35B2\] {
    --tw-ring-color: rgb(127 53 178 / var(--tw-ring-opacity));
  }
