// LOGIN PASSWORD

  // LOGIN PASSWORD

  //NAVBAR
  $(document).ready(function () {
    $("#hamburger_menu").on("click", function () {
        $("#mobile_navbar").removeClass("scale-0");
        $("#mobile_navbar").addClass("scale-1");
    });
  });
  $(document).ready(function () {
    $("#mobile_close_btn").on("click", function () {
        $("#mobile_navbar").removeClass("scale-1");
        $("#mobile_navbar").addClass("scale-0");
    });
  });
  //NAVBAR

  //HOME ACCORDION
  $(document).ready(function () {
      $(document).on("click", ".accordion_btn", function () {
        let accordionItem = $(this)
            .closest(".accordion_div")
            .find(".accordion_item");
        // $(".accordion_item").not(accordionItem).removeClass("max-h-fit").addClass("max-h-0");
        accordionItem.toggleClass("max-h-0 max-h-fit");
        if(!accordionItem.hasClass("overflow-hidden")){
          accordionItem.addClass("overflow-hidden")
                }

        if (accordionItem.find(".cart_item").length !== 0) {
            let whiteArrow = $(this)
                .closest(".accordion_div")
                .find(".white-arrow");
            whiteArrow.toggleClass("rotate-0 rotate-180");
            accordionItem.find(".white-arrow").removeClass("rotate-180");
        }
    });
  });



  // //ŞİRKET ARACI CHECKBOX

  $("#sirket_araci").prop("checked",true);
  $(`#sirket_araci`).closest(".checkbox-container").addClass("bg-[#707070]/[0.3]");
  $(`#sirket_araci`).prop("checked", true);
  $(`#sirket_araci`)
    .closest(".checkbox-container")
    .addClass("bg-[#707070]/[0.3]");
  const updateCheckboxContainerBackground = (checkbox) => {
    const checkboxContainer = checkbox.closest(".checkbox-container");

    if (
        checkboxContainer.parentElement.classList.contains(
            "sirket_araci_container"
        )
    ) {
        if (checkbox.checked) {
            checkboxContainer.classList.add("bg-[#707070]/[0.3]");
        } else {
            checkboxContainer.classList.remove("bg-[#707070]/[0.3]");
        }
    }
  };
  // //ŞİRKET ARACI CHECKBOX

  // //MEAL TICKET RADIO FUNCTION
  const updateRadioContainerBackground = (radio) => {
    const radioContainers2 = document.querySelectorAll(".meal_ticket_container .radio-container");

    const radioContainers3 = document.querySelectorAll(".plan_container .radio-container");
    radioContainers2.forEach((container) => {
        container.classList.remove("bg-[#0F56AF]/[0.3]");
    });
    radioContainers3.forEach((container) => {
        container.classList.remove("bg-[#0F56AF]/[0.3]");
    });

    const radioContainer2 = radio.closest(".radio-container");
    radioContainer2.classList.add("bg-[#0F56AF]/[0.3]");
    const radioContainer3 = radio.closest(".radio-container");
    radioContainer3.classList.add("bg-[#0F56AF]/[0.3]");

    const isUnderFrmSecenekli123 = $(radio).closest("#frmSecenekli_123").length > 0;
    if (isUnderFrmSecenekli123) {
    const parentDiv = $(radio).closest(".meal_ticket_container").parent();
  const triangleContainer = parentDiv.find(".triangle-container");
  $(".triangle-container").hide();

  if (triangleContainer.length) {
    triangleContainer.show();
    $("#triangle_overlay").show()
  }
  setTimeout(() => {
    $(".triangle-container").hide();
    $("#triangle_overlay").hide()
  }, 5000);
}
  };
  // //**************** */






  // //********************************************* */
  // //SIDEBAR
  $(document).ready(function () {
    $("#cart_btn").click(function () {
      console.log("Cart button clicked!");
        // if ($("#yan_hak_sepeti").hasClass("right-[-100%]")) {
        //     $("#yan_hak_sepeti").removeClass("right-[-100%]");
        //     $("#yan_hak_sepeti").addClass("right-0");
        // } else {
        //     $("#yan_hak_sepeti").removeClass("right-0");
        //     $("#yan_hak_sepeti").addClass("right-[-100%]");
        // }
        if ($(window).width() > $(window).height() && $(window).width()<800) {
          console.log("Window width condition met!");
          if ($("#yan_hak_sepeti").hasClass("right-[-100%]")) {
            $("#yan_hak_sepeti").removeClass("right-[-100%]");
            $("#yan_hak_sepeti").addClass("right-0");
            setTimeout(function () {
              console.log("test");
              $("#yan_hak_sepeti").removeClass("fixed");
              $("#yan_hak_sepeti").addClass("absolute");
            }, 500);
        } else {

            $("#yan_hak_sepeti").removeClass("right-0");
            $("#yan_hak_sepeti").addClass("right-[-100%]");
        }
        setTimeout(function () {
          $("#yan_hak_sepeti").removeClass("absolute");
          $("#yan_hak_sepeti").addClass("fixed");
        }, 500);
        } else{
          if ($("#yan_hak_sepeti").hasClass("right-[-100%]")) {
              $("#yan_hak_sepeti").removeClass("right-[-100%] absolute");
              $("#yan_hak_sepeti").addClass("right-0 fixed");
          } else {
              $("#yan_hak_sepeti").removeClass("right-0 absolute");
              $("#yan_hak_sepeti").addClass("right-[-100%] fixed");
          }
        }

    });

    $("#yan_haklar_sepeti_close_btn").click(function () {
        $("#yan_hak_sepeti").removeClass("right-0 absolute");
        $("#yan_hak_sepeti").addClass("right-[-100%] fixed");
    });
    $("#alısverisi_tamamla").click(function () {
        $("#yan_hak_sepeti").removeClass("right-0 absolute");
        $("#yan_hak_sepeti").addClass("right-[-100%] fixed");
    });
    $("#sepeti_sifirla").click(function () {
        $("#yan_hak_sepeti").removeClass("right-0 absolute");
        $("#yan_hak_sepeti").addClass("right-[-100%] fixed");
    });
  });



  $(document).on("click", ".cart_close_btn", function () {
    $(this).closest(".cart_item").remove();
    localStorage.setItem('secondCartArray', JSON.stringify([]));
    $("input[name='meal_ticket']").prop('checked', false);
    $(".radio-container").removeClass("bg-[#0F56AF]/[0.3]");
    if( $("#cart_second_part")
              .closest(".accordion_div")
              .find(".cart_item").length === 0){
                $("#cart_second_part")
              .closest(".accordion_div").find(".white-arrow").removeClass("rotate-180");
              $("#cart_second_part_total")
              // .text("0.00 TL")
              }

  });




  // When an input with name "plan" is changed, update the third cart and local storage
  $('input[name="plan"]').change(function () {
    // Assuming you have some way to get the title, content, and imgSrc for the thirdCartArray
    const title = 'Özel Sağlık Sigortası';
    const content = $(this)
      .closest(".plan_container")
      .parent()
      .find(".private_health_policy_value")
      .text()
      .trim();
    const imgSrc = $(".heart-img").attr("src");

    const selectedItemInfo = {
      title: title,
      content: content,
      imgSrc: imgSrc,
      value: $(this).val()
    };

    // Update local storage
    localStorage.setItem('thirdCartArray', JSON.stringify([selectedItemInfo]));

    // Update the cart
    updateThirdCart(selectedItemInfo);
  });
  $(document).on("click", ".cart_close_btn", function () {
    $(this).closest(".cart_item").remove();
    localStorage.setItem('thirdCartArray', JSON.stringify([]));
    $("input[name='plan']").prop('checked', false);
    $(".radio-container").removeClass("bg-[#0F56AF]/[0.3]");
    if( $("#cart_third_part")
              .closest(".accordion_div")
              .find(".cart_item").length === 0){
                $("#cart_third_part")
              .closest(".accordion_div").find(".white-arrow").removeClass("rotate-180");
              }
              $("#cart_third_part_total")
              .text("0.00 TL")


  });

  //**************************************************** */

  // Function to update the fourth cart
  function updateFourthCart(selectedItem) {
    const cartContainer = $("#cart_fourth_part");

    // Clear existing cart items
    cartContainer.empty();

    // Iterate over all selected items and add them to the cart
    for (const item of selectedItem) {
      const newDiv = $("<div>").addClass("border-b border-slate-300/[0.5] py-1 flex items-center justify-between cart_item");
      const innerDiv = $("<div>");
      const title = $("<p>").addClass("text-black text-[11px] font-semibold").text(item.title);
      const content = $("<p>").addClass("text-[#5F5F5F] text-[11px]").text(`Net: 100,00 TL - Brüt ${item.content}`);
      const image = $("<img>").attr("src", "images/close.png").attr("alt", "close").addClass("w-[10px] cursor-pointer cart_close_btn");

      innerDiv.append(title, content);
      newDiv.append(innerDiv, image);

      cartContainer.append(newDiv);

    }
  }

  $(document).ready(function () {
    let storedFourthCartArray = JSON.parse(localStorage.getItem('fourthCartArray')) || [];

    if (storedFourthCartArray.length > 0) {
      // Update the cart with the stored array
      updateFourthCart(storedFourthCartArray);


    }
  });
  let totalCash;
  let sum=0;
  // When an input with name "yourInputName" is changed, update the fourth cart and local storage
  // Function to update the fourth cart
  function updateFourthCart(selectedItems) {
    const cartContainer = $("#cart_fourth_part");

    // Clear existing cart items
    cartContainer.empty();
    sum=0
    // Iterate over all selected items and add them to the cart
    for (const selectedItem of selectedItems) {
      const newDiv = $("<div>").addClass("border-b border-slate-300/[0.5] pb-1 flex items-center justify-between cart_item");
      const innerDiv = $("<div>");
      const title = $("<p>").addClass("text-black text-[11px] font-semibold").text(selectedItem.title);
      const content = $("<p>").addClass("text-[#5F5F5F] text-[11px]").text(`Net: 100,00 TL - Brüt ${selectedItem.content} TL`);
      const image = $("<img>").attr("src", "images/close.png").attr("alt", "close").addClass("w-[10px] cursor-pointer cart_close_btn");
  sum+=parseFloat(selectedItem.content)
  totalCash=totalCash-sum
  $("#cart_fourth_part_total").text(`${sum.toFixed(2)} TL`)
      innerDiv.append(title, content);
      newDiv.append(innerDiv, image);

      cartContainer.append(newDiv);

    }
  }





  $(document).on("click", ".cart_close_btn", function () {
    $(this).closest(".cart_item").remove();
    localStorage.setItem('fourthCartArray', JSON.stringify([]));
    if( $("#cart_fourth_part")
              .closest(".accordion_div")
              .find(".cart_item").length === 0){
                $("#cart_fourth_part")
              .closest(".accordion_div").find(".white-arrow").removeClass("rotate-180");

                $("#cart_fourth_part_total")
              .text("0.00 TL")

              }
  });


  /*************APPROVAL**********************/

    $(document).ready(function() {
        $('#survey').on('submit', function(e) {
            e.preventDefault();

            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                data: $(this).serialize(),
                success: function(response) {
                    console.log(response);
                    $('#survey_modal').addClass("hidden").removeClass("flex");
                    $('#survey_modal_answer').removeClass("hidden").addClass("flex");

                    // $('#thankyou_text').text( "{{ __('contents.anket_metin') }}");
                    // $('#thankyou_title').text( "{{ __('contents.anket_baslik') }}");
                },
                error: function(xhr) {
                    alert('Hata oluştu!');
                }
            });
        });

        $('#answerClose').on('click', function() {
            $('#survey_modal_answer').addClass("hidden").removeClass("flex");
            $('body').removeClass("overflow-hidden")
        });
        $('#survey_close_btn').on('click', function() {
            $('#survey_modal').addClass("hidden").removeClass("flex");
            $('body').removeClass("overflow-hidden")
        });
        $('#atla_btn').on('click', function() {
            $('#survey_modal').addClass("hidden").removeClass("flex");
            $('body').removeClass("overflow-hidden")
        });

        function getCurrentDateFormatted() {
            const now = new Date();
            let day = now.getDate().toString();
            let month = (now.getMonth() + 1).toString();
            const year = now.getFullYear();

            day = day.length < 2 ? '0' + day : day;
            month = month.length < 2 ? '0' + month : month;

            return `${day}.${month}.${year}`;
        }


        $(document).on('click', '#taahutnameBtn', function (e) {
            e.preventDefault();

            $('#loadingSpinner').show();

            var member_id = $(this).data('user-id');

            const currentDate = getCurrentDateFormatted();
            const start_date = currentDate;
            const end_date = currentDate;

            var query = $.param({
                member_id: member_id,
                start_date: start_date,
                end_date: end_date
            });

            window.location.href = '/get-export-pdf?' + query;

            setTimeout(function () {
                $('#loadingSpinner').hide();
            }, 4000);
        });


        // $("#taahutnameBtn").on("click",function(){
        //     $("#home-link").attr("href","#")
        // })


    });





  /*************APPROVAL**********************/


  // PAYMENT

    $(document).ready(function() {

    $("#cart_icon_btn").click(function () {

        if ($(window).width() > $(window).height() && $(window).width()<800) {
          if ($("#yan_hak_sepeti").hasClass("right-[-100%]")) {
            $("#yan_hak_sepeti").removeClass("right-[-100%]");
            $("#yan_hak_sepeti").addClass("right-0");
            setTimeout(function () {
              $("#yan_hak_sepeti").removeClass("fixed");
              $("#yan_hak_sepeti").addClass("absolute");
            }, 500);
        } else {

            $("#yan_hak_sepeti").removeClass("right-0");
            $("#yan_hak_sepeti").addClass("right-[-100%]");
            setTimeout(function () {
          $("#yan_hak_sepeti").removeClass("absolute");
          $("#yan_hak_sepeti").addClass("fixed");
        }, 500);
        }

        } else{
          if ($("#yan_hak_sepeti").hasClass("right-[-100%]")) {
              $("#yan_hak_sepeti").removeClass("right-[-100%] absolute");
              $("#yan_hak_sepeti").addClass("right-0 fixed");
          } else {
              $("#yan_hak_sepeti").removeClass("right-0 absolute");
              $("#yan_hak_sepeti").addClass("right-[-100%] fixed");
          }
        }

    });

    })


  // PAYMENT

//  LOGIN



    $(document).ready(function () {

        $("[id^=kvvk_]").change(function () {
            var id = this.id.split("_")[1];
            $("#popup_overlay_" + id)
                .toggleClass("flex", this.checked)
                .toggleClass("hidden", !this.checked);
        });


        $("[id^=kvkk_close_btn_]").on("click", function () {

            var id = this.id.split("_")[3];
            $("#popup_overlay_" + id).addClass("hidden").removeClass("flex");
        });
    });



    $(document).ready(function () {
        // const selectedLanguage = "{{ session('app_language', 'tr') }}";
        const trLink = $(".tr-link");

        if (appLocale === "tr" && trLink.length > 0) {
            trLink.addClass("font-bold").removeClass("font-normal");
        }
    });

    $(document).ready(function () {
        $("#popup_overlay_3 .content_parent_div").removeClass("h-full max-h-[570px]").addClass("h-fit");
    });



//  LOGIN



//SİDE BENEFITS



$(document).ready(function () {
      $("#approval_button").hide();
      // Radio butonunun durum değiştirdiğinde tetiklenen fonksiyon
      $("#okudum-anladim").on("change", function () {
        var isRadioChecked = $(this).prop("checked");

        if (isRadioChecked) {
          $("#approval_button").show();
          $("#user_agreement_button").hide();
        } else {
          $("#approval_button").hide();
          $("#user_agreement_button").show();
        }
      });

    });

//SİDE BENEFITS

//SEÇENEKLİ


//SEÇENEKLİ
