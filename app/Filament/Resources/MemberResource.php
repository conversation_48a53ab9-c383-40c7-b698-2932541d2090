<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MemberResource\Pages;
use App\Filament\Resources\MemberResource\RelationManagers;
use App\Models\Member;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\ReplicateAction;


class MemberResource extends Resource
{
    protected static ?string $model = Member::class;
    protected static ?string $navigationGroup = 'Members & Benefits';
    protected static ?string $navigationIcon = 'heroicon-o-user';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->maxLength(255)
                    ->required(),
                Forms\Components\TextInput::make('password')
                    ->password()
                    ->dehydrateStateUsing(fn($state, $record) => filled($state) ? Hash::make($state) : $record?->password)
                    ->dehydrated(fn($state, $record) => is_null($record) || (filled($state) && $state !== $record->getRawOriginal('password')))
                    ->required(fn(string $context): bool => $context === 'create')
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->maxLength(255)
                    ->required(),
                Forms\Components\TextInput::make('identity_number')
                    ->maxLength(255)
                    ->required(),
                Forms\Components\DatePicker::make('birth_date'),
                Forms\Components\TextInput::make('percentage')
                    ->numeric(),
                Forms\Components\TextInput::make('family_percentage')
                    ->numeric(),
                Forms\Components\TextInput::make('positive_balance')
                    ->numeric(),
                Forms\Components\Select::make('firm_id')
                    ->relationship('firm', 'name', function ($query) {
                        return $query->whereNotNull('name');
                    }),
                Forms\Components\Select::make('department_id')
                    ->relationship('department', 'name', function ($query) {
                        return $query->whereNotNull('name');
                    }),
                Forms\Components\Select::make('title_id')
                    ->relationship('title', 'name', function ($query) {
                        return $query->whereNotNull('name');
                    }),
                Forms\Components\Select::make('position_id')
                    ->relationship('position', 'name', function ($query) {
                        return $query->whereNotNull('name');
                    }),
                Forms\Components\Select::make('workplace_id')
                    ->relationship('workplace', 'name', function ($query) {
                        return $query->whereNotNull('name');
                    }),
                Forms\Components\DateTimePicker::make('last_login_date'),
                Forms\Components\TextInput::make('maritial_status')
                    ->maxLength(255),
                Forms\Components\Toggle::make('is_approved'),
                Forms\Components\Toggle::make('status'),
                Forms\Components\TextInput::make('phone_number')
                    ->maxLength(255),
            ]);
    }


    // public static function table(Table $table): Table
    // {

    //     return $table
    //         ->columns([
    //             Tables\Columns\TextColumn::make('identity_number')->sortable()->searchable(),
    //             Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
    //             Tables\Columns\TextColumn::make('email')->sortable()->searchable()
    //             ->label('Benefits Approved')
    //             ->sortable()
    //             ->searchable()
    //             ->getStateUsing(function ($record) {
    //                 return $record->is_approved === null ? '0' : $record->is_approved;
    //             }),
    //             ])
    //         ->filters([
    //             //
    //         ],

    //         layout: \Filament\Tables\Enums\FiltersLayout::AboveContent,
    //         )
    //         ->actions([
    //             Tables\Actions\EditAction::make(),
    //             Tables\Actions\DeleteAction::make(),
    //         ])
    //         ->bulkActions([
    //             Tables\Actions\DeleteBulkAction::make(),
    //         ]);
    // }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('identity_number')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('email')->sortable()->searchable(),
                Tables\Columns\BooleanColumn::make('is_approved')
                    ->label('Benefits Approved')
                    ->sortable()
                    ->searchable()
                    ->getStateUsing(function ($record) {
                        return $record->is_approved === null ? 0 : $record->is_approved;
                    }),
                Tables\Columns\BooleanColumn::make('status')->sortable()->searchable()
                    ->getStateUsing(function ($record) {
                        return $record->status === null ? 0 : $record->status;
                    }),
            ])
            ->filters([
                Tables\Filters\Filter::make('status')->query(fn(Builder $query): Builder => $query->where('status', false))->label('Show Blocked')
            ],
//                layout: \Filament\Tables\Enums\FiltersLayout::AboveContent,
            )
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ReplicateAction::make()
                ->excludeAttributes(['status', 'email', 'identity_number', 'password', 'last_login_date', 'is_approved'])
                // ->mutateRecordDataUsing(function (array $data): array {
                //     $data['user_id'] = auth()->id();

                //     return $data;
                // })
                ->successNotificationTitle('Başarıyla Kopyalandı'),
                Tables\Actions\DeleteAction::make(),


            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }


    public static function getRelations(): array
    {
        return [
            RelationManagers\MemberBenefitRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMembers::route('/'),
            'create' => Pages\CreateMember::route('/create'),
            'edit' => Pages\EditMember::route('/{record}/edit'),
            'import' => Pages\ImportMembers::route('/import'),
            'excel-actions-benefit' => Pages\ExcelActionsBenefit::route('/excel-actions-benefit'),

        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
