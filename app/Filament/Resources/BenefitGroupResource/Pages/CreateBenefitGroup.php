<?php

namespace App\Filament\Resources\BenefitGroupResource\Pages;

use App\Filament\Resources\BenefitGroupResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
class CreateBenefitGroup extends CreateRecord
{
    protected static string $resource = BenefitGroupResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $record = parent::handleRecordCreation($data);

        Cache::member($record->member_id)->flush();

        return $record;
    }
}
