<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OrderResource\Pages;
use App\Filament\Resources\OrderResource\RelationManagers;
use App\Models\Order;
use App\Models\Member;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $navigationGroup = 'Orders';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('member_id')
                ->relationship('member', 'name'),
                Forms\Components\Select::make('season_id')
                ->relationship('season', 'name'),
                    Forms\Components\TextInput::make('member_balance')
                    ->maxLength(255),
                    Forms\Components\TextInput::make('ip')
                    ->maxLength(255),
                    Forms\Components\TextInput::make('grand_total')
                    ->maxLength(255),
                    Forms\Components\DateTimePicker::make('date'),
                    Forms\Components\Toggle::make('is_changed'),
                    Forms\Components\TextInput::make('real_balance')
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('date')
                ->sortable(),
                Tables\Columns\TextColumn::make('member_balance')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('member.name')->label('Member Name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('member.identity_number')->label('Identity Number')->sortable()->searchable(),

            ])
            ->defaultSort("date")
            // ->modifyQueryUsing(function (Builder $query){
            //     return $query->whereHas('member', function (Builder $query) {
            //         $query->where('is_approved', '!=', 0)
            //         ->whereNotNull('is_approved');
            //     });
            // })
            // ->query(function (Builder $query) {
            //     return $query->whereHas('member', function (Builder $query) {
            //         dd($query);
            //         $query->where('is_approved', '!=', 0)
            //         ->whereNotNull('is_approved');
            //     });
            // })
            ->reorderable("date")
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);

    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'create' => Pages\CreateOrder::route('/create'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
