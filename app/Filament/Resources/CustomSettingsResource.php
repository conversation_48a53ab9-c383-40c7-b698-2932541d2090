<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CustomSettingsResource\Pages;
use App\Filament\Resources\CustomSettingsResource\RelationManagers;
use App\Models\CustomSettings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Tables\Columns\TextColumn;

class CustomSettingsResource extends Resource
{
    protected static ?string $model = CustomSettings::class;

    protected static ?string $navigationIcon = 'heroicon-o-paint-brush';

    public static function form(Form $form): Form
    {
        return $form->schema(function ($record) {
            $key = $record->settings_key;

            if ($key == "site_language") {
                return [
                    Select::make('settings_value')->options([
                        'tr' => 'Türkçe',
                        'en' => 'İngilizce',
                        'tr,en' => 'Türkçe-İngilizce (Varsayılan dil Türkçe gelir, dil değişimi mümkün)',
                        'en,tr' => 'İngilizce-Türkçe (Varsayılan dil İngilizce gelir, dil değişimi mümkün)',
                    ]),
                ];
            } else if ($key == 'logo') {
                return [
                    FileUpload::make('settings_file')->label('Image')
                        ->image()
                        ->enableOpen()
                        ->enableDownload(),
                    FileUpload::make('settings_file_en')->label('Image/EN')
                        ->image()
                        ->enableOpen()
                        ->enableDownload(),
                ];
            } else if ($key == 'rules') {
                return [
                    Placeholder::make('info_text')
                        ->label('')
                        ->content('Kural 1: Çalışan hangi paketi seçerse bağımlıların sigorta paketi (eş ve çocuklar) da aynı olmak zorundadır. Farklı bir paket seçemezler. Sadece eş veya çocuklar için istemiyorum seçeneği seçilebilir.')
                        ->columnSpan('full'),
                    Placeholder::make('info_text')
                        ->label('')
                        ->content('Kural 2: Çalışan hangi paketi seçerse bağımlıların sigorta paketi (eş ve çocuklar) o paketle aynı veya daha düşük tutarlı olabilir. Eş ve çocuklar için çalışandan daha yüksek tutarlı bir paket seçilemez. Sadece eş veya çocuklar için istemiyorum seçeneği seçilebilir.')
                        ->columnSpan('full'),
                    Select::make('settings_value')->options([
                        '0' => 'Default',
                        '1' => 'Kural 1',
                        '2' => 'Kural 2',
                    ]),
                ];
            } else if ($key == 'project_components') {
                return [
                    Placeholder::make('info_text')
                        ->label('')
                        ->content('Bileşen 1: Satılabilir herhangi bir haktan elde edilen kazancın harcanması için alınabilir yan haklar kısıtlanabilir olmalıdır.')
                        ->columnSpan('full'),
                    Select::make('settings_value')->options([
                        '0' => 'Default',
                        '1' => 'Bileşen 1',
                    ]),
                ];
            } else if ($key == 'otp_active') {
                return [
                    Placeholder::make('info_text')
                        ->label('')
                        ->content('Login işlemleri esnasında OTP şartını aktif/deaktif eder.')
                        ->columnSpan('full'),
                    Select::make('settings_value')->options([
                        '0' => 'Pasif',
                        '1' => 'Aktif',
                    ]),
                ];
            } else if ($key == 'izin_hakki_text') {
                return [
                    TextInput::make('settings_value')
                        ->maxLength(255),
                    TextInput::make('settings_value_en')->label('Settings Value/EN')
                        ->maxLength(255),
                ];
            } else if ($key == 'izin_hakki_text_brut') {
                return [
                    TextInput::make('settings_value')
                        ->maxLength(255),
                    TextInput::make('settings_value_en')->label('Settings Value/EN')
                        ->maxLength(255),
                ];
            } else if ($key == 'contact_email_to') {
                return [
                    TextInput::make('settings_value')
                        ->maxLength(255),
                    TextInput::make('settings_value_en')->label('Settings Value/EN')
                        ->maxLength(255),
                ];
            } else if ($key == 'contact_email_cc') {
                return [
                    TextInput::make('settings_value')
                        ->maxLength(255),
                    TextInput::make('settings_value_en')->label('Settings Value/EN')
                        ->maxLength(255),
                ];
            } else if ($key == 'approve_email') {
                return [
                    TextInput::make('settings_value')
                        ->maxLength(255),
                    TextInput::make('settings_value_en')->label('Settings Value/EN')
                        ->maxLength(255),
                ];
            } else if ($key == 'beyanname_taahhutname') {
                return [
                    Forms\Components\RichEditor::make('settings_value')->label('Beyanname Taahhütname')
                        ->maxLength(65535),
                    Forms\Components\RichEditor::make('settings_value_en')->label('Beyanname Taahhütname/EN')
                        ->maxLength(65535),
                ];
            } else {
                return [
                    TextInput::make('settings_value')
                        ->maxLength(255),
                    TextInput::make('settings_value_en')->label('Settings Value/EN')
                        ->maxLength(255),
                ];
            }
        });
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('settings_key')->label('Key'),
                TextColumn::make('settings_value')->label('Değer')->limit(50),
            ])
            ->defaultSort('id', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomSettings::route('/'),
            'edit' => Pages\EditCustomSettings::route('/{record}/edit'),
        ];
    }

}
