<?php

namespace App\Filament\Resources\BenefitResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use App\Models\Benefit;
use App\Models\CustomBenefit;

class CustomBenefitsRelationManager extends RelationManager
{
    protected static string $relationship = 'customBenefits';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\CheckboxList::make('benefit_ids')
                ->label('Select Benefits')
                    ->options(function () {
                        return Benefit::where('benefit_group_id', 15)->pluck('name', 'id');
                    })
                    ->default(function () {
                        return Benefit::where('benefit_group_id', 15)->pluck('id')->toArray();
                    })
                    ->required(),
                Forms\Components\Hidden::make('benefit_group_id')
                    ->default(15),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('benefit_related_id')
                    ->label('Benefit Name')
                    ->getStateUsing(function (CustomBenefit $record) {
                        return Benefit::find($record->benefit_related_id)?->name;
                    }),
            ])
            ->filters([

            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->before(function (array $data) {
                        $parent_benefit_id = $this->ownerRecord->id;

                        $related_benefit_ids = $data['benefit_ids'];
                        $benefit_group_id = $data['benefit_group_id'];

                        if (!is_array($related_benefit_ids)) {
                            $related_benefit_ids = [$related_benefit_ids];
                        }

                        $existing_benefit_ids = CustomBenefit::where('benefit_id', $parent_benefit_id)
                            ->where('benefit_group_id', $benefit_group_id)
                            ->pluck('benefit_related_id')
                            ->toArray();

                        $benefits_to_add = array_diff($related_benefit_ids, $existing_benefit_ids);
                        foreach ($benefits_to_add as $benefit_related_id) {
                            $related_benefit = Benefit::find($benefit_related_id);

                            CustomBenefit::create([
                                'benefit_id' => $parent_benefit_id,
                                'benefit_group_id' => $benefit_group_id,
                                'benefit_price' => $related_benefit->benefit_price,
                                'benefit_name' => $related_benefit->name,
                                'benefit_name_en' => $related_benefit->name_en,
                                'benefit_related_id' => $benefit_related_id,
                            ]);
                        }

                        $benefits_to_remove = array_diff($existing_benefit_ids, $related_benefit_ids);
                        CustomBenefit::where('benefit_id', $parent_benefit_id)
                            ->where('benefit_group_id', $benefit_group_id)
                            ->whereIn('benefit_related_id', $benefits_to_remove)
                            ->delete();

                        return $data;
                    })
                    ->after(function ($record) {
                        if (is_null($record->benefit_related_id) || is_null($record->benefit_group_id)) {
                            $record->delete();
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

}
