<?php

namespace App\Filament\Resources;
use App\Filament\Resources\ReportResource\Pages;

use App\Models\Report;
use App\Models\Benefit;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;

use Illuminate\Support\Str;

class ReportResource extends Resource
{
    protected static ?string $model = Report::class;
    protected static ?string $navigationGroup = 'Site Management';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';



    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReports::route('/')
        ];
    }

     public static function table(Table $table): Table
    {

        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title')->sortable()->searchable(),
                ])
            ->filters([
                //
            ],

            layout: \Filament\Tables\Enums\FiltersLayout::AboveContent,
            )
            ->actions([
                Tables\Actions\Action::make('customAction')
                    ->url(fn ($record) => route('report.download', ['id' => $record->id]))
                    ->label('Raporu İndir')
                    ->icon('heroicon-m-cloud-arrow-down')
                    ->visible(fn ($record) => $record->id != 10),

                Tables\Actions\Action::make('specialReport')
                    ->label('Raporu İndir')
                    ->label('Raporu İndir')
                    ->icon('heroicon-m-cloud-arrow-down')
                    ->visible(fn ($record) => $record->id == 10)
                    ->form([
                        Forms\Components\Select::make('benefitId')
                            ->label('Hak Seçimi')
                            ->options(Benefit::all()->pluck('name', 'id'))
                            ->required(),
                    ])
                    ->action(function ($record, $data) {
                        $benefitId = $data['benefitId'];
                        // Formdan alınan benefitId ile rapor indirme URL'si oluşturulur
                        return redirect()->route('report.download', ['id' => $record->id, 'benefitId' => $benefitId]);
                    }),
            ]);

    }


    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

}
