<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BenefitResource\Pages;
use App\Filament\Resources\BenefitResource\RelationManagers;
use App\Models\Benefit;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BenefitResource extends Resource
{
    protected static ?string $model = Benefit::class;
    protected static ?string $navigationGroup = 'Members & Benefits';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('order_no')
                    ->numeric()
                    ->required()
                    ->columnSpan(2),
                Forms\Components\TextInput::make('name')
                    ->maxLength(255),
                Forms\Components\TextInput::make('name_en')->label('Name/EN')
                    ->maxLength(255),
                Forms\Components\TextInput::make('title')
                    ->maxLength(255),
                Forms\Components\TextInput::make('title_en')->label('Title/EN')
                    ->maxLength(255),
                Forms\Components\Select::make('benefit_group_id')
                    ->relationship('benefitGroup', 'name')
                    ->columnSpan(2),
                Forms\Components\RichEditor::make('description')
                    ->maxLength(65535),
                Forms\Components\RichEditor::make('description_en')->label('Description/EN')
                    ->maxLength(65535),
                Forms\Components\FileUpload::make('desc_image')->label('Description Image')
                    ->image()
                    ->enableOpen()
                    ->enableDownload(),
                Forms\Components\FileUpload::make('desc_image_en')->label('Description Image/EN')
                    ->image()
                    ->enableOpen()
                    ->enableDownload(),
                Forms\Components\RichEditor::make('alert_message')
                    ->maxLength(65535),
                Forms\Components\RichEditor::make('alert_message_en')->label('Alert Message/EN')
                    ->maxLength(65535),
                Forms\Components\Select::make('alert_bg_color')->label('Alert Background Color')->options([
                    null => 'Varsayılan',
                    'red' => 'Kırmızı',
                    'green' => 'Yeşil',
                ]),
                Forms\Components\Select::make('alert_bg_color_en')->label('Alert Background Color/EN')->options([
                    null => 'Varsayılan',
                    'red' => 'Kırmızı',
                    'green' => 'Yeşil',
                ]),
                Forms\Components\Select::make('alert_color')->label('Alert Color')->options([
                    null => 'Varsayılan',
                    'red' => 'Kırmızı',
                    'green' => 'Yeşil',
                ]),
                Forms\Components\Select::make('alert_color_en')->label('Alert Color/EN')->options([
                    null => 'Varsayılan',
                    'red' => 'Kırmızı',
                    'green' => 'Yeşil',
                ]),
                Forms\Components\FileUpload::make('image')
                    ->image()
                    ->enableOpen()
                    ->enableDownload(),
                Forms\Components\FileUpload::make('image_en')->label('Image/EN')
                    ->image()
                    ->enableOpen()
                    ->enableDownload(),
                Forms\Components\RichEditor::make('comment')
                    ->maxLength(65535),
                Forms\Components\RichEditor::make('comment_en')->label('Comment/EN')
                    ->maxLength(65535),
                Forms\Components\TextInput::make('price'),
                Forms\Components\Toggle::make('status')->reactive(),
                Forms\Components\Toggle::make('has_choice'),
                Forms\Components\Toggle::make('has_flexible_price'),
                Forms\Components\TextInput::make('flexible_price')
                    ->maxLength(255),
                Forms\Components\Select::make('type')->label('Hak Type')
                    ->options([
                        '1' => 'Temel Hak',
                        '2' => 'Alınabilir',
                        '3' => 'Satılabilir',
                        '4' => 'Sigorta',
                        '5' => 'İzin'
                    ])
                    ->required(),
                Forms\Components\TextInput::make('flexible_amount_title')
                    ->maxLength(255),
                Forms\Components\TextInput::make('flexible_amount_title_en')->label('Flexible Amount Title/EN')
                    ->maxLength(255),
                Forms\Components\Toggle::make('hide_if_not_predefined'),
                Forms\Components\Toggle::make('show_on_positive_balance'),
                Forms\Components\TextInput::make('price_original'),
                Forms\Components\Toggle::make('has_details')
                    ->columnSpan(2),
                Forms\Components\RichEditor::make('details')
                    ->maxLength(65535),

                Forms\Components\RichEditor::make('details_en')->label('Details/EN')
                    ->maxLength(65535),

                Forms\Components\Toggle::make('show_option_info'),
                Forms\Components\Toggle::make('convert_price'),
                Forms\Components\Toggle::make('equal_price'),
                Forms\Components\Toggle::make('really_equal_price'),
                Forms\Components\TextInput::make('min_amount'),
                Forms\Components\TextInput::make('max_amount'),
                Forms\Components\TextInput::make('benefit_fin_code'),
                Forms\Components\TextInput::make('benefit_fin_typ'),
                Forms\Components\TextInput::make('benefit_fin_nr'),
                Forms\Components\Select::make('benefit_bulutfin_mode')->label('Brut/Net Mod')
                    ->options([
                        '1' => 'Net',
                        '0' => 'Brut',
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_no')->sortable(),
                Tables\Columns\TextColumn::make('name')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('name_en')->label('Name/EN')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('benefitGroup.name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('benefitGroup.name_en')->label('Benefit Name/EN')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make('image')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make('image_en')->sortable()->searchable(),
            ])
            ->defaultSort("order_no")
            ->reorderable("order_no")
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ReplicateAction::make()
                ->excludeAttributes(['status'])
                // ->mutateRecordDataUsing(function (array $data): array {
                //     $data['user_id'] = auth()->id();

                //     return $data;
                // })
                ->successNotificationTitle('Başarıyla Kopyalandı'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\CustomBenefitsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBenefits::route('/'),
            'create' => Pages\CreateBenefit::route('/create'),
            'edit' => Pages\EditBenefit::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
