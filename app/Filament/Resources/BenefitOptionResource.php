<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BenefitOptionResource\Pages;
use App\Filament\Resources\BenefitOptionResource\RelationManagers;
use App\Models\BenefitOption;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BenefitOptionResource extends Resource
{
    protected static ?string $model = BenefitOption::class;
    protected static ?string $navigationGroup = 'Members & Benefits';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('benefit_id')
                    ->relationship('benefit', 'name')
                    ->required()
                    ->columnSpan(2),
                Forms\Components\TextInput::make('name')
                    ->maxLength(255)
                    ->required(),
                Forms\Components\TextInput::make('name_en')->label('Name/EN')
                    ->maxLength(255),
                Forms\Components\RichEditor::make('description')
                    ->maxLength(65535)
                    ->columnSpan(2),
                Forms\Components\RichEditor::make('description_en')->label('Description/EN')
                    ->maxLength(65535)
                    ->columnSpan(2),
                Forms\Components\Grid::make(5)->schema([
                    Forms\Components\RichEditor::make('alert_message')->maxLength(65535)->columnSpan(2),
                    Forms\Components\RichEditor::make('alert_message_en')->label('Alert Message/EN')->maxLength(65535)->columnSpan(2),
                    Forms\Components\ColorPicker::make('alert_color'),
                ]),
                Forms\Components\FileUpload::make('image')
                    ->image()
                    ->enableOpen()
                    ->enableDownload(),
                Forms\Components\FileUpload::make('image_en')->label('Image/EN')
                    ->image()
                    ->enableOpen()
                    ->enableDownload(),
                Forms\Components\RichEditor::make('comment')
                    ->maxLength(65535),
                Forms\Components\RichEditor::make('comment_en')->label('Comment/EN')
                    ->maxLength(65535),
                Forms\Components\TextInput::make('price'),
                Forms\Components\Toggle::make('status'),
                Forms\Components\Toggle::make('update_status')
                    ->required(),
                Forms\Components\TextInput::make('order_no')
                    ->required(),
                Forms\Components\Toggle::make('is_addon'),
                Forms\Components\TextInput::make('price_original'),
                Forms\Components\Toggle::make('has_details'),
                Forms\Components\RichEditor::make('details')
                    ->columnSpan(2)
                    ->maxLength(65535),
                Forms\Components\RichEditor::make('details_en')->label('Details/EN')
                    ->columnSpan(2)
                    ->maxLength(65535),
                Forms\Components\TextInput::make('benefit_fin_code'),
                Forms\Components\TextInput::make('benefit_fin_typ'),
                Forms\Components\TextInput::make('benefit_fin_nr'),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_no'),
                Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('name_en')->label('Name/EN')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('benefit.name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('benefit.name_en')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make('image')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make('image_en')->sortable()->searchable(),
            ])
            ->defaultSort("order_no")
            ->reorderable("order_no")
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),

                // Tables\Actions\ReplicateAction::make()
                // ->excludeAttributes(['status'])
                // ->mutateRecordDataUsing(function (array $data): array {
                //     $data['benefit_id'] = null;
                //     $data['update_status']= null;

                //     return $data;
                // })
                // ->successNotificationTitle('Başarıyla Kopyalandı'),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBenefitOptions::route('/'),
            'create' => Pages\CreateBenefitOption::route('/create'),
            'edit' => Pages\EditBenefitOption::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
