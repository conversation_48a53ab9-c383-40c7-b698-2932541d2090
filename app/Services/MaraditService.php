<?php

namespace App\Services;

use Exception;

class MaraditService
{
    protected $username;
    protected $password;
    protected $apiUrl;

    public function __construct()
    {
        $this->username = config('services.maradit.username');
        $this->password = config('services.maradit.password');
        $this->apiUrl = config('services.maradit.api_url');
        $this->from= config('services.maradit.from');
    }

    public function sendSMS($phoneNumber, $message, $format = 'xml')
    {
        try {
            if ($format == 'json') {
                $payload = $this->buildJSON($phoneNumber, $message);
                $contentType = 'application/json';
            } else {
                $payload = $this->buildXML($phoneNumber, $message);
                $contentType = 'text/xml';
            }

            $ch = curl_init($this->apiUrl);


            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: ' . $contentType]);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            curl_setopt($ch, CURLOPT_VERBOSE, true);

            $response = curl_exec($ch);




            if ($response == false) {
                throw new Exception('CURL Hatası: ' . curl_error($ch));
            }


            curl_close($ch);

            if ($format == 'json') {
                $response = json_decode($response, true);
            }

            if ($format == 'xml') {
                $response = simplexml_load_string($response);
            }

            return $response;
        } catch (Exception $e) {

            return ['error' => $e->getMessage()];
        }
    }


    private function buildXML($phoneNumber, $message)
    {
        return <<<XML
            <Submit xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.maradit.net/api/types">
              <Credential>
                <Password>{$this->password}</Password>
                <Username>{$this->username}</Username>
              </Credential>
              <DataCoding>Default</DataCoding>
              <Header>
                <From>{$this->from}</From>
                <Route>0</Route>
                <ScheduledDeliveryTime>0001-01-01T00:00:00</ScheduledDeliveryTime>
                <ValidityPeriod>0</ValidityPeriod>
              </Header>
              <Message>{$message}</Message>
              <To xmlns:d2p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
                <d2p1:string>{$phoneNumber}</d2p1:string>
              </To>
            </Submit>
        XML;
    }

    private function buildJSON($phoneNumber, $message)
    {
        return json_encode([
            "Submit" => [
                "Credential" => [
                    "Password" => $this->password,
                    "Username" => $this->username,
                ],
                "DataCoding" => "Default",
                "Header" => [
                    "From" => "YOUR_SMS_HEADER",
                    "Route" => 0,
                    "ScheduledDeliveryTime" => "0001-01-01T00:00:00",
                    "ValidityPeriod" => 0
                ],
                "Message" => $message,
                "To" => [
                    "d2p1:string" => [$phoneNumber]
                ]
            ]
        ], JSON_PRETTY_PRINT);

    }

}
