<?php
namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Arr;

class BordroService
{
    protected string $baseUrl;
    protected array  $endpoints;
    protected array  $required;
    protected array  $resolvers;

    public function __construct()
    {
        $this->baseUrl   = config('bulutfin.base_url');
        $this->endpoints = config('bulutfin.endpoints');
        $this->required  = config('bulutfin.required_params');
        $this->resolvers = config('bulutfin.param_resolvers');
    }

    /**
     * @param string $key
     * @param array  $overrides
     */

    public function request(string $key, array $overrides = []): array
    {
        if (! isset($this->endpoints[$key])) {
            throw new \InvalidArgumentException("Undefined endpoint: {$key}");
        }

        $ep     = $this->endpoints[$key];
        $method = strtolower($ep['method']);
        $uri    = $ep['uri'];

        $reqs = array_merge(
            Arr::wrap($this->required['*'] ?? []),
            Arr::wrap($this->required[$key] ?? [])
        );

        foreach ($reqs as $param) {
            if (! array_key_exists($param, $overrides) || is_null($overrides[$param])) {
                $overrides[$param] = call_user_func($this->resolvers[$param]);
            }
        }

        $url = $this->baseUrl . $uri . '?' . http_build_query($overrides);

        try {
            $response = Http::retry(2, 200)
                ->acceptJson()
                ->timeout(5)
                ->{$method}($url);

            $json = $response->json();

            if ($response->successful()) {
                // API "success" diyorsa olduğu gibi dön
                return $json;
            }

            // API'den hata dönse bile JSON içinde mesaj varsa çek
            $error = [
                'success' => false,
                'error_message' => $json['error_description'] ?? $json['message'] ?? 'Beklenmeyen bir hata oluştu.',
                'error_code' => $json['error_code'] ?? null,
            ];

            return $error;

        } catch (\Throwable $e) {
            return [
                'success' => false,
                'error_message' => 'BulutFin API bağlantı hatası: ' . $e->getMessage(),
            ];
        }
    }
}
