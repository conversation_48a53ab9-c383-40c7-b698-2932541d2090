<?php

namespace App\Http\Controllers;

use App\Models\CustomBenefit;
use App\Models\CustomSettings;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

use Illuminate\Support\Facades\Auth;
use App\Models\Slider;
use App\Models\BenefitGroup;
use App\Models\Benefit;
use App\Models\Member;
use App\Models\BenefitOption;
use App\Models\Order;
use App\Models\MemberBenefit;
use App\Models\FamilyBenefit;
use App\Models\Question;
use App\Models\MemberBenefitCart;
use App\Models\Announcement;
use Illuminate\Support\Facades\DB;
use Session;
use Carbon\Carbon;
use App\Services\BordroService;

class PaymentController extends Controller
{
    protected BordroService $bulutfin;

    public function __construct(BordroService $bulutfin)
    {
        $this->bulutfin = $bulutfin;
    }

    public function init()
    {
        // Üye bilgileri
        $member    = Member::find(Auth::guard('member')->id());
        $member_id = $member->id;
        $user_info = $member->toArray();
        $memberPct = $member->percentage;

        // Final faydaları
        $m_benefit           = new Benefit();
        $finalBenefits       = $m_benefit->getFinalBenefits($member_id);

        $finalFamilyBenefits = $m_benefit->getFinalFamilyBenefits($member_id);
        $finalFamilyTotals   = $m_benefit->getFinalFamilyBenefitsTotalsByGroup($member_id);

        // Seçili option'ların kodlarını al
        $optionIds = $finalBenefits
            ->pluck('option_id')
            ->filter()
            ->unique()
            ->all();

        $optionCodes = BenefitOption::whereIn('id', $optionIds)
            ->pluck('benefit_fin_code', 'id')
            ->all();

        $resp   = $this->bulutfin->request('yillikozet');
        $data   = data_get($resp, 'Data', []);
        $yanHak = data_get($data, 'YanHakSumList', []);
        $hsoh   = data_get($data, 'HSOHSumList', []);
        $allBF  = array_merge($yanHak, $hsoh);

        $groupedByCode = collect($allBF)
            ->groupBy('CODE')
            ->map(fn($items) => [
                'priceBrut' => $items->sum('YanHakBurut'),
                'priceNet'  => $items->sum('YanHakNet'),
            ])
            ->all();

        $groupedBenefits    = [];
        $totalPricesByGroup = [];

        $izinLedgerByBenefitId = \DB::table('member_benefit_balance_ledger')
            ->where('member_id', $member_id)
            ->where('fin_type', 'izin_to_balance')
            ->get()
            ->keyBy('benefit_id');

        foreach ($finalBenefits as $b) {
            $b->benefit_fin_code_option = $optionCodes[$b->option_id] ?? null;
            $gid = $b->group_id;
            if (empty($gid)) continue;

            $code = $b->option_id ? $b->benefit_fin_code_option : $b->benefit_fin_code;

            // Ledger varsa ve type == 5 ise öncelikli olarak ordan çek
            if ($b->type == 5 && isset($izinLedgerByBenefitId[$b->benefit_id])) {
                $ledger = $izinLedgerByBenefitId[$b->benefit_id];
                $priceBrut = floatval($ledger->price_brut);
                $priceNet  = floatval($ledger->price_net);
            } elseif (!empty($b->priceBrut)) {
                $priceBrut = $b->priceBrut;
                $priceNet  = $b->priceNet;
            } elseif (!empty($code) && isset($groupedByCode[$code])) {
                $priceBrut = $groupedByCode[$code]['priceBrut'];
                $priceNet  = $groupedByCode[$code]['priceNet'];
            } else {
                $priceBrut = 0;
                $priceNet  = 0;
            }

            // Fiyatlar sıfırsa bu hakkı atla
            if ($priceBrut == 0 && $priceNet == 0) {
                continue;
            }

            $b->priceBrut = $priceBrut;
            $b->priceNet  = $priceNet;

            $groupedBenefits[$gid][] = $b;

            $totalPricesByGroup[$gid] = ($totalPricesByGroup[$gid] ?? 0) + $priceBrut;
        }
        foreach ($finalFamilyTotals as $groupId => $famTot) {
            $totalPricesByGroup[$groupId] = ($totalPricesByGroup[$groupId] ?? 0) + $famTot;
        }

        $rawBalance = $m_benefit->getMemberBalance($member_id);
        $balance = is_array($rawBalance)
            ? $rawBalance
            : ['balance' => floatval($rawBalance), 'brut_balance' => floatval($rawBalance)];

        $excludedGroups = ['NR01'];
        $filteredTotalPrices = array_filter($totalPricesByGroup, function ($_, $key) use ($excludedGroups) {
            return !in_array($key, $excludedGroups);
        }, ARRAY_FILTER_USE_BOTH);
        return [
            'is_changed'         => $m_benefit->getIfIsChanged($member_id),
            'final_benefits'     => $finalBenefits,
            'family_benefits'    => $finalFamilyBenefits,
            'balance'            => $balance,
            'grouped_benefits'   => $groupedBenefits,
            'totalPricesByGroup' => $totalPricesByGroup,
            'grand_total'        => array_sum($totalPricesByGroup),
            'groupNames'         => $m_benefit->getGroupNames(),
            'groupNamesEng'      => $m_benefit->getGroupNamesEng(),
            'user_info'          => $user_info,
        ];
    }
    public function getInitData()
    {
        $data = $this->init();
        return response()->json($data);
    }

    public function checkAction(Request $request)
    {

        $member = Member::where('id', Auth::guard('member')->id())->first();

        if ($member) {
            $member_id = $member->id;
        }


        $m_benefit = new Benefit;
        $member_balance = $m_benefit->getMemberBalance($member_id);

        $finalBenefits = $m_benefit->getFinalBenefits($member_id);
        $selected_rule = session('selected_rule');

        $componentsValue = CustomSettings::getComponents();

        if($componentsValue==1 && $member_balance==0){
            $customRuleMsg=true;
            $benefitsCart = new MemberBenefitCart();
            $customBenefits = CustomBenefit::getAllCustomBenefits();
            $custom_benefit_and_related_id=[];
            foreach($customBenefits as $key => $value){
                if (!in_array($value->benefit_related_id, $custom_benefit_and_related_id)) {
                    $custom_benefit_and_related_id[] = $value->benefit_related_id;
                }

                if (!in_array($value->benefit_id, $custom_benefit_and_related_id)) {
                    $custom_benefit_and_related_id[] = $value->benefit_id;
                }
            }

            $nonCustomBenefitCart = $benefitsCart->where('member_id', $member_id)
                ->whereNotIn('benefit_id', $custom_benefit_and_related_id)
                ->where('benefit_id', '!=', 149)
                ->get()
                ->toArray();


            $total_sum=0;
            foreach($nonCustomBenefitCart as $value)
            {
                $total_sum+=$value['amount'];
            }



            $non_related_sales = DB::table('custom_benefits_sale')
                ->where('member_id', $member_id)
                ->whereNull('related_sale')
                ->get();

            $related_sales = DB::table('custom_benefits_sale')
                ->where('member_id', $member_id)
                ->whereNull('non_related_sale')
                ->get();



            $initial_balance=$member->positive_balance;
            $non_related_sales_gain=0;
            foreach ($non_related_sales as $sales){
                $non_related_sales_gain+=$sales->non_related_sale;
            }

            $related_sales_gain=0;
            foreach ($related_sales as $sales){
                $related_sales_gain+=$sales->related_sale;
            }


            $custom_benefits_name=[];
            foreach ($customBenefits as $key => $custom_benefit) {
                $existing_names = array_column($custom_benefits_name, 'name');
                $existing_names_en = array_column($custom_benefits_name, 'name_en');

                if (!in_array($custom_benefit['benefit_name'], $existing_names) && !in_array($custom_benefit['benefit_name'], $existing_names_en)) {
                    $custom_benefits_name[$key] = [
                        'name' => $custom_benefit['benefit_name'],
                        'name_en' => $custom_benefit['benefit_name'],
                    ];
                }
            }

            $benefit_names = array_column($custom_benefits_name, 'name');
            $benefit_names_en = array_column($custom_benefits_name, 'name_en');

            $related_benefits = implode(', ', $benefit_names);
            $related_benefits_en = implode(', ', $benefit_names_en);

            $aktarilmasi_gereken_tutar=$total_sum-($initial_balance);


            $message = sprintf(
                "Alışverişe devam edebilmek için toplam harcamanızdan en az  %s TL kadarını %s 'e aktarmalısınız",
                $related_sales_gain,
                $related_benefits
            );


            $message_en = sprintf(
                "To continue shopping, you must transfer at least %s TL of your total spending to %s",
                $related_sales_gain,
                $related_benefits
            );




            if($aktarilmasi_gereken_tutar > 0 && $related_sales_gain > 0)
            {
                return response()->json([
                    'component1Active' => true,
                    'customRuleMsg' => $customRuleMsg,
                    'message' => $message,
                    'message_en' =>$message_en
                ]);
            }

        }

        $custom_control = [];
        $customRuleMsg = true;
        $rule2Active = $selected_rule == "2";
        $customDetected = false;

        //burda project component 1 varsa custom balance'ı al bu custom balance'dan besi çıkar - oluyorsa kabul etme
        //veya can be negative yoksa vsvs kontrol burda olacak.

        if ($rule2Active) {
            foreach ($finalBenefits as $benefit) {
                if (in_array($benefit->id, ["158", "159", "160"])) {
                    $custom_control[] = $benefit->option_id;
                }
            }

            $allowedCombinations = [
                ['270', '280', '327'],
                ['274', '284', '331'],
                ['278', '335', '336'],
                ['305', '306', '307']
            ];

            $optionalElements = ['289', '326', '279'];

            foreach ($allowedCombinations as $combination) {
                if (count(array_intersect($custom_control, $combination)) == count($combination)) {
                    $customRuleMsg = false;
                    break;
                }

                // Check for combinations with one element replaced by an optional element
                foreach ($combination as $element) {
                    if (!in_array($element, $custom_control)) {
                        foreach ($optionalElements as $optionalElement) {
                            if (in_array($optionalElement, $custom_control)) {
                                $newCombination = array_replace($combination, [$element => $optionalElement]);
                                if (count(array_intersect($newCombination, $custom_control)) == count($combination)) {
                                    $customRuleMsg = false;
                                    break;
                                }
                            }
                        }
                    }
                    if (!$customRuleMsg) break;
                }
            }

            if ($customRuleMsg) {
                return response()->json([
                    'rule2Active' => true,
                    'customRuleMsg' => $customRuleMsg,
                    'message' => 'Çalışan hangi paketi seçerse bağımlıların sigorta paketi (eş ve çocuklar) o paketle aynı veya daha düşük tutarlı olabilir. Eş ve çocuklar için çalışandan daha yüksek tutarlı bir paket seçilemez. Sadece eş veya çocuklar için istemiyorum seçeneği seçilebilir.',
                    'message_en' =>"Whichever package the employee chooses, the insurance package of dependents (spouse and children) may be the same or lower than that package. A package with a higher amount than the employee cannot be selected for spouse and children. The I don't want option can be selected only for spouse or children."
                ]);
            }
        }







        if ($member_balance['balance'] < 0 && $member_balance['brut_balance'] < 0) {
            $member_balance_with_negatives = $member_balance['balance'];

            $m_benefit_group = new BenefitGroup;
            $groups = $m_benefit_group->getGroupsAllowNegative();

            if (empty($groups)) {
                return response()->json(['error' => 'Eksi bakiye kabul edilmemektedir !'], 400);
            }

            foreach ($groups as $group) {
                $group_balance = $m_benefit->getMemberBalanceByGroupId($member_id, $group['id']);

                $group_balance = sprintf('%.2f', $group_balance);

                $member_balance_with_negatives -= $group_balance;
            }

            if ($member_balance_with_negatives < 0) {
                $names = $groups->pluck('name', 'name_en')->all();

                $names_string_en = implode(', ', array_keys($names));
                $names_string_tr = implode(', ', array_values($names));

                // session(['can_be_negative' => 0]);

                if (empty($names)) {
                    session(['can_be_negative' => 0]);
                    // $message = "Eksi bakiye ile alışverişi tamamlayabilmeniz için aşağıdaki koşulları yerine getirdiğinizi lütfen kontrol ediniz:";
                    if (App::getLocale() === 'en') {
                        $message = "Negative balance is not accepted.";
                    } else {
                        $message = "Eksi bakiye kabul edilmemektedir !";
                    }

                } else {
                    session(['can_be_negative' => 0]);
                    if (App::getLocale() === 'en') {
                        $message = "Negative balance is not accepted.\r\n
                        - You can only go into negative balance in the {$names_string_en} group.\r\n
                        - If you have chosen options from other right groups along with {$names_string_en}, BFLEX does not allow you to go into negative balance. You need to waive rights from other groups to the extent of the negative balance amount.\r\n";

                    } else {
                        $message = "Eksi bakiye kabul edilmemektedir !\r\n
                        - Yalnızca {$names_string_tr} grubunda eksi bakiyeye düşebilirsiniz. \r\n
                        - {$names_string_tr} ile birlikte başka hak gruplarından seçim yaptıysanız, BFLEX eksi bakiyeye düşmenize izin vermemektedir. Oluşan eksi bakiye tutarı kadar başka hak gruplarından feragat etmeniz gerekmektedir.\r\n";
                    }


                }

                return response()->json([
                    'message' => $message,
                    'eksiBakiye' => true,
                    'checkStep' => true
                ]);
            } else {
                session(['can_be_negative' => 1]);

                if (App::getLocale() === 'en') {
                    $message = 'You have a negative balance of ' . abs($member_balance['balance']) . ' TL in your account. This amount will be deducted from your salary in 5 equal installments. Are you sure you want to complete the purchase?';

                } else {
                    $message = 'Kasanda ' . abs($member_balance['balance']) . ' TL kadar eksi bakiye bulunmaktadır. Bu tutar 5 eşit taksitte maaşınızdan düşecektir. Alışverişi tamamlamak istediğinize emin misiniz?';

                }
                session(['negative_member_balance' => abs($member_balance['balance'])]);

                return response()->json([
                    'message' => $message,
                    'eksiBakiye' => true,
                    'checkStep' => false,
                    'member_balance' => $member_balance['balance']
                ]);
            }
        } else if ($member_balance['balance']  > 0 && $member_balance['brut_balance'] > 0) {
            session(['can_be_negative' => 0]);
            return response()->json([
                'message' => 'Fazla hak bulunuyor.',
                'positive_balance' => true,
                'member_balance' => $member_balance['balance']
            ]);
        }else {
            session(['can_be_negative' => 0]);
            return response()->json(['message' => 'Başarıyla onaylanmıştır.']);
        }

        // $is_lightbox = $request->input('is_lightbox');
        // if ($member_balance > 0) {
        //     return response()->json([
        //     'message' => 'Fazla hak bulunuyor.',
        //     'positive_balance' => true,
        //     'member_balance' => $member_balance
        // ]);
        // }


    }

    public function approve(Request $request)
    {
        $member = Member::where('id', Auth::guard('member')->id())->first();

        if ($member) {
            $member_id = $member->id;
        } else {
            die;
        }

        $benefit = new Benefit();
        $member_balance = $benefit->getMemberBalance($member_id);

        if ($member_balance['balance'] < 0 && $member_balance['brut_balance'] < 0) {
            // Eksi bakiye işlemleri
            $m_benefit_group = new BenefitGroup();
            $groups = $m_benefit_group->getGroupsAllowNegative();

            if ($groups->isEmpty()) {
                return response()->json(['error' => 'Eksi bakiye kabul edilmemektedir !'], 422);
            }

            foreach ($groups as $group) {
                $group_balance = $benefit->getMemberBalanceByGroupId($member_id, $group->id);
                $member_balance['balance'] -= $group_balance;
            }

            if ($member_balance['balance'] < 0 && $member_balance['brut_balance'] < 0) {
                $names = $groups->pluck('name')->toArray();
                return response()->json(['error' => join(', ', $names) . ' haricindeki hak gruplarında eksi bakiye kabul edilmemektedir !'], 422);
            }
        }

        if ($member_balance['balance'] > 0 && $member_balance['brut_balance'] > 0) {
            // Artı bakiye işlemleri
            // İşlemleri buraya ekleyebilirsiniz.
        }

        // Siparişi oluştur
        $order = new Order();

        $order->createOrder($member_id, $member_balance['balance'], $member_balance['brut_balance']);

        // Kullanıcı bilgilerini güncelle
        $member = Member::find($member_id );
        $member->is_approved = 1;
        $member->approval_date = Carbon::now()->timestamp;
        $member->save();

        // Session::put("send_email", true);

        return response()->json(['success' => 'Başarıyla onaylanmıştır.']);
    }
}
