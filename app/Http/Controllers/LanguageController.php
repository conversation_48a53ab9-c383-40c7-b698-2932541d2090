<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    public function setLanguage($language, Request $request) {

        $availableLocales = ['en', 'tr'];

        if (in_array($language, $availableLocales)) {
            session(['app_language' => $language]);
            App::setLocale($language);
        }

        return redirect()->back();
    }
}
