<?php

namespace App\Http\Controllers;
use Illuminate\Support\Arr;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Slider;
use App\Models\Benefit;
use App\Models\Member;
use App\Models\MemberBenefit;
use App\Models\FamilyBenefit;
use App\Models\MemberBenefitCart;
use App\Models\MemberBenefitPref;
use App\Models\WelcomeMessage;
use App\Models\Announcement;
use App\Models\CustomSettings;
use App\Models\CustomBenefit;

use App\Models\WorkRules;

use App\Models\CustomBenefitsSale;
use App\Models\MemberDashboard;
use Illuminate\Support\Facades\Cache;
use App\Services\BordroService;
use Illuminate\Support\Collection;
use App\Models\MemberBenefitBalanceLedger;
use App\Helpers\BordroHelper;

class MemberDashboardController extends Controller
{
    protected BordroService $bulutfin;

    public function __construct(BordroService $bulutfin)
    {
        $this->bulutfin = $bulutfin;
    }

    public function show()
    {
        //RULES VALUE
        $rulesValue = CustomSettings::getRules();
        //RULES VALUE

        //PROJECT COMPONENTS VALUE
        $componentsValue = CustomSettings::getComponents();
        //PROJECT COMPONENTS VALUE



        // SLIDERS
        $sliders = Cache::remember('sliders', 3600, function () {
            return Slider::all();
        });

        // WELCOME MESSAGES
        $welcome_messages = Cache::remember('welcome_messages', 3600, function () {
            return WelcomeMessage::orderBy('order_no', 'asc')->get();
        });



        $member_id = Auth::guard('member')->id();


        //$yillikOzet = $this->bulutfin->get('yillikozet');


        $member = Cache::member($member_id)
            ->remember("member_{$member_id}", 3600, function () use ($member_id) {
                return Member::find($member_id);
            });

        if ($member) {
            $user_info = $member->toArray();
        }

        if ($user_info['is_approved'] == 1) {
            return redirect()->route('approval');
        }


        //$response = $this->bulutfin->request('sifirla');
        //dd($response);


        $m_benefit = new Benefit();
        $benefits = Cache::member($member_id)
            ->remember("member_benefits_{$member_id}", 3600, function () use ($m_benefit, $member_id) {
                return $m_benefit->getAll($member_id);
            });



        $memberBenefit = new MemberBenefit();
       $predefinedBenefits = $memberBenefit->getBenefits($member_id);


        $ozelSaglik = $memberBenefit->getOzelBenefit($member_id);




        $groupOneGeneralInfo = $predefinedBenefits->first(function ($benefit) {
            return $benefit['group_id'] == '1';
        });

        // Grup '1' için temel hakları ve esnek fiyatları toplayalım.
        $temelHaklar = [];
        $predefinedFlexiblePriceValues = [];
        foreach ($predefinedBenefits as $predefinedBenefit) {
            if ($predefinedBenefit['group_id'] == '1') {
                $temelHaklar[] = $predefinedBenefit->toArray(); // Eloquent modelini diziye çevir
            }

            if ($predefinedBenefit['has_flexible_price']) {
                $predefinedFlexiblePriceValues[$predefinedBenefit['benefit_id']] = $predefinedBenefit['amount'];
            }
        }



        $benefits['1'] = [
            'id' => $groupOneGeneralInfo->group_id ?? 1,
            'order_no' => $groupOneGeneralInfo->order_no ?? 1,
            'name' => $groupOneGeneralInfo->group_name ?? 'Değiştirilemez Yan Haklar',
            'name_en' => $groupOneGeneralInfo->group_name_en ?? 'Core Benefits',
            // 'name' => $groupOneGeneralInfo->group_name ?? 'Temel Haklar Grubu',
            // 'name_en' => $groupOneGeneralInfo->group_name_en ?? 'Temel Haklar Grubu',
            'description' => $groupOneGeneralInfo->group_description ?? 'Grup açıklaması',
            'description_en' => $groupOneGeneralInfo->group_description_en ?? 'Group Explanation',
            'status' => $groupOneGeneralInfo->status ?? 2,
            'group_image' => $groupOneGeneralInfo->group_image ?? 'default-image.png',
            'group_image_en' => $groupOneGeneralInfo->group_image_en ?? 'default-image.png',
            'image' => $groupOneGeneralInfo->image ?? 'default-image.png',
            'image_en' => $groupOneGeneralInfo->image_en ?? 'default-image.png',
            'benefits' => $temelHaklar,

        ];


        $finalBenefits = $m_benefit->getFinalBenefits($member_id);

        $allFamilyBenefits = $m_benefit->getFamilyBenefits($member_id);

        $familyBenefit = new FamilyBenefit();
        $finalFamilyBenefits = $familyBenefit->getFinalFamilyBenefits($member_id);

        $selecteds = [];
        $values = [];

        $selectedCustom = session('selected_custom', []);

        foreach ($finalBenefits as $benefit) {
            $selecteds[$benefit->benefit_id] = $benefit->option_id ? $benefit->option_id : 1;

            if ($benefit->has_flexible_price) {
                $values[$benefit->benefit_id] = $benefit->amount;
            }
        }


        foreach ($benefits as $group_id => &$group) {


            // Eğer 'benefits' anahtarı varsa ve bir dizi ise işlemleri yap
            if (isset($group['benefits']) && is_array($group['benefits'])) {

                foreach ($group['benefits'] as $key => $benefit) {
                    // Eğer benefit 'hide_if_not_predefined' true ve predefinedBenefits içinde yoksa
                    if ($benefit['hide_if_not_predefined'] && !isset($predefinedBenefits[$key])) {
                        unset($group['benefits'][$key]); // Bu benefit'i sil
                    }
                }

                // Eğer bu grupta hiç benefit kalmadıysa, bu grubu da sil
                if (empty($group['benefits'])) {
                    unset($benefits[$group_id]);
                } else {
                    $group['benefits'] = array_values($group['benefits']); // Anahtarları sıralı hale getir
                }
            }
        }

        unset($group);


        $predefinedBenefitIds = $predefinedBenefits->pluck('id')->all();

        foreach ($benefits as $group_id => $benefit_group) {
            if (isset($benefit_group['benefits'])) {  // Eğer bu grup içinde benefits varsa
                $uniqueIds = []; // Benzersiz benefit id'lerini tutmak için

                foreach ($benefit_group['benefits'] as $key => $benefit) {

                    // Eğer bu benefit 'predefined' olarak işaretlenmişse ve predefinedBenefits içinde yer almıyorsa
                    if ($benefit['hide_if_not_predefined'] && !in_array($benefit['id'], $predefinedBenefitIds)) {
                        // Bu benefiti $benefits dizisinden sil
                        unset($benefits[$group_id]['benefits'][$key]);
                    } else {
                        // Aynı id'ye sahip birden fazla benefit varsa, sadece ilkini tut
                        if (in_array($benefit['id'], $uniqueIds)) {
                            // Aynı id'den zaten varsa, bu benefiti sil
                            unset($benefits[$group_id]['benefits'][$key]);
                        } else {
                            // İlk kez görülen id'yi uniqueIds dizisine ekle
                            $uniqueIds[] = $benefit['id'];
                        }
                    }
                }

                // İndeksleri yeniden sırala
                $benefits[$group_id]['benefits'] = array_values($benefits[$group_id]['benefits']);
            }
        }




        foreach ($allFamilyBenefits as $benefit_id => $familyBenefit) {
            foreach ($familyBenefit as $member_family_id => $memberFamilyBenefit) {
                if ($memberFamilyBenefit->prerequisite_benefit_id) {
                    if (!isset($finalFamilyBenefits[$memberFamilyBenefit->prerequisite_benefit_id][$member_family_id]) || $finalFamilyBenefits[$memberFamilyBenefit->prerequisite_benefit_id][$member_family_id]->real_price == '0.00') {
                        unset($allFamilyBenefits[$benefit_id][$member_family_id]);
                    }
                }
            }
        }


        //Data gelisi temizlendi ve daha sade hale getirildi.
        $opened = request()->input('opened') ? explode(',', request()->input('opened')) : [];

        $paymentData = app(PaymentController::class)->init();

        foreach ($benefits as $key => $value) {
            if (empty($value['benefits'])) {
                unset($benefits[$key]);
            }
        }

        //VIP OPTION ICIN GECICI COZUM
        if (!array_key_exists(18, $benefits)) {
            if (array_key_exists(17, $benefits)) {
                $newOptions = [];
                foreach ($benefits[17]['benefits'][0]['options'] as $option) {
                    if (strpos(strtoupper($option['name']), 'VIP') === false) {
                        $newOptions[] = $option;
                    }
                }
                $benefits[17]['benefits'][0]['options'] = $newOptions;
            }
        }
        //VIP OPTION ICIN GECICI COZUM

        if(!empty($selectedCustom)){
            foreach ($selectedCustom as $key => $value) {
                if (isset($selecteds[$key])) {
                    $selecteds[$key] = $value;
                }
            }
        }


        $memberBenefit = new MemberBenefit();
        $balance = $m_benefit->getMemberBalance($member_id);

        $memberBenefitPref = new MemberBenefitPref();
        $memberBenefitOptionIds_1 = $memberBenefitPref->getVisibleBenefitOptionsByMemberId($member_id);
        $memberBenefitOptionIds_2 = $memberBenefit->getSelectedBenefitOptionsByMemberId($member_id);

        $memberBenefitOptionIds = $memberBenefitOptionIds_1->merge($memberBenefitOptionIds_2)->unique();

        $memberBenefitOptions = $memberBenefit->getSelectedBenefitOptionsAmountByMemberId($member_id);

        foreach ([17, 18] as $key) {
            if (isset($benefits[$key])) {
                $benefits[$key]['benefits'] = collect($benefits[$key]['benefits'])->map(function ($benefit) use ($memberBenefitOptionIds) {
                    $benefit['options'] = collect($benefit['options'])->filter(function ($option) use ($memberBenefitOptionIds) {
                        return $memberBenefitOptionIds->contains($option['id']);
                    })->toArray();
                    return $benefit;
                })->toArray();
            }
        }


        foreach([14,17,18] as $key)
        {
            if(isset($benefits[$key])){
                foreach( $benefits[$key]['benefits'] as $k => $options){
                    foreach($options['options'] as $t => $option){
                        foreach($memberBenefitOptions as $o => $op)
                        {
                            if($option['id']==$o){
                                $option['price']=$op;
                                $benefits[$key]['benefits'][$k]['options'][$t] = $option;
                            }
                        }
                    }
                }
            }
        }

        if (isset($benefits[18]['benefits']) && !empty($benefits[18]['benefits'])) {
            if(isset($benefits[19])){
                unset($benefits[19]);
            }
        }

        if (!empty($benefits[1]['benefits']) && is_array($benefits[1]['benefits'])) {
            usort($benefits[1]['benefits'], function ($a, $b) {
                return $a['order_no'] <=> $b['order_no'];
            });
        }


//        usort($benefits[1]['benefits'], function ($a, $b) {
//            return $a['order_no'] <=> $b['order_no'];
//        });



        $response = $this->bulutfin->request('yillikozet');

        $data = data_get($response, 'Data', []);
        $brutMaas = data_get($data, 'BrutMaas', 0);
        $netMaas = data_get($data, 'NetMaas', 0);

        $errorMessage = data_get($response, 'ErrorMessage');

        $yanHakList = data_get($data, 'YanHakSumList', []);
        $hsohList   = data_get($data, 'HSOHSumList', []);
        $allBenefits = array_merge($yanHakList, $hsohList);

        $groupedByCode = collect($allBenefits)
            ->groupBy('CODE')
            ->map(fn($items) => $items->values()->all())
            ->all();

        foreach ($benefits as &$group) {
            foreach ($group['benefits'] as &$benefit) {
                $code = $benefit['benefit_fin_code'];

                // Önce options'ları işleyelim
                if (! empty($benefit['options']) && is_array($benefit['options'])) {
                    foreach ($benefit['options'] as &$opt) {
                        $optCode = $opt['benefit_fin_code'];
                        if ($optCode !== null && isset($groupedByCode[$optCode])) {
                            $optItems = $groupedByCode[$optCode];
                            $opt['priceBrut'] = array_sum(array_column($optItems, 'YanHakBurut'));
                            $opt['priceNet']  = array_sum(array_column($optItems, 'YanHakNet'));
                        } else {
                            $opt['priceBrut'] = 0;
                            $opt['priceNet']  = 0;
                        }
                    }
                    unset($opt);
                }

                // Eğer benefit'in kodu option'lar tarafından zaten kapsanıyorsa, benefit için fiyat yazma
                $isCodeInOptions = false;
                if ($code !== null && !empty($benefit['options'])) {
                    foreach ($benefit['options'] as $opt) {
                        if ($opt['benefit_fin_code'] === $code) {
                            $isCodeInOptions = true;
                            break;
                        }
                    }
                }

                if ($code !== null && !$isCodeInOptions && isset($groupedByCode[$code])) {
                    $items = $groupedByCode[$code];
                    $benefit['priceBrut'] = array_sum(array_column($items, 'YanHakBurut'));
                    $benefit['priceNet']  = array_sum(array_column($items, 'YanHakNet'));
                } else {
                    $benefit['priceBrut'] = 0;
                    $benefit['priceNet']  = 0;
                }
            }
            unset($benefit);
        }
        unset($group);
        if ($errorMessage) {
            logger()->warning('[Bulutfin Error]', ['message' => $errorMessage]);
        }

        foreach ($benefits as $groupId => &$grp) {
            $totalBrut = 0;
            $totalNet  = 0;

            foreach ($grp['benefits'] as $b) {
                // 1) Eğer alt seçenekler (options) varsa, sadece seçili olan option'ın BulutFin fiyatlarını kullan
                if (! empty($b['options'])) {
                    $selOptId = $selecteds[$b['id']] ?? null;
                    if ($selOptId) {
                        $opt = collect($b['options'])->firstWhere('id', $selOptId);
                        if ($opt) {
                            $totalBrut += $opt['priceBrut'] ?? 0;
                            $totalNet  += $opt['priceNet']  ?? 0;
                        }
                        continue; // bu benefit için artık işimiz bitti
                    }
                }

                // 2) Alt seçenek yoksa ama BulutFin kodu varsa, benefit'in kendi BulutFin fiyatlarını kullan
                if (! empty($b['benefit_fin_code'])) {
                    $totalBrut += $b['priceBrut'] ?? 0;
                    $totalNet  += $b['priceNet']  ?? 0;
                    continue;
                }

            }

            $grp['totalBrut'] = $totalBrut;
            $grp['totalNet']  = $totalNet;
        }
        unset($grp);

        $type5amount = MemberBenefit::where('member_id', $member_id)
            ->where('benefit_id', 149)
            ->value('amount');

        // MemberBenefit'te benefit_id = 149 olan kaydı çek
        $type5amount = MemberBenefit::where('member_id', $member_id)
            ->where('benefit_id', 149)
            ->value('amount');

        // BulutFin maaş bilgileri
        $brutMaas = data_get($response, 'Data.BrutMaas', 0);
        $netMaas  = data_get($response, 'Data.NetMaas', 0);

        $day_price_net  = round($netMaas / 360, 2);
        $day_price_brut = round($brutMaas / 360, 2);

        if (!is_null($type5amount)) {
            foreach ($benefits as &$group) {
                foreach ($group['benefits'] as &$benefit) {
                    if (($benefit['type'] ?? null) == 5) {
                        $benefit['amount']          = $type5amount;
                        $benefit['day_price_net']   = $day_price_net;
                        $benefit['day_price_brut']  = $day_price_brut;
                    }
                }
                unset($benefit);
            }
            unset($group);
        }

        $values = [];

        foreach ($benefits as $group) {
            foreach ($group['benefits'] as $benefit) {
                    $benefitId = $benefit['id'];
                    $optionId  = $selecteds[$benefitId] ?? null;

                    $priceBrut = 0;
                    $priceNet  = 0;

                if (($benefit['type'] ?? null) === "5") {
                    $values[$benefitId] = $selecteds[$benefitId]
                        ?? ($benefit['amount'] ?? 0);
                    continue;
                } elseif ($optionId) {
                    $opt = collect($benefit['options'] ?? [])->firstWhere('id', $optionId);
                    if ($opt) {
                        $priceBrut = $opt['priceBrut'] ?? 0;
                        $priceNet  = $opt['priceNet']  ?? 0;
                    }
                } else {
                    $priceBrut = $benefit['priceBrut'] ?? 0;
                    $priceNet  = $benefit['priceNet']  ?? 0;
                }

                    $values[$benefitId] = [
                        'priceBrut' => $priceBrut,
                        'priceNet'  => $priceNet,
                    ];
                }
        }

        $grandTotalBrut = array_sum(array_column($benefits, 'totalBrut'));
        $grandTotalNet  = array_sum(array_column($benefits, 'totalNet'));




            $memberDashboardData = [
            'sliders' => $sliders,
            'welcome_messages' => $welcome_messages,
            'predefined_benefits' => $predefinedBenefits,
            'predefinedFlexiblePriceValues' => $predefinedFlexiblePriceValues,
            'final_family_benefits' => $finalFamilyBenefits,
            'opened' => $opened,
            'family_benefits' => $allFamilyBenefits,
            'selecteds' => $selecteds,
            'benefits' => $benefits,
            'values' => $values,
            'ozelSaglik' => $ozelSaglik,
        ];


        $data = array_merge($memberDashboardData, $paymentData);



        return view('member_dashboard', $data);


    }

    public function addAction(Request $request)
    {


        // Mevcut üye bilgileri
        $member = Member::where('id', Auth::guard('member')->id())->first();
        if (!$member) return response()->json(['success' => false, 'message' => 'Üye bulunamadı.']);




        $member_id  = $member->id;
        $percentage = $member->percentage;
        $append     = $request->has('append');
        $izin       = $request->has('izin');
        $m_benefit           = new Benefit();
        $benefitsCart        = new MemberBenefitCart();
        $memberBenefit       = new MemberBenefit();
        $benefit             = new Benefit();
        $workRules           = new WorkRules();
        $componentsValue     = CustomSettings::getComponents();
        $rulesValue          = CustomSettings::getRules();
        $customBenefits      = CustomBenefit::getAllCustomBenefits();
        $matchFound          = false;
        $continue            = true;
        $salesType           = null;
        $customBenefitsSale  = $componentsValue == "1" ? new CustomBenefitsSale() : null;
        $optionId            = $request->input('option_id');
        $benefitId           = $optionId ? $memberBenefit->getBenefitDataWithOptionID($optionId, $member_id) : $request->input('benefit_id');
        $netBrutMode = (int) $request->input('benefit_bulutfin_mode', 1);




        if($netBrutMode==1){
            $birey_field=$request->input('birey_field');
        }else{
            $birey_field=$request->input('birey_field_brut');
        }

        foreach ($customBenefits as $customBenefit) {
            if ($customBenefit->benefit_id == $benefitId || $customBenefit->benefit_related_id == $benefitId) {
                $matchFound = true;
                break;
            }
        }

        $isFlexibleOnly = !$request->has('option_id') && $request->has('benefit_id');
        $code = $request->input('benefit_fin_code');
        $priceBrut = $request->input('price_brut');
        $priceNet  = $request->input('price_net');
        $oldSelectedBrut = (new BordroHelper())->getOldSelectedPriceBrut(
            $request->input('previous_fin_code') ?: null
        );
        if ($oldSelectedBrut == 0) {
            $oldSelectedBrut = floatval($request->input('previous_brut_price', 0));
        }

        $oldSelectedNet = (new BordroHelper())->getOldSelectedPriceNet(
            $member_id,
            $benefitId,
            $request->input('previous_option_id') ?: null
        );
        if ($izin) {
            $adet = floatval(str_replace(',', '.', $request->input('birey_field_p_adet')));
            $dayNetPrice  = BordroHelper::parseTlToFloat($request->input('day_price_net'));   // örn: 2640.76
            $dayBrutPrice = BordroHelper::parseTlToFloat($request->input('day_price_brut'));  // örn: 4000

            $priceNetFloat = $adet * $dayNetPrice;
            $priceNet      = $priceNetFloat;
            $priceBrut     = $adet * $dayBrutPrice;

            $refundedAmount = $priceNetFloat; // Net pozitif bakiyeye eklenecek

// Ledger için upsert kontrolü
            $existingIzinLedger = MemberBenefitBalanceLedger::where('member_id', $member_id)
                ->where('benefit_id', $benefitId)
                ->whereNull('benefit_option_id')
                ->where('fin_type', 'izin_to_balance')
                ->latest('id')
                ->first();

            $ledgerData = [
                'member_id'                  => $member_id,
                'benefit_id'                 => $benefitId,
                'benefit_option_id'          => null,
                'fin_code'                   => $code,
                'fin_type'                   => 'izin_to_balance',
                'operation_type'             => 'izin',
                'price_brut'                 => $priceBrut,
                'price_net'                  => $priceNetFloat,
                'reference_price_net'        => 0,
                'reference_price_brut'       => 0,
                'used_from_positive_balance' => 0,
                'used_from_sales_earning'    => 0,
                'refunded_to_balance'        => $refundedAmount,
                'sale_net'                   => 0,
                'sale_brut'                  => 0,
            ];

            if ($existingIzinLedger) {
                $existingIzinLedger->update($ledgerData);
            } else {
                MemberBenefitBalanceLedger::create($ledgerData);
            }
            return response()->json(['success' => true]);
        }else{
            if ($code) {

                if ($isFlexibleOnly) {


                    if($birey_field=="0.00"){
                        $priceResponse = $this->bulutfin->request('haksat', [
                            'hakCode'  => $code,
                            'sicilNo'  => $member->identity_no,
                            'netBrut'  => $netBrutMode,
                            'tutar'    => $request->input('birey_field') ?? 0,
                            'kismiTam' => false,
                        ]);
                    }else{

                        $priceResponse = $this->bulutfin->request('hakal', [
                            'hakCode'  => $code,
                            'sicilNo'  => $member->identity_no,
                            'netBrut'  => $netBrutMode,
                            'tutar'    => $birey_field,
                        ]);

                    }


                }elseif (empty($priceNet) || $priceNet == "0") {
                    $priceResponse = $this->bulutfin->request('haksat', [
                        'hakCode'  => $code,
                        'sicilNo'  => $member->identity_no,
                        'netBrut'  => $netBrutMode,
                        'tutar'    => $request->input('birey_field') ?? 0,
                        'kismiTam' => false,
                    ]);
                } else {

                    $priceResponse = $this->bulutfin->request('haksatal', [
                        'satHakCode'  => $request->input('previous_fin_code'),
                        'alHakCode'   => $code,
                        'sicilNo'     => $member->identity_no,
                        'alNetBrut'   => $netBrutMode,
                        'alTutar'     => $request->input('price_net'),
                    ]);
                }

                $apiSuccess = data_get($priceResponse, 'Success', true);
                if ($apiSuccess === false) {
                    return response()->json([
                        'success' => false,
                        'message' => data_get($priceResponse, 'ErrorMessage', 'BulutFin servisinden hata alındı.'),
                        'message_en' => data_get($priceResponse, 'ErrorMessage', 'Error received from BulutFin service.'),
                    ]);
                }


                if ($netBrutMode == 1) {
                    // CODE'e göre eşleşen fiyatları al
                    $priceResponse = $this->bulutfin->request('yillikozet');
                    $data = data_get($priceResponse, 'Data', []);
                    $yanHakList = data_get($data, 'YanHakSumList', []);
                    $matched = collect($yanHakList)->firstWhere('CODE', $code);

                    $priceBrut = data_get($matched, 'YanHakBurut', 0);
                    $priceNet  = data_get($matched, 'YanHakNet', 0);
                }else{
                    $yanHakList = data_get($priceResponse, 'Data.YanHakSumList', []);
                    $matched    = collect($yanHakList)->firstWhere('CODE', $code);

                    $priceNet  = data_get($matched, 'YanHakNet', 0);
                    $priceBrut = data_get($matched, 'YanHakBurut', 0);
                }


            }
        }




        if (($rulesValue == "1" || $rulesValue == "2") && $componentsValue == "1") {
            $customBenefitIds = $customBenefits->pluck('benefit_id')->toArray();
            $nonCustomBenefitCart = $benefitsCart->where('member_id', $member_id)
                ->whereNotIn('benefit_id', $customBenefitIds)
                ->get()
                ->toArray();

            if (!empty($nonCustomBenefitCart)) {
                $continue = false;
            }
        }

        if (!$optionId) {
            $amount = $request->input('birey_field_p_adet', $request->input('birey_field'));
            $amount = str_replace(',', '.', $amount);
            $amount = max(0, $amount);

            $checkLimits = $benefit->checkLimits($benefitId);
            if ($checkLimits && $amount != 0) {
                if (!empty($checkLimits['min_amount']) && $amount < $checkLimits['min_amount']) {
                    return response()->json([
                        'success' => false,
                        'message' => "Seçtiğiniz hakta MİNİMUM tutar {$checkLimits['min_amount']} TL'dir.",
                        'message_en' => "Minimum limit is {$checkLimits['min_amount']} TL.",
                        'data' => []
                    ]);
                }
                if (!empty($checkLimits['max_amount']) && $amount > $checkLimits['max_amount']) {
                    return response()->json([
                        'success' => false,
                        'message' => "Seçtiğiniz hakta MAKSİMUM tutar {$checkLimits['max_amount']} TL'dir.",
                        'message_en' => "Maximum limit is {$checkLimits['max_amount']} TL.",
                        'data' => []
                    ]);
                }
            }

            $balance_previous = $m_benefit->getMemberBalance($member_id);

            if ($customBenefitsSale) {
                $checkAction = $customBenefitsSale->checkAvailableAction($benefitId, $member_id, $amount);
                if (!$checkAction['status']) {
                    return response()->json([
                        'success' => false,
                        'message' => $checkAction['message'],
                        'message_en' => $checkAction['message_en'],
                        'data' => []
                    ]);
                }
            }

            // Fiyatları ayarla
            $priceNet = $request->input('birey_field');
            $priceBrut = $request->input('price_brut');

            // fallback: yillikozet üzerinden net ve brut al
            $yillikozet = $this->bulutfin->request('yillikozet');
            $yanHakList = data_get($yillikozet, 'Data.YanHakSumList', []);
            $matched = collect($yanHakList)->first(function ($item) use ($code) {
                return strtoupper($item['CODE']) === strtoupper($code);
            });

            if ($netBrutMode == 0) {
                $priceNet = data_get($matched, 'YanHakNet', 0);
            }

            // fallback: brut boşsa birey_field_brut
            if (empty($priceBrut) || floatval($priceBrut) == 0) {
                $priceBrut = floatval(str_replace(',', '.', $request->input('birey_field_brut', 0)));
            }

            $priceNetFloat = floatval($priceNet);


            $previousOptionId = $request->input('previous_option_id');
            $oldSelectedNet = (new BordroHelper())->getOldSelectedPriceNet($member_id, $benefitId, $previousOptionId);
            $oldSelectedBrut = (new BordroHelper())->getOldSelectedPriceBrut($request->input('previous_fin_code'));
            if ($oldSelectedBrut == 0) {
                $oldSelectedBrut = floatval($request->input('previous_brut_price', 0));
            }

            $actualUsed = $netBrutMode == 0
                ? floatval($priceBrut) - floatval($oldSelectedBrut)
                : floatval($priceNetFloat) - floatval($oldSelectedNet);

            $result = $benefitsCart->addBenefitsByBenefitId(
                $benefitId,
                $amount,
                $append,
                $priceNet,
                $priceBrut,
                $code,
                $request->input('benefit_fin_type'),
                $request->input('benefit_fin_nr')
            );

            if ($result) {
                $workRules->processCustomBalance($member_id, null, $benefitId, $rulesValue, $netBrutMode);
                $balance = $m_benefit->getMemberBalance($member_id);

                if ($customBenefitsSale) {
                    $salesProfit = $balance - $balance_previous;
                    if ($salesProfit != 0 && $matchFound) {
                        $salesType = $matchFound ? "related" : "non-related";
                        $customBenefitsSale->benefitSale($benefitId, $member_id, $salesType, $salesProfit);
                    }
                }

                if ($netBrutMode == 0) {
                    // BRUT MODE: birey_field_brut kullanılır
                    $amount = floatval(str_replace(',', '.', $request->input('birey_field_brut', 0)));
                } else {
                    // NET MODE: birey_field_p_adet ya da birey_field kullanılır
                    $amount = $request->input('birey_field_p_adet', $request->input('birey_field'));
                    $amount = floatval(str_replace(',', '.', $amount));
                }
                $amount = max(0, $amount);



                if ($amount == 0) {
                    $operationType = 'refund';
                    $refundedToBalance = 0;
                    $priceNetFloat = 0;
                    $priceBrut = 0;
                    $usedFromPositiveBalance = 0;
                    $usedFromSalesEarning = 0;
                } elseif ($actualUsed > 0) {
                    $operationType = 'purchase';
                    $refundedToBalance = 0;

                    $ledgerTotals = \DB::table('member_benefit_balance_ledger')
                        ->where('member_id', $member_id)
                        ->selectRaw('
                    SUM(CASE WHEN operation_type = "purchase" THEN used_from_sales_earning ELSE 0 END) as total_sales_used,
                    SUM(CASE WHEN operation_type = "purchase" THEN used_from_positive_balance ELSE 0 END) as total_positive_used,
                    SUM(CASE WHEN operation_type = "refund" THEN refunded_to_balance ELSE 0 END) as total_refunded
                ')
                        ->first();

                    $totalSalesUsed = floatval($ledgerTotals->total_sales_used ?? 0);
                    $totalPositiveUsed = floatval($ledgerTotals->total_positive_used ?? 0);
                    $totalRefunded = floatval($ledgerTotals->total_refunded ?? 0);

                    $totalSalesProfit = \DB::table('member_benefit_balance_ledger')
                        ->where('member_id', $member_id)
                        ->where('operation_type', 'refund')
                        ->selectRaw('SUM(reference_price_net - price_net) as net_profit')
                        ->value('net_profit');

                    $initialPositiveBalance = floatval($member->positive_balance ?? 0);
                    $realSalesEarning = max(0, $totalSalesProfit - $totalSalesUsed);
                    $realPositiveBalance = max(0, $initialPositiveBalance - $totalPositiveUsed + $totalRefunded);

                    $usedFromSalesEarning = min($realSalesEarning, $amount);
                    $usedFromPositiveBalance = min($realPositiveBalance, $amount - $usedFromSalesEarning);
                } else {
                    $usedFromPositiveBalance = 0;
                    $usedFromSalesEarning = 0;
                    $refundedToBalance = abs($actualUsed);
                    $operationType = 'refund';
                }

                $existingLedger = MemberBenefitBalanceLedger::where('member_id', $member_id)
                    ->where('benefit_id', $benefitId)
                    ->whereNull('benefit_option_id')
                    ->where('fin_code', $code)
                    ->latest('id')
                    ->first();

                $ledgerData = [
                    'member_id'                  => $member_id,
                    'benefit_id'                 => $benefitId,
                    'benefit_option_id'          => null,
                    'fin_code'                   => $code,
                    'fin_type'                   => $isFlexibleOnly ? 'hakal' : 'haksatal',
                    'operation_type'             => $operationType,
                    'price_brut'                 => floatval($priceBrut),
                    'price_net'                  => floatval($priceNetFloat),
                    'reference_price_net'        => floatval($oldSelectedNet),
                    'reference_price_brut'       => floatval($oldSelectedBrut),
                    'used_from_positive_balance' => $usedFromPositiveBalance,
                    'used_from_sales_earning'    => $usedFromSalesEarning,
                    'refunded_to_balance'        => $refundedToBalance,
                    'sale_net'                   => floatval($priceNetFloat) - floatval($oldSelectedNet),
                    'sale_brut'                  => floatval($priceBrut) - floatval($oldSelectedBrut),
                ];

                if ($existingLedger) {
                    $existingLedger->update($ledgerData);
                } else {
                    MemberBenefitBalanceLedger::create($ledgerData);
                }

                return response()->json(['success' => true]);
            }
        }

        // HAKALSAT — option_id varsa
        else {

            $checkRules = $workRules->isAvailable($member_id, $optionId, $benefitId, $rulesValue);
            if (!$checkRules['status']) {
                return response()->json([
                    'success' => false,
                    'message' => $checkRules['message'],
                    'message_en' => $checkRules['message_en'],
                    'workRules' => true,
                    'data' => []
                ]);
            }

            $balance_previous = $m_benefit->getMemberBalance($member_id);

            if ($customBenefitsSale) {
                if ($balance_previous < 0 && $matchFound && !$continue) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Bakiyeniz - durumdayken kısıtlı hak satışı yapamazsınız!',
                        'message_en' => 'You cannot sell a restricted right while balance is negative!',
                        'ruleMatch' => true,
                        'data' => [
                            'benefit_id' => $benefitId,
                            'option_id' => $optionId
                        ]
                    ]);
                }

                $amount = $m_benefit->getCheckBenefitOptionPref($optionId, $member_id, 0);
                $checkAction = $customBenefitsSale->checkAvailableAction($benefitId, $member_id, $amount);
                if (!$checkAction['status']) {
                    return response()->json([
                        'success' => false,
                        'message' => $checkAction['message'],
                        'message_en' => $checkAction['message_en'],
                        'data' => []
                    ]);
                }
            }

            $finCode = $request->input('benefit_fin_code');
            $finType = $request->input('benefit_fin_type');
            $finNr   = $request->input('benefit_fin_nr');




            $result = $benefitsCart->addBenefitsByOptionId(
                $optionId,
                $member_id,
                $benefitId,
                $priceNet,
                $priceBrut,
                $netBrutMode,
                $finCode,
                $finType,
                $finNr
            );
            if ($result) {
                $workRules->processCustomBalance($member_id, $optionId, $benefitId, $rulesValue,$netBrutMode);

            }


            if ($customBenefitsSale && $result) {
                $balance = $m_benefit->getMemberBalance($member_id);
                $salesProfit = $balance - $balance_previous;
                if ($salesProfit != 0 && $matchFound) {
                    $salesType = $matchFound ? "related" : "non-related";
                    $customBenefitsSale->benefitSale($benefitId, $member_id, $salesType, $salesProfit);
                }
            }


            $priceNetFloat = floatval($priceNet);

            $actualNetUsed = $priceNetFloat - $oldSelectedNet;

            if ($actualNetUsed > 0) {
                $amount = floatval(str_replace(',', '.', $request->input('birey_field', 0)));
                $operationType = 'purchase';
                $refundedToBalance = 0;
// Ledger'daki toplamlar
                $ledgerTotals = \DB::table('member_benefit_balance_ledger')
                    ->where('member_id', $member_id)
                    ->selectRaw('
        SUM(CASE WHEN operation_type = "purchase" THEN used_from_sales_earning ELSE 0 END) as total_sales_used,
        SUM(CASE WHEN operation_type = "purchase" THEN used_from_positive_balance ELSE 0 END) as total_positive_used,
        SUM(CASE WHEN operation_type = "refund" THEN refunded_to_balance ELSE 0 END) as total_refunded
    ')
                    ->first();

// Gerçek bakiyeler
                $totalSalesUsed = floatval($ledgerTotals->total_sales_used ?? 0);
                $totalPositiveUsed = floatval($ledgerTotals->total_positive_used ?? 0);
                $totalRefunded = floatval($ledgerTotals->total_refunded ?? 0);

                $totalSalesProfit = \DB::table('member_benefit_balance_ledger')
                    ->where('member_id', $member_id)
                    ->where('operation_type', 'refund') // refund = satış
                    ->selectRaw('SUM(reference_price_net - price_net) as net_profit')
                    ->value('net_profit');
                $initialPositiveBalance = floatval($member->positive_balance ?? 0);
                $realSalesEarning = max(0, $totalSalesProfit - $totalSalesUsed);
                $realPositiveBalance = max(0, $initialPositiveBalance - $totalPositiveUsed + $totalRefunded);

// Önce satıştan kullan, kalan varsa pozitiften
                $usedFromSalesEarning = min($realSalesEarning, $amount);
                $usedFromPositiveBalance = min($realPositiveBalance, $amount - $usedFromSalesEarning);
            } else {
                // İade durumu
                $usedFromPositiveBalance = 0;
                $usedFromSalesEarning    = 0;
                $refundedToBalance       = abs($actualNetUsed);
                $operationType           = 'refund';
            }

            MemberBenefitBalanceLedger::create([
                'member_id'                  => $member_id,
                'benefit_id'                 => $benefitId,
                'benefit_option_id'          => $optionId ?? null,
                'fin_code'                   => $code,
                'fin_type'                   => $isFlexibleOnly ? 'hakal' : 'haksatal',
                'operation_type'             => $operationType,
                'price_brut'                 => $priceBrut,
                'price_net'                  => $priceNetFloat,
                'reference_price_net'        => $oldSelectedNet,
                'used_from_positive_balance' => $usedFromPositiveBalance,
                'used_from_sales_earning'    => $usedFromSalesEarning,
                'refunded_to_balance'        => $refundedToBalance,
                'sale_net'                 => floatval($priceNet)  - floatval($oldSelectedNet),
                'sale_brut'                => floatval($priceBrut) - floatval($oldSelectedBrut),
                'reference_price_brut'     => $oldSelectedBrut,
            ]);

            $benefitId = $result['benefit_id'];
            $announcement = new Announcement();
            $announcementId = $announcement->getByBenefitId($benefitId);

            return response()->json([
                'message' => $result['message'],
                'success' => $result['success'],
                'data' => ['announcement_id' => $announcementId],
                'multiple' => $result['multiple'] ?? false,
                'benefit_ids' => $result['benefit_ids'] ?? null,
                'benefit_options_ids' => $result['benefit_options_ids'] ?? null,
            ]);
        }

        return response()->json(['success' => false, 'message' => 'İşlem gerçekleştirilemedi.']);
    }
    public function resetCart(Request $request)
    {
        $member = Member::where('id', Auth::guard('member')->id())->first();
        $member_id = $member->id ?? null;

        if (!$member_id) {
            return redirect()->back()->with('error', 'Üye bilgileri bulunamadı.');
        }

        $response = $this->bulutfin->request('sifirla');
        $data     = data_get($response, 'Data', []);
        if (!data_get($response, 'Success')) {
            $error = data_get($response, 'ErrorMessage', 'BulutFin sıfırlama sırasında bir hata oluştu.');
            return redirect()->back()->with('error', $error);
        }

        MemberDashboard::clearMemberData($member_id);

        MemberBenefitBalanceLedger::where('member_id', $member_id)->delete();

        $request->session()->forget('not_window_on_load');
        $request->session()->forget('selected_custom');


        return redirect()->route('member_dashboard')
            ->with('success', 'Kart başarıyla sıfırlandı.');
    }

    function parseTlToFloat(string $value): float
    {
        $clean = str_replace([' TL', 'TL', ' '], '', $value);
        $clean = str_replace('.', '', $clean);
        $clean = str_replace(',', '.', $clean);
        return floatval($clean);
    }
}
