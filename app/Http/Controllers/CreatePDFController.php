<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\Member;
use App\Models\Benefit;
use App\Models\MemberBenefit;
use App\Models\MemberBenefitCart;
use App\Models\SiteMovement;
use Illuminate\Support\Facades\App;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\BenefitOption;
use Mpdf\Mpdf;

class CreatePDFController extends Controller
{


    public function exportBenefitsToPDF(Request $request)
    {
        $member_id = $request->input('member_id');
        $member = Member::findOrFail($member_id);

        $siteMovement = SiteMovement::find(2);
        $senderEmail = $siteMovement->extra1;
        $senderName = $siteMovement->extra2;

        $start_date = Carbon::createFromFormat('d.m.Y', $request->input('start_date', "01.06.2021"));
        $end_date = Carbon::createFromFormat('d.m.Y', $request->input('end_date', "01.06.2022"));

        $order = Order::where('member_id', $member_id)->orderByDesc('id')->first();
        $orderItems = $order ? OrderItem::where('order_id', $order->id)->get() : collect();

        $data = [];

        if (App::getLocale() == 'en') {
            $data["title"] = "DECLARATION AND COMMITMENT";
            $data["lines"] = [
                "ANNEX 1: SUMMARY OF SIDE BENEFITS",
                "Name Surname: " . $member->name,
                "Date: " . date('d/m/Y'),
            ];
            $data["table2"]["headers"] = ["Selected Benefits", "Net Price", "Gross Price"];
            $data["table2"]["headers_sub"] = ["", "Net", "Brut"];
            $data["table2"]["headers_width"] = [250, 100, 100];
        } else {
            $data["title"] = "BEYANNAME VE TAAHHÜTNAME";
            $data["lines"] = [
                "EK 1: YAN HAKLAR ÖZETİ",
                "Ad Soyad: " . $member->name,
                "Tarih: " . date('d/m/Y'),
            ];
            $data["table2"]["headers"] = ["Seçilen Haklar", "Net Tutar", "Brüt Tutar"];
            $data["table2"]["headers_sub"] = ["", "Net", "Brüt"];
            $data["table2"]["headers_width"] = [250, 100, 100];
        }

        $data["table"]["headers"] = ["Eski Haklar"];
        $data["table"]["headers_width"] = [150];
        $data["table"]["rows"] = [];
        $data["table2"]["rows"] = [];

        if ($order && $orderItems->isNotEmpty()) {
            $benefitMap = Benefit::whereIn('id', $orderItems->pluck('benefit_id'))->get()->keyBy('id');
            $optionMap = BenefitOption::whereIn('id', $orderItems->pluck('option_id')->filter())->get()->keyBy('id');

            foreach ($orderItems as $item) {
                $net = floatval($item->price);
                $brut = floatval($item->brut_price);

                if ($net == 0.0 && $brut == 0.0) {
                    continue;
                }

                $benefit = $benefitMap[$item->benefit_id] ?? null;
                $option = $item->option_id ? ($optionMap[$item->option_id] ?? null) : null;

                $benefitTitle = $benefit
                    ? (App::getLocale() == 'en'
                        ? ($benefit->title_en ?? $benefit->name_en ?? $benefit->title)
                        : ($benefit->title ?? $benefit->name))
                    : '';

                $optionTitle = $option
                    ? (App::getLocale() == 'en'
                        ? ($option->name_en ?? $option->name)
                        : ($option->name))
                    : '';

                $fullTitle = trim($benefitTitle . ($optionTitle ? ' - ' . $optionTitle : ''));

                $data["table2"]["rows"][] = [
                    $fullTitle,
                    $net ? number_format($net, 2) . ' TL' : '-',
                    $brut ? number_format($brut, 2) . ' TL' : '-',
                ];
            }
        }

        $data["is_table"] = 1;
        $data["table"]["totals"] = [];
        $data["table2"]["totals"] = [];

        $html = view('reports.benefit', $data)->render();
        $mpdf = new Mpdf(['tempDir' => storage_path('app/public/tmp')]);
        $mpdf->WriteHTML($html);

        $pdfFileName = $member->name . '_benefits.pdf';
        $pdfFilePath = storage_path('app/public/pdfs/' . $pdfFileName);

        if (!file_exists(dirname($pdfFilePath))) {
            mkdir(dirname($pdfFilePath), 0777, true);
        }

        $mpdf->Output($pdfFilePath, 'F');

        return response()->download($pdfFilePath)->deleteFileAfterSend(true);
    }
}
