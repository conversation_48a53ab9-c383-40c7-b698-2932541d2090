<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SiteMovement;
use App\Models\Member;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\App;
use Maatwebsite\Excel\Facades\Excel;

class SiteMovementController extends Controller
{
    public function show()
    {
        $siteMovementData = SiteMovement::find(1);

        if (!$siteMovementData) {
            return abort(404, 'Kayıt bulunamadı.');
        }

        if ($siteMovementData->extra1 === "0") {
            return redirect()->route('member_login');
        }

        return view('site_closed', [
            'siteMovementData' => $siteMovementData
        ]);
    }

    // public function sendCustomEmails()
    // {

    //      // ID'si 3 olan site_movements kaydını çek
    //     $siteMovement = SiteMovement::find(3);

    //     // Eğer kayıt bulunamazsa, fonksiyonu sonlandır
    //     if (!$siteMovement) {
    //         return;
    //     }

    //     // senderName ve senderEmail'i al
    //     $senderName = $siteMovement->extra2;
    //     $senderEmail = $siteMovement->extra1;

    //     if (App::getLocale() == 'en') {
    //         $subject = "Site Opened";
    //     } else {
    //         $subject = "Site Açıldı";
    //     }

    //     $members = Member::all();


    //     foreach ($members as $member) {
    //         Mail::send('emails.site_opened', ['member' => $member], function ($message) use ($member, $senderName, $senderEmail, $subject) {
    //             $message->from($senderEmail, $senderName);
    //             $message->to($member->email)->subject($subject);
    //         });
    //     }
    // }


    public function import(Request $request)
    {
        $request->validate([
            'excel_file' => 'required|mimes:xlsx'
        ]);

        $file = $request->file('excel_file');
        $lang = $request->get('language');

        if ($file) {
            try {
                set_time_limit(300);

                $collection = Excel::toArray([], $file)[0];
                $header = array_shift($collection);

                $expectedColumns = ['IDENTITY_NUMBER', 'NAME', 'EMAIL', 'PASSWORD'];

                if (array_diff($expectedColumns, $header)) {
                    return back()->with('error', 'Excel dosyasında beklenen kolonlar bulunamadı.');
                }

                if (!empty($collection) && count($collection)) {
                    $siteMovement = SiteMovement::find(3);
                    $senderName = $siteMovement->extra2 ?? "Demo WTW";
                    $senderEmail = $siteMovement->extra1 ?? "<EMAIL>";

//                    $extra4 = $siteMovement && $siteMovement->extra4 == "2" ? $siteMovement->extra4 : 1;
                    $subject = $lang == "en" ? "Flexible Benefits System Opened!" : "Esnek Yan Haklar Sistemi Açıldı!!";

                    foreach ($collection as $row) {
                        $emailData = [
                            'identityNumber' => $row[0],
                            'name' => $row[1],
                            'email' => $row[2],
                            'password' => $row[3],
//                            'extra4' => $extra4,
                            'lang' => $lang
                        ];

                        Mail::send('emails.site_opened', $emailData, function ($message) use ($emailData, $senderName, $senderEmail, $subject) {
                            $message->from($senderEmail, $senderName);
                            $message->to($emailData['email'])->subject($subject);
                        });
                    }
                }

                return back()->with('success', 'Toplu mail gönderimi başarıyla sağlandı.');
            } catch (\Exception $e) {
                return back()->with('error', 'Bir hata oluştu: ' . $e->getMessage());
            }
        }
    }

}
