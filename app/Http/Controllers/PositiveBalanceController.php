<?php

namespace App\Http\Controllers;

use App\Services\BordroService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Benefit;
use App\Models\Member;
use App\Models\MemberBenefit;
use App\Models\Question;
use App\Models\QuestionOption;
use App\Models\StaticPage;
use Illuminate\Support\Facades\App;

class PositiveBalanceController extends Controller
{

    protected BordroService $bulutfin;

    public function __construct(BordroService $bulutfin)
    {
        $this->bulutfin = $bulutfin;
    }

    public function show()
    {
        $member = Member::find(Auth::guard('member')->id());
        if (!$member) {
            return '';
        }

        $memberId    = $member->id;
        $user_info   = $member->toArray();
        $memberPct   = $member->percentage;

        $mBenefit = new Benefit();
        $memberBalance = $mBenefit->getMemberBalance($memberId);
        $benefits = $mBenefit->getShowOnPositiveBenefits($memberId);
        $finalBenefits = $mBenefit->getFinalBenefits($memberId);
        $finalFamilyBenefitTotalsByGroup = $mBenefit->getFinalFamilyBenefitsTotalsByGroup($memberId);

        // Eğer member’ın onayı yoksa ve pozitif bakiyesi varsa yönlendir
        if ($memberBalance['balance'] > 0 && $user_info['is_approved'] != 1) {
            return redirect()->to('/member_dashboard');
        }

        // 1) option_id varsa kodlarını topla
        $optionIds = $finalBenefits->pluck('option_id')->filter()->unique()->all();
        $optionCodes = \App\Models\BenefitOption::whereIn('id', $optionIds)
            ->pluck('benefit_fin_code', 'id')
            ->all();

        $resp = $this->bulutfin->request('yillikozet');
        $data   = data_get($resp, 'Data', []);
        $yanHak = data_get($data, 'YanHakSumList', []);
        $hsoh   = data_get($data, 'HSOHSumList', []);
        $allBF  = array_merge($yanHak, $hsoh);

        $groupedByCode = collect($allBF)
            ->groupBy('CODE')
            ->map(fn($items) => [
                'priceBrut' => $items->sum('YanHakBurut'),
                'priceNet'  => $items->sum('YanHakNet'),
            ])
            ->all();

        $groupedBenefits    = [];
        $totalPricesByGroup = [];

        $izinLedgerByBenefitId = \DB::table('member_benefit_balance_ledger')
            ->where('member_id', $memberId)
            ->where('fin_type', 'izin_to_balance')
            ->get()
            ->keyBy('benefit_id');

        foreach ($finalBenefits as $b) {
            $gid = $b->group_id;
            if (empty($gid)) {
                continue;
            }

            $b->benefit_fin_code_option = $optionCodes[$b->option_id] ?? null;
            $code = $b->benefit_fin_code_option ?: $b->benefit_fin_code;

            if ($b->type == 5 && isset($izinLedgerByBenefitId[$b->benefit_id])) {
                $ledger     = $izinLedgerByBenefitId[$b->benefit_id];
                $priceBrut  = floatval($ledger->price_brut);
                $priceNet   = floatval($ledger->price_net);
            }
            elseif (isset($groupedByCode[$code])) {
                $priceBrut = $groupedByCode[$code]['priceBrut'];
                $priceNet  = $groupedByCode[$code]['priceNet'];
            }
            else {
                $priceBrut = 0;
                $priceNet  = 0;
            }

            if ($priceBrut == 0 && $priceNet == 0) {
                continue;
            }

            $b->priceBrut = $priceBrut;
            $b->priceNet  = $priceNet;

            $groupedBenefits[$gid][] = $b;

            $totalPricesByGroup[$gid]['brut'] = ($totalPricesByGroup[$gid]['brut'] ?? 0) + $priceBrut;
            $totalPricesByGroup[$gid]['net']  = ($totalPricesByGroup[$gid]['net']  ?? 0) + $priceNet;
        }

        // 4) Aile faydaları ekle
        foreach ($finalFamilyBenefitTotalsByGroup as $gid => $total) {
            $totalPricesByGroup[$gid] = ($totalPricesByGroup[$gid] ?? 0) + $total;
        }

        // 5) Değer girilmiş esnek hakları al
        $values = [];
        foreach ($finalBenefits as $benefit) {
            if ($benefit['has_flexible_price']) {
                $values[$benefit['benefit_id']] = $benefit['amount'];
            }
        }

        // 6) Soru-opsiyon yapıları
        $questions = (new Question())->getUnansweredQuestions($memberId);
        $options   = (new QuestionOption())->getOptions();
        $qo = [];

        foreach ($options as $o) {
            $qo[$o->question_id][$o->id] = App::getLocale() == 'en' ? $o->option_en : $o->option;
        }

        // 7) Static Pages
        $static_pages = StaticPage::where(function ($query) {
            $query->where('before_login', '<>', 1)
                ->orWhereNull('before_login');
        })
            ->where('is_visible', 1)
            ->get();


        return view('side_benefits_approval', [
            'values'              => $values,
            'static_pages'        => $static_pages,
            'benefits'            => $benefits,
            'memberBalance'       => $memberBalance,
            'finalBenefits'       => $finalBenefits,
            'groupedBenefits'     => $groupedBenefits,
            'user_info'           => $user_info,
            'totalPricesByGroup'  => $totalPricesByGroup,
            'questions'           => $questions,
            'options'             => $qo
        ]);
    }
    public function showOLD()
    {
        $member = Member::where('id', Auth::guard('member')->id())->first();
        if ($member) {
            $memberId = $member->id;
            $user_info = $member->toArray();
        }
        if (!$memberId) {
            return '';
        }

        $mBenefit = new Benefit();
        $memberBalance = $mBenefit->getMemberBalance($memberId);

        if ($memberBalance > 0 && $user_info['is_approved'] != 1) {
            return redirect()->to('/member_dashboard');
        }

        $benefits = $mBenefit->getShowOnPositiveBenefits($memberId);


        $finalBenefits = $mBenefit->getFinalBenefits($memberId);

        $finalFamilyBenefitTotalsByGroup = $mBenefit->getFinalFamilyBenefitsTotalsByGroup($memberId);


        $groupedBenefits = [];
        $totalPricesByGroup = [];

        $memberPercentage = Member::value('percentage');

        foreach ($finalBenefits as $benefit) {

//                if ($benefit->group_id != 1 && ($benefit->real_price == '0.00' || empty($benefit->real_price)) && empty($finalFamilyBenefits[$benefit->id]) && $benefit->type != 5) {
//                    continue;
//                }

            if (($benefit->real_price=='0.00' || empty($benefit->real_price) && $benefit->benefit_group_id != 14)) {
                continue;
            }

            $group_id = $benefit->group_id;


            if ($group_id !== null && $group_id !== '') {

                $groupedBenefits[$group_id][] = $benefit;

                if($benefit->type!="5"){
                    if ($benefit->convert_price) {
                        if ($benefit->group_id == "14" && (is_null($benefit->real_price) || $benefit->real_price == "0.00")) {
                            $real_price = $benefit->amount / ($memberPercentage / 100);
                        }else{
                            $real_price = $benefit->real_price / ($memberPercentage / 100);
                        }
                    }else if($benefit->equal_price){
                        if ($benefit->group_id == "14" && (is_null($benefit->real_price) || $benefit->real_price == "0.00")) {
                            $real_price = $benefit->amount;
                        }else{
                            $real_price = $benefit->real_price;
                        }
                    } else {
                        if ($benefit->group_id == "14" && (is_null($benefit->real_price) || $benefit->real_price == "0.00")) {
                            $real_price = $benefit->amount;
                        }else{
                            $real_price = $benefit->real_price;
                        }
                    }
                }else{
                    $real_price = $benefit->amount*$benefit->option_price;

                }


                if (!isset($totalPricesByGroup[$group_id])) {
                    $totalPricesByGroup[$group_id] = 0;
                }

                $totalPricesByGroup[$group_id] += $real_price;
            }
        }


        foreach ($finalFamilyBenefitTotalsByGroup as $key => $familyBenefitTotal) {
            if (empty($totalPricesByGroup[$key])) {
                $totalPricesByGroup[$key] = 0;
            }

            $totalPricesByGroup[$key] += $familyBenefitTotal;
        }

        $values = [];
        foreach ($finalBenefits as $benefit) {
            if ($benefit['has_flexible_price']) {
                $values[$benefit['benefit_id']] = $benefit['amount'];
            }
        }

        $questions = (new Question())->getUnansweredQuestions($memberId);

        $options = (new QuestionOption())->getOptions();

        $qo = [];

        foreach ($options as $o) {
            if (App::getLocale() == 'en') {
                $qo[$o->question_id][$o->id] = $o->option_en;
            } else {
                $qo[$o->question_id][$o->id] = $o->option;
            }
        }


        // // STATIC PAGES
        // $static_pages = StaticPage::where('before_login', '<>', 1)
        //     ->orWhereNull('before_login')
        //     ->get();
        // // STATIC PAGES

        // STATIC PAGES
        $static_pages = StaticPage::where(function ($query) {
            $query->where('before_login', '<>', 1)
                ->orWhereNull('before_login');
        })
            ->where('is_visible', 1)
            ->get();
        // STATIC PAGES

        foreach($groupedBenefits as $key => $value){
            foreach($value as $v){
                if($v['type']=="5"){
                    $v['real_price']=$v['option_price']*$v['amount'];
                }
            }
        }

;



        $viewData = [
            "values" => $values,
            'static_pages' => $static_pages,
            'benefits' => $benefits,
            'memberBalance' => $memberBalance,
            'finalBenefits' => $finalBenefits,
            'groupedBenefits' => $groupedBenefits,
            'user_info' => $user_info,
            'totalPricesByGroup' => $totalPricesByGroup,
            'questions' => $questions,
            'options' => $qo
        ];

        return view('side_benefits_approval', $viewData);
    }

    public function getInitData()
    {
        $view = $this->show();
        $data = $view->getData();
        return response()->json($data);
    }

    public function showApproval()
    {

        $view = $this->show();

        if ($view instanceof \Illuminate\View\View) {
            $data = $view->getData();
            return view('approval', ['data' => $data]);
        }
    }
}
