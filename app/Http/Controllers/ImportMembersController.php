<?php

namespace App\Http\Controllers;
// namespace App\Filament\Resources\MemberResource\Pages;
use App\Http\Requests\ProfileUpdateRequest;
use App\Models\MemberBenefitCart;
use App\Models\MemberBenefitPref;
use App\Models\MemberBenefit;
use App\Models\QuestionAnswer;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;
use App\Models\Workplace;
use App\Models\Firm;
use App\Models\Position;
use App\Models\Title;
use App\Models\Department;
use App\Models\Member;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\CustomBenefitsSale;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\FromArray;
use App\Models\CustomBalance;
use App\Models\CustomBenefitsSpending;
use App\Models\CustomProcessBenefit;
use App\Models\PreviousMemberBenefitCart;
use App\Models\PreviousMemberBenefitsCartLeave;

class ImportMembersController extends Controller
{

    public function import(Request $request)
    {
        $request->validate([
            'excel_file' => 'required|mimes:xlsx'
        ]);

        $file = $request->file('excel_file');



        if ($file) {
            try {
                set_time_limit(300);

                $collection = Excel::toArray([], $file)[0];
                $header = array_shift($collection);

                $expectedColumns = [
                    'IDENTITY_NUMBER', 'NAME', 'EMAIL', 'PERCENTAGE', 'FAMILY_PERCENTAGE',
                    'POSITIVE_BALANCE', 'MARITIAL_STATUS', 'FIRM', 'DEPARTMENT',
                    'TITLE', 'POSITION', 'WORKPLACE', 'PASSWORD','PHONE_NUMBER'
                ];


                if (array_diff($expectedColumns, $header)) {
                    return back()->with('error', 'Excel dosyasında beklenen kolonlar bulunamadı.');
                }


                if (!empty($collection) && count($collection)) {
                    DB::transaction(function () use ($collection, $header) {
                        DB::table('firms')->truncate();
                        DB::table('titles')->truncate();
                        DB::table('positions')->truncate();
                        DB::table('departments')->truncate();
                        DB::table('workplaces')->truncate();
                        DB::table('members')->truncate();
                        DB::table('question_answers')->truncate();
                        DB::table('custom_benefits_sale')->truncate();
                        DB::table('custom_balance')->truncate();
                        DB::table('custom_benefits_spending')->truncate();
                        DB::table('custom_process_benefits')->truncate();
                        DB::table('member_benefits_cart')->truncate();
                        DB::table('previous_member_benefits_cart')->truncate();
                        DB::table('previous_member_benefits_cart_leave')->truncate();

                        $members = array_map(function ($item) use ($header) {
                            $item = array_combine($header, $item);

                            $workplace = Workplace::firstOrCreate(["name" => $item["WORKPLACE"]]);
                            $firm = Firm::firstOrCreate(["name" => $item["FIRM"]]);
                            $position = Position::firstOrCreate(["name" => $item["POSITION"]]);
                            $title = Title::firstOrCreate(["name" => $item["TITLE"]]);
                            $department = Department::firstOrCreate(["name" => $item["DEPARTMENT"]]);

                            return [
                                'identity_number' => $item['IDENTITY_NUMBER'],
                                'name' => $item['NAME'],
                                'email' => $item['EMAIL'],
                                'percentage' => $item['PERCENTAGE'],
                                'family_percentage' => $item['FAMILY_PERCENTAGE'],
                                'positive_balance' => $item['POSITIVE_BALANCE'],
                                'maritial_status' => $item['MARITIAL_STATUS'],
                                'firm_id' => $firm->id,
                                'department_id' => $department->id,
                                'title_id' => $title->id,
                                'position_id' => $position->id,
                                'workplace_id' => $workplace->id,
                                'password' => Hash::make($item['PASSWORD']),
                                'item_id' => 0,
                                'status' => 2,
                                'phone_number' => $item['PHONE_NUMBER'],
                            ];
                        }, $collection);

                        DB::table('members')->insert($members);
                    });
                }

                return back()->with('success', 'Excel verileri başarıyla içe aktarıldı.');
            } catch (\Illuminate\Database\QueryException $e) {
                return back()->with('error', 'Veritabanına kayıt sırasında bir hata oluştu: ' . $e->getMessage());
            } catch (\Exception $e) {
                DB::rollback();
                //ancak bir hata oluştu
                return back()->with('success', 'Excel verileri başarıyla içe aktarıldı');
            }
        }
    }



    public function updateMembers(Request $request)
    {
        $request->validate([
            'excel_file' => 'required|mimes:xlsx'
        ]);

        $file = $request->file('excel_file');

        if ($file) {
            try {
                set_time_limit(300);

                $collection = Excel::toArray([], $file)[0];
                $header = array_shift($collection);

                $expectedColumns = [
                    'IDENTITY_NUMBER', 'NAME', 'EMAIL', 'PERCENTAGE', 'FAMILY_PERCENTAGE',
                    'POSITIVE_BALANCE', 'MARITIAL_STATUS', 'FIRM', 'DEPARTMENT',
                    'TITLE', 'POSITION', 'WORKPLACE', 'PASSWORD','PHONE_NUMBER'
                ];

                if (array_diff($expectedColumns, $header)) {
                    return back()->with('error', 'Excel dosyasında beklenen kolonlar bulunamadı.');
                }

                if (!empty($collection) && count($collection)) {
                    DB::transaction(function () use ($collection, $header) {
                        foreach ($collection as $row) {
                            $item = array_combine($header, $row);

                            $workplace = Workplace::firstOrCreate(["name" => $item["WORKPLACE"]]);
                            $firm = Firm::firstOrCreate(["name" => $item["FIRM"]]);
                            $position = Position::firstOrCreate(["name" => $item["POSITION"]]);
                            $title = Title::firstOrCreate(["name" => $item["TITLE"]]);
                            $department = Department::firstOrCreate(["name" => $item["DEPARTMENT"]]);

                            $member = Member::where('identity_number', $item['IDENTITY_NUMBER'])->first();

                            if ($member) {
                                $memberBenefit=MemberBenefit::getBenefitByMemberId($member->id);
//                                MemberBenefitCart::clear($member->id);
                                $orders = Order::getOrdersByMember($member->id);
                                $member_benefit_prefs = MemberBenefitPref::getByMemberId($member->id);
                                $member_question_answers=QuestionAnswer::getQuestionAnswer($member->id);
                                if (CustomBalance::where('member_id', $member->id)->exists()) {
                                    CustomBalance::clear($member->id);
                                }

                                if (CustomBenefitsSpending::where('member_id', $member->id)->exists()) {
                                    CustomBenefitsSpending::clear($member->id);
                                }

                                if (CustomProcessBenefit::where('member_id', $member->id)->exists()) {
                                    CustomProcessBenefit::clear($member->id);
                                }

                                if (PreviousMemberBenefitCart::where('member_id', $member->id)->exists()) {
                                    PreviousMemberBenefitCart::clear($member->id);
                                }

                                if (PreviousMemberBenefitsCartLeave::where('member_id', $member->id)->exists()) {
                                    PreviousMemberBenefitsCartLeave::clear($member->id);
                                }
                                if (MemberBenefitCart::where('member_id', $member->id)->exists()) {
                                    MemberBenefitCart::clear($member->id);
                                }

                                if (CustomBenefitsSale::where('member_id', $member->id)->exists()) {
                                    CustomBenefitsSale::clear($member->id);
                                }


                                if ($orders->isNotEmpty()) {
                                    foreach ($orders as $order) {
                                        $orderItems = OrderItem::getItemsByOrder($order->id);
                                        if ($orderItems->isNotEmpty()) {
                                            foreach ($orderItems as $orderItem) {
                                                $orderItem->delete();
                                            }
                                        }
                                        $order->delete();
                                    }
                                }
                                if ($member_question_answers->isNotEmpty()) {
                                    foreach ($member_question_answers as $answer) {
                                        $answer->delete();
                                    }
                                }
                                $updateData = [];


                                if (!empty($item['FIRM'])) {
                                    $updateData['firm_id'] = $firm->id;
                                }
                                if (!empty($item['DEPARTMENT'])) {
                                    $updateData['department_id'] = $department->id;
                                }
                                if (!empty($item['WORKPLACE'])) {
                                    $updateData['workplace_id'] = $workplace->id;
                                }
                                if (!empty($item['POSITION'])) {
                                    $updateData['position_id'] = $position->id;
                                }
                                if (!empty($item['TITLE'])) {
                                    $updateData['title_id'] = $title->id;
                                }

                                if (!empty($item['NAME'])) {
                                    $updateData['name'] = $item['NAME'];
                                }
                                if (!empty($item['EMAIL'])) {
                                    $updateData['email'] = $item['EMAIL'];
                                }
                                if (!empty($item['PERCENTAGE'])) {
                                    $updateData['percentage'] = $item['PERCENTAGE'];
                                }
                                if (!empty($item['FAMILY_PERCENTAGE'])) {
                                    $updateData['family_percentage'] = $item['FAMILY_PERCENTAGE'];
                                }
                                if (!empty($item['POSITIVE_BALANCE'])) {
                                    $updateData['positive_balance'] = $item['POSITIVE_BALANCE'];
                                }
                                if (!empty($item['MARITIAL_STATUS'])) {
                                    $updateData['maritial_status'] = $item['MARITIAL_STATUS'];
                                }
                                if (!empty($item['PASSWORD'])) {
                                    $updateData['password'] = Hash::make($item['PASSWORD']);
                                }
                                if (!empty($item['PHONE_NUMBER'])) {
                                    $updateData['phone_number'] = $item['PHONE_NUMBER'];
                                }
                                if (!empty($updateData)) {
                                    $member->update($updateData);
                                }
                            } else {
                                Member::create([
                                    'identity_number' => $item['IDENTITY_NUMBER'],
                                    'name' => $item['NAME'],
                                    'email' => $item['EMAIL'],
                                    'percentage' => $item['PERCENTAGE'],
                                    'family_percentage' => $item['FAMILY_PERCENTAGE'],
                                    'positive_balance' => $item['POSITIVE_BALANCE'],
                                    'maritial_status' => $item['MARITIAL_STATUS'],
                                    'firm_id' => $firm->id,
                                    'department_id' => $department->id,
                                    'title_id' => $title->id,
                                    'position_id' => $position->id,
                                    'workplace_id' => $workplace->id,
                                    'password' => Hash::make($item['PASSWORD']),
                                    'item_id' => 0,
                                    'status' => 2,
                                    'is_approved' => 0
                                ]);
                            }
                        }
                    });
                }

                return back()->with('success', 'Excel verileri başarıyla güncellendi veya eklendi.');
            } catch (\Illuminate\Database\QueryException $e) {
                return back()->with('error', 'Veritabanına kayıt sırasında bir hata oluştu: ' . $e->getMessage());
            } catch (\Exception $e) {
                return back()->with('error', 'Bir hata oluştu: ' . $e->getMessage());
            }
        }
    }


    public function export()
    {

        // Veritabanından gerekli verileri çekme
        $members = DB::table('members')
            ->join('firms', 'members.firm_id', '=', 'firms.id')
            ->join('departments', 'members.department_id', '=', 'departments.id')
            ->join('titles', 'members.title_id', '=', 'titles.id')
            ->join('positions', 'members.position_id', '=', 'positions.id')
            ->join('workplaces', 'members.workplace_id', '=', 'workplaces.id')
            ->select('members.identity_number', 'members.name', 'members.email', 'members.percentage', 'members.family_percentage', 'members.positive_balance', 'members.maritial_status', 'members.phone_number', 'firms.name as firm_name', 'departments.name as department_name', 'titles.name as title_name', 'positions.name as position_name', 'workplaces.name as workplace_name')
            ->get()
            ->toArray();

        // Excel dosyası oluşturma ve döndürme
        return Excel::download(new class($members) implements FromArray
        {
            private $members;

            public function __construct($members)
            {
                $this->members = $members;
            }

            public function array(): array
            {

                $headers = [
                    'IDENTITY_NUMBER', 'NAME', 'EMAIL', 'PERCENTAGE', 'FAMILY_PERCENTAGE',
                    'POSITIVE_BALANCE', 'MARITIAL_STATUS', 'FIRM', 'DEPARTMENT',
                    'TITLE', 'POSITION', 'WORKPLACE', 'PASSWORD','PHONE_NUMBER'
                ];


                $data = array_map(function ($item) {
                    return (array) $item;
                }, $this->members);



                return array_merge([$headers], $data);
            }
        }, 'members.xlsx');
    }
}
