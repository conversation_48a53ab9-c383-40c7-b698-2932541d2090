<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Report;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ReportController extends Controller
{
    protected static $reportMethods = [
        1 => 'getCustomReports',
        2 => 'predefinedBenefitsReports',
        3 => 'hakListesiSecenekli',
        4 => 'uyeAileListesi',
        5 => 'ApprovedMembers',
        6 => 'hakSecenekleriHediyeCekleri',
        7 => 'AnketCevaplariRaporu',
        8 => 'firmaDepartmanBazindaSectigiHaklariOnaylamayanlarListesi',
        9 => 'firmaDepartmanBazindaSectigiHaklariOnaylayanlarListesi',
        10 => 'hakBazindaSecimler',
        11 => 'hangiHakKacKereSecildi',
        12 => 'getOrders',
        13 => 'secimSonuclari',
        14 => 'haklariniOnaylayanKisiListesi',
        15 => 'anketCevaplariOzetRaporu',
    ];

    // public static function getReport($reportId)
    // {
    //     $method = self::$reportMethods[$reportId] ?? null;

    //     if ($method && method_exists(Report::class, $method)) {
    //         return Report::$method();
    //     }

    //     throw new \Exception("Rapor bulunamadı.");
    // }

    public static function getReport($reportId, $additionalParams = null)
    {
        $method = self::$reportMethods[$reportId] ?? null;

        if ($method && method_exists(Report::class, $method)) {
            // Ekstra parametreleri kontrol edin ve ilgili metoda aktarın
            return $additionalParams ? Report::$method($additionalParams) : Report::$method();
        }

        throw new \Exception("Rapor bulunamadı.");
    }


    public function index($id, Request $request)
    {
        try {
            // $reportData = ReportController::getReport($id);
            $benefitId = $request->query('benefitId');

            $reportData = ReportController::getReport($id, $benefitId);

            array_walk($reportData['data'], function (&$row) {
                // if (property_exists($row, "Real Balance")) {
                //     if ($row->{"Real Balance"} === null || $row->{"Real Balance"} === '' || $row->{"Real Balance"} === "0.0" || $row->{"Real Balance"} === 0.0) {
                //         $row->{"Real Balance"} = '0';
                //     }
                // } else {
                //     $row->{"Real Balance"} = '0';
                // }

                if (is_array($row)) {
                    // $row bir dizi ise, array_key_exists ile kontrol
                    if (array_key_exists("Real Balance", $row)) {
                        if ($row["Real Balance"] === null || $row["Real Balance"] === '' || $row["Real Balance"] === "0.0" || $row["Real Balance"] === 0.0) {
                            $row["Real Balance"] = '0';
                        }
                    }
                } elseif (is_object($row)) {
                    // $row bir nesne ise, property_exists ile kontrol
                    if (property_exists($row, "Real Balance")) {
                        if ($row->{"Real Balance"} === null || $row->{"Real Balance"} === '' || $row->{"Real Balance"} === "0.0" || $row->{"Real Balance"} === 0.0) {
                            $row->{"Real Balance"} = '0';
                        }
                    }
                }
            });

            $fileName = 'report_' . ($reportData['name'] ?? 'unknown') . '.xlsx';

            return Excel::download(new class($reportData['data'], $reportData['columns']) implements FromCollection, WithHeadings, WithStyles {
                private $data;
                private $headings;

                public function __construct($data, $headings)
                {
                    $this->data = $data;
                    $this->headings = $headings;
                }


                public function collection()
                {

                    $headings=$this->headings;
                    if (isset($headings[3]) && $headings[3] == 'hakbazinda') {
                        $newData = collect();
                        foreach ($this->data as $item) {


                            $newItem = [];

                            $newItem['Name'] = $item->name;

                            $newItem[$this->headings()[1]] = $item->Adet !== null ? $item->Adet : '-';
                            // $newItem[$this->headings()[1]] = $item->Adet;

                            $newData->push($newItem);
                        }

                        return $newData;
                    }else{
                        return collect($this->data);
                    }


                }


                public function headings(): array
                {

                    $headings=$this->headings;
                    if (isset($headings[3]) && $headings[3] == 'hakbazinda') {
                        $dynamicHeading = isset($this->data[0]->Hak) ? $this->data[0]->Hak : 'Default Heading';
                        return ['Name', $dynamicHeading];
                    }else{
                        return $this->headings;
                    }

                }

                public function styles(Worksheet $sheet)
                {
                    return [
                        // Style the first row as bold text.
                        1 => ['font' => ['bold' => true]],
                    ];
                }
            }, $fileName);
        } catch (\Exception $e) {

            return response()->json(['error' => $e->getMessage()], 404);
        }
    }
}
