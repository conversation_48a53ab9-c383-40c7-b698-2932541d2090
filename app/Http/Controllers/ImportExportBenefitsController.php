<?php

namespace App\Http\Controllers;

use App\Models\MemberBenefitCart;
use App\Models\QuestionAnswer;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use App\Models\Member;
use App\Models\Benefit;
use App\Models\Order;
use Illuminate\Http\Request;
use App\Models\MemberBenefitPref;
use App\Models\MemberBenefit;
use App\Models\BenefitOption;
use App\Models\OrderItem;
use App\Models\CustomBenefitsSale;
use App\Models\CustomBalance;
use App\Models\CustomBenefitsSpending;
use App\Models\CustomProcessBenefit;
use App\Models\PreviousMemberBenefitCart;
use App\Models\PreviousMemberBenefitsCartLeave;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class ImportExportBenefitsController extends Controller
{
    public function exportExcel()
    {
        $exportData = new class implements FromCollection, WithHeadings
        {
            public function collection()
            {
                $leaveBenefit = Benefit::where('type', '5')->first();
                $benefitLeaveId = $leaveBenefit->id ?? null;

                $benefits = Benefit::with('benefitOptions')
                    ->orderBy('benefit_group_id')
                    ->orderBy('id')
                    ->get();

                $exportData = collect([]);

                $row = [];
                $row['IDENTITY_NUMBER'] = '';
                $row['NAME'] = '';

                foreach ($benefits as $benefit) {
                    if ($benefit->benefitOptions->isEmpty()) {
                        $header = implode('/', [
                            $benefit->benefit_group_id,
                            $benefit->id,
                            0,
                            $benefit->name,
                            '', // boş opsiyon adı
                            $benefit->benefit_fin_code ?? '{benefit_fin_code}',
                            $benefit->benefit_fin_type ?? '{benefit_fin_typ}',
                            $benefit->benefit_fin_nr ?? '{benefit_fin_nr}',
                        ]);
                        $row[$header] = '';

                        if ($benefit->id == $benefitLeaveId) {
                            $headerLeave = implode('/', [
                                $benefit->benefit_group_id,
                                $benefit->id,
                                -1,
                                $benefit->name,
                                '',
                                $benefit->benefit_fin_code ?? '{benefit_fin_code}',
                                $benefit->benefit_fin_typ ?? '{benefit_fin_typ}',
                                $benefit->benefit_fin_nr ?? '{benefit_fin_nr}',
                            ]);
                            $row[$headerLeave] = '';
                        }
                    } else {
                        foreach ($benefit->benefitOptions as $option) {
                            $header = implode('/', [
                                $benefit->benefit_group_id,
                                $benefit->id,
                                $option->id,
                                $benefit->name,
                                $option->name,
                                $option->benefit_fin_code ?? '{benefit_fin_code}',
                                $option->benefit_fin_typ ?? '{benefit_fin_typ}',
                                $option->benefit_fin_nr ?? '{benefit_fin_nr}',
                            ]);
                            $row[$header] = '';
                        }
                    }
                }

                $exportData->push($row);
                return $exportData;
            }

            public function headings(): array
            {
                $headings = ['IDENTITY_NUMBER', 'NAME'];
                $benefitHeaders = [];

                $leaveBenefit = Benefit::where('type', '5')->first();
                $benefitLeaveId = $leaveBenefit->id ?? null;

                $benefits = Benefit::with('benefitOptions')
                    ->orderBy('benefit_group_id')
                    ->orderBy('id')
                    ->get();

                foreach ($benefits as $benefit) {
                    if ($benefit->benefitOptions->isEmpty()) {
                        $benefitHeaders[] = implode('/', [
                            $benefit->benefit_group_id,
                            $benefit->id,
                            0,
                            $benefit->name,
                            '',
                            $benefit->benefit_fin_code ?? '{benefit_fin_code}',
                            $benefit->benefit_fin_typ ?? '{benefit_fin_typ}',
                            $benefit->benefit_fin_nr ?? '{benefit_fin_nr}',
                        ]);
                        if ($benefit->id == $benefitLeaveId) {
                            $benefitHeaders[] = implode('/', [
                                $benefit->benefit_group_id,
                                $benefit->id,
                                -1,
                                $benefit->name,
                                '',
                                $benefit->benefit_fin_code ?? '{benefit_fin_code}',
                                $benefit->benefit_fin_typ ?? '{benefit_fin_typ}',
                                $benefit->benefit_fin_nr ?? '{benefit_fin_nr}',
                            ]);
                        }
                    } else {
                        foreach ($benefit->benefitOptions as $option) {
                            $benefitHeaders[] = implode('/', [
                                $benefit->benefit_group_id,
                                $benefit->id,
                                $option->id,
                                $benefit->name,
                                $option->name,
                                $option->benefit_fin_code ?? '{benefit_fin_code}',
                                $option->benefit_fin_typ ?? '{benefit_fin_typ}',
                                $option->benefit_fin_nr ?? '{benefit_fin_nr}',
                            ]);
                        }
                    }
                }

                return array_merge($headings, $benefitHeaders);
            }
        };

        return Excel::download($exportData, 'members_benefits.xlsx');
    }

    public function importExcel(Request $request)
    {
        if (! $request->hasFile('excel_file')) {
            session()->flash('error', 'Dosya yüklenmedi');
            return redirect()->to('admin/members/excel-actions-benefit');
        }

        $leaveBenefit = Benefit::where('type', '5')->first();
        $benefitLeaveId = $leaveBenefit->id ?? null;

        $file       = $request->file('excel_file');
        $collection = Excel::toCollection([], $file)->first();
        $headers    = $collection->first();
        $rows       = $collection->slice(1);

        $identityNumbers = $rows->pluck(0)->unique();
        $members = Member::whereIn('identity_number', $identityNumbers)
            ->get()
            ->keyBy('identity_number');

        // Temizlik
        MemberBenefitPref::truncate();
        MemberBenefit::truncate();

        $benefitRows = [];
        $prefRows = [];

        foreach ($rows as $row) {
            $identity = $row[0];
            if (! isset($members[$identity])) {
                continue;
            }

            $member_id = $members[$identity]->id;

            for ($i = 2, $len = count($row); $i < $len; $i++) {
                $cell   = trim($row[$i]);

                $parts  = explode('/', $headers[$i]);

                $benefit_id = $parts[1] ?? null;
                $option_id  = $parts[2] ?? 0;
                $isLeave    = $benefit_id == $benefitLeaveId;

                // Yeni eklenen kod bilgileri
                $benefit_fin_code = $parts[5] ?? null;
                $benefit_fin_type = $parts[6] ?? null;
                $benefit_fin_nr   = $parts[7] ?? null;

                if ($cell === '' || in_array($cell, ['-', '–', '—', '‐', '−'], true)) {
                    continue;
                }

                // Tip 5 izin için numeric değer member_benefit’e amount olarak yazılır
                if ($isLeave && is_numeric($cell)) {
                    $benefitRows[] = [
                        'member_id'          => $member_id,
                        'benefit_id'         => $benefit_id,
                        'benefit_option_id'  => 0,
                        'amount'             => $cell,
                        'update_status'      => 1,
                        'benefit_fin_code'   => $benefit_fin_code,
                        'benefit_fin_type'   => $benefit_fin_type,
                        'benefit_fin_nr'     => $benefit_fin_nr,
                    ];
                    continue;
                }



                if ($cell == '!+') {

                    // SEÇİLİ hak — MemberBenefit
                    $benefitRows[] = [
                        'member_id'          => $member_id,
                        'benefit_id'         => $benefit_id,
                        'benefit_option_id'  => $option_id,
                        'amount'             => 0,
                        'update_status'      => 1,
                        'benefit_fin_code'   => $benefit_fin_code,
                        'benefit_fin_type'   => $benefit_fin_type,
                        'benefit_fin_nr'     => $benefit_fin_nr,
                    ];
                } elseif ($cell === '+') {
                    // GÖRÜNÜR opsiyon — MemberBenefitPref
                    $prefRows[] = [
                        'member_id'         => $member_id,
                        'benefit_id'        => $benefit_id,
                        'benefit_option_id' => $option_id,
                        'price'             => 0,
                        'is_visible'        => 1,
                    ];
                }
            }
        }

        if (!empty($benefitRows)) {
            MemberBenefit::upsert(
                $benefitRows,
                ['member_id', 'benefit_id', 'benefit_option_id'],
                ['amount', 'update_status', 'benefit_fin_code', 'benefit_fin_type', 'benefit_fin_nr']
            );
        }

        if (! empty($prefRows)) {
            MemberBenefitPref::upsert(
                $prefRows,
                ['member_id', 'benefit_id', 'benefit_option_id'],
                ['price', 'is_visible']
            );
        }

        session()->flash('success', 'İçerik başarıyla yüklendi.');
        return redirect()->to('admin/members/excel-actions-benefit');
    }

    public function updateExcel(Request $request)
    {
        if (! $request->hasFile('excel_file')) {
            session()->flash('error', 'Dosya yüklenmedi');
            return redirect()->to('admin/members/excel-actions-benefit');
        }

        $leaveBenefit = Benefit::where('type', '5')->first();
        $benefitLeaveId = $leaveBenefit->id ?? null;

        $file       = $request->file('excel_file');
        $collection = Excel::toCollection([], $file)->first();
        $headers    = $collection->first();
        $rows       = $collection->slice(1);

        $identityNumbers = $rows->pluck(0)->unique();
        $members = Member::whereIn('identity_number', $identityNumbers)
            ->get()
            ->keyBy('identity_number');

        $benefitRows = [];
        $prefRows    = [];

        foreach ($rows as $row) {
            $identity = $row[0];
            if (! isset($members[$identity])) {
                continue;
            }

            $member_id = $members[$identity]->id;

            for ($i = 2, $len = count($row); $i < $len; $i++) {
                $cell  = trim($row[$i]);
                $parts = explode('/', $headers[$i]);

                $benefit_id = $parts[1] ?? null;
                $option_id  = $parts[2] ?? 0;
                $isLeave    = $benefit_id == $benefitLeaveId;

                $benefit_fin_code = $parts[5] ?? null;
                $benefit_fin_type = $parts[6] ?? null;
                $benefit_fin_nr   = $parts[7] ?? null;

                $benefit_fin_code = ($benefit_fin_code == '{benefit_fin_code}') ? null : $benefit_fin_code;
                $benefit_fin_type = ($benefit_fin_type == '{benefit_fin_type}') ? null : $benefit_fin_type;
                $benefit_fin_nr   = ($benefit_fin_nr == '{benefit_fin_nr}') ? null : $benefit_fin_nr;

                // Sil mevcut kayıtları
                MemberBenefit::where([
                    'member_id' => $member_id,
                    'benefit_id' => $benefit_id,
                    'benefit_option_id' => $option_id,
                ])->delete();

                MemberBenefitPref::where([
                    'member_id' => $member_id,
                    'benefit_id' => $benefit_id,
                    'benefit_option_id' => $option_id,
                ])->delete();

                // Type 5 izin: amount yazılır
                if ($isLeave && is_numeric($cell)) {
                    $benefitRows[] = [
                        'member_id'          => $member_id,
                        'benefit_id'         => $benefit_id,
                        'benefit_option_id'  => 0,
                        'amount'             => (float) $cell,
                        'update_status'      => 1,
                        'benefit_fin_code'   => $benefit_fin_code,
                        'benefit_fin_type'   => $benefit_fin_type,
                        'benefit_fin_nr'     => $benefit_fin_nr,
                    ];
                    continue;
                }

                if ($cell === '-') {
                    continue;
                }

                if ($cell === '!+') {
                    // Seçili hak
                    $benefitRows[] = [
                        'member_id'          => $member_id,
                        'benefit_id'         => $benefit_id,
                        'benefit_option_id'  => $option_id,
                        'amount'             => 0,
                        'update_status'      => 1,
                        'benefit_fin_code'   => $benefit_fin_code,
                        'benefit_fin_type'   => $benefit_fin_type,
                        'benefit_fin_nr'     => $benefit_fin_nr,
                    ];
                } elseif ($cell === '+') {
                    // Görünür hak
                    $prefRows[] = [
                        'member_id'         => $member_id,
                        'benefit_id'        => $benefit_id,
                        'benefit_option_id' => $option_id,
                        'price'             => 0,
                        'is_visible'        => 1,
                    ];
                }
            }
        }

        if (! empty($benefitRows)) {
            MemberBenefit::upsert(
                $benefitRows,
                ['member_id', 'benefit_id', 'benefit_option_id'],
                ['amount', 'update_status', 'benefit_fin_code', 'benefit_fin_type', 'benefit_fin_nr']
            );
        }

        if (! empty($prefRows)) {
            MemberBenefitPref::upsert(
                $prefRows,
                ['member_id', 'benefit_id', 'benefit_option_id'],
                ['price', 'is_visible']
            );
        }

        session()->flash('success', 'Güncelleme başarıyla tamamlandı.');
        return redirect()->to('admin/members/excel-actions-benefit');
    }
    private function getExpectedHeaders(): array
    {
        $headings = ['IDENTITY_NUMBER', 'NAME'];
        $benefitHeaders = [];

        $leaveBenefit = Benefit::where('type', '5')->first();
        $benefitLeaveId = $leaveBenefit->id ?? null;

        $benefits = Benefit::with('benefitOptions')->get();

        foreach ($benefits as $benefit) {
            if ($benefit->benefitOptions->isEmpty()) {
                $benefitHeaders[] = implode('/', [
                    $benefit->benefit_group_id,
                    $benefit->id,
                    0,
                    $benefit->name,
                    '',
                    $benefit->benefit_fin_code ?? '',
                    $benefit->benefit_fin_typ ?? '',
                    $benefit->benefit_fin_nr ?? '',
                ]);

                if ($benefit->id == $benefitLeaveId) {
                    $benefitHeaders[] = implode('/', [
                        $benefit->benefit_group_id,
                        $benefit->id,
                        -1,
                        $benefit->name,
                        '',
                        $benefit->benefit_fin_code ?? '',
                        $benefit->benefit_fin_typ ?? '',
                        $benefit->benefit_fin_nr ?? '',
                    ]);
                }
            } else {
                foreach ($benefit->benefitOptions as $option) {
                    $benefitHeaders[] = implode('/', [
                        $benefit->benefit_group_id,
                        $benefit->id,
                        $option->id,
                        $benefit->name,
                        $option->name,
                        $option->benefit_fin_code ?? '',
                        $option->benefit_fin_typ ?? '',
                        $option->benefit_fin_nr ?? '',
                    ]);
                }
            }
        }

        return array_merge($headings, $benefitHeaders);
    }

    private function isValidHeaders($headers, $expectedHeaders): bool
    {
        $clean = fn($arr) => array_map(fn($h) => preg_replace('/\s+/', '', $h), $arr);
        return $clean($headers->toArray()) === $clean($expectedHeaders);
    }
}
