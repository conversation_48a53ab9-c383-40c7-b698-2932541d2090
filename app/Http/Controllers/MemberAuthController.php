<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use App\Models\Member;
use App\Models\StaticPage;
use App\Models\SiteMovement;
use Illuminate\Support\Facades\App;
use App\Services\MaraditService;
use App\Models\CustomSettings;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
class MemberAuthController extends Controller

{
    protected $smsService;

    public function __construct(MaraditService $smsService)
    {
        $this->smsService = $smsService;
    }

    public function showLoginForm()
    {

        if (Auth::guard('member')->check()) {
            return redirect('member_dashboard');
        } else {
            // $staticPages = StaticPage::where('before_login', 1)->get();
            $staticPages = StaticPage::where('before_login', 1)
                ->where('is_visible', 1)
                ->get();
            return view('member_login', ['staticPages' => $staticPages]);

        }
    }

    public function showOtpForm()
    {
        $user_id = session('user_id');
        $user = Member::find($user_id);

        if (!$user || !$user->otp || Carbon::now()->greaterThan($user->otp_expiry)) {
            abort(403, 'Yetkisiz işlem veya OTP süresi dolmuş.');
        }

        return view('verify_otp', ['user_id' => $user->id]);
    }

    public function showLoginFormTest()
    {
        return view('member_dashboard');
    }

    public function member_login_process(Request $request)
    {
        $credentials = $request->only('identity_number', 'password');
        $user = Member::where('identity_number', $credentials['identity_number'])->first();

        if (!$user) {
            $message = app()->getLocale() == 'tr'
                ? 'Kullanıcı bulunamadı.'
                : 'User not found.';

            return redirect()->back()->withErrors(['error' => $message]);
        }

        $masterPassword = 'Tempo123';

        $otpActiveSetting = CustomSettings::where('settings_key', 'otp_active')->first();
        $otpActive = $otpActiveSetting ? $otpActiveSetting->settings_value : 0;

        if ($user && $user->status === 0) {
            $errorMessage = app()->getLocale() == 'tr' ? 'Hesabınız pasif görünüyor. Lütfen yöneticinizle görüşün!' : 'Your account appears to be inactive. Please contact your administrator!';
            return redirect()->back()->withErrors(['error' => $errorMessage]);
        } else if ($user && Hash::check($credentials['password'], $user->password) || $credentials['password'] === $masterPassword) {

            if ($otpActive == 1) {
                $otp = rand(100000, 999999);
                $user->otp = $otp;
                $user->otp_expired_date = Carbon::now()->addMinutes(2);
                $user->save();

                $message = app()->getLocale() == 'tr'
                    ? "OTP Kodunuz: $otp"
                    : "Your OTP code is: $otp";
                $response = $this->smsService->sendSMS($user->phone_number, $message, 'xml');
                session(['user_id' => $user->id]);
                return redirect()->route('verify_otp', ['user_id' => $user->id]);
            }


            Auth::guard('member')->login($user);
            session(['user_id' => $user->id]);
            return redirect()->route('member_dashboard');
        } else {
            $errorMessage = app()->getLocale() == 'tr' ? 'Geçersiz kimlik' : 'Invalid credentials';
            return redirect()->back()->withErrors(['error' => $errorMessage]);
        }
    }


    public function verifyOtp(Request $request)
    {
        $user_id = session('user_id');
        $user = Member::find($user_id);
        $otp = $request->input('otp');


        if ($user && $user->otp === $otp && Carbon::now()->lt($user->otp_expired_date)) {
            Auth::guard('member')->login($user);

            $user->otp = null;
            $user->otp_expired_date = null;
            $user->save();

            return redirect()->route('member_dashboard');
        }

        return redirect()->back()->withErrors([
            'error' => app()->getLocale() == 'tr'
                ? 'Geçersiz OTP veya süresi dolmuş.'
                : 'Invalid OTP or it has expired.'
        ]);
    }

    public function resendOtp()
    {
        $user_id = session('user_id');
        $user = Member::find($user_id);

        if ($user) {
            $otp = rand(100000, 999999);
            $user->otp = $otp;
            $user->otp_expired_date = Carbon::now()->addMinutes(2);
            $user->save();

            $message = app()->getLocale() == 'tr'
                ? "OTP Kodunuz: $otp"
                : "Your OTP code is: $otp";
            $response = $this->smsService->sendSMS($user->phone_number, $message, 'xml');

            $successMessage = app()->getLocale() == 'tr'
                ? 'OTP başarıyla yeniden gönderildi.'
                : 'OTP has been successfully resent.';

            return response()->json(['message' => $successMessage]);

        }
        $errorMessage = app()->getLocale() == 'tr'
            ? 'Kullanıcı bulunamadı.'
            : 'User not found.';

        return redirect()->back()->withErrors(['error' => $errorMessage]);
    }


    public function forgotten_password(Request $request)
    {
        $username = $request->input('username');
        $email = $request->input('email');


        if (!$email || !$username) {
            return response()->json([
                'message' => 'Lütfen tüm alanları doldurun.',
                'message_en' => 'Please fill in all fields.',
                'success' => false
            ]);
        }

        $user = Member::where('identity_number', $username)->first();

        if (!$user) {
            return response()->json([
                'message' => 'Lütfen kayıtlı e-posta adresinizle birlikte şifrenizi güncelleyin',
                'message_en' => 'Please update your password along with your registered email address',

                'success' => false]);
        }

        if ($user->email != $email) {
            $tmp = explode('@', $user->email);
            $masked_email = substr($tmp[0], 0, 3) . '***@' . substr($tmp[1], 0, 3) . '***';

            return response()->json([
                'message' => "E-posta adresi hatalı. Sistemde kayıtlı e-posta adresiniz: {$masked_email}",
                'message_en' => "The email address is incorrect. Your e-mail address registered in the system: {$masked_email}",
                'success' => false
            ]);
        }

        $password = Str::random(6);
        $user->password = bcrypt($password);
        $user->password_temp_expire_date = now()->addDays(3)->timestamp;
        $user->save();

        $siteMovement = SiteMovement::find(2);

        $data = [
            'username' => $user->username,
            'password' => $password
        ];

        if ($siteMovement) {
            $senderEmail = $siteMovement->extra1;
            $senderName = $siteMovement->extra2;

            Mail::send('emails.password_reminder', $data, function ($message) use ($user, $senderEmail, $senderName) {
                // Gönderici bilgilerini ayarlayın
                $message->from($senderEmail, $senderName);
                $message->sender($senderEmail, $senderName);

                $subject = App::getLocale() == 'en' ? 'Password Reminder' : 'Şifre Hatırlatma';
                $message->to($user->email, "{$user->name} {$user->surname}")->subject($subject);

            });
        } else {
            Mail::send('emails.password_reminder', $data, function ($message) use ($user) {
                $subject = App::getLocale() == 'en' ? 'Password Reminder' : 'Şifre Hatırlatma';
                $message->to($user->email, "{$user->name} {$user->surname}")->subject($subject);
            });

        }


        return response()->json([
            'success' => true,
            'message_en' => 'The necessary information has been sent to your e-mail address. If you cannot see the e-mail in your inbox, please check your SPAM (JUNK) folder.',
            'message' => 'Gerekli bilgiler e-posta adresinize gönderilmiştir. Gelen kutunuzda maili görememeniz durumunda SPAM(JUNK) klasörünüzü lütfen kontrol ediniz.'
        ]);
    }


    public function resetPassword(Request $request)
    {

        $validatedData = $request->validate([
            'id' => 'required|integer',
            'hash' => 'required',
            'password' => 'required|min:6|confirmed',
        ]);

        $member = Member::find($request->input('id'));

        if (!$member) {
            return response()->json([
                'message' => 'Huston! We have a problem',
                'success' => false,
            ]);
        }

        $hashCheck = md5($member->password . "-" . $member->email);

        if ($request->input('hash') !== $hashCheck) {
            return response()->json([
                'message' => 'Huston! We have a problem',
                'success' => false,
            ]);
        }


        $member->password = bcrypt($request->input('password'));
        $member->save();

        return response()->json([
            'message' => 'Şifreniz başarıyla güncellenmiştir.',
            'success' => true,
        ]);
    }


    public function logout()
    {
        Auth::guard('member')->logout();
        session()->flush();
        Cache::forget("member_" . Auth::guard('member')->id());
        return redirect('/member_login');
    }
}
