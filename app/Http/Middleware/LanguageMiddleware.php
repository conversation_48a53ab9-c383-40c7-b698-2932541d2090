<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Helpers\CustomSettingsHelper;

class LanguageMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, Closure $next)
    {

        $defaultLanguage = 'tr';
        $customSettings = new CustomSettingsHelper();
        $languageSetting = $customSettings->getText('site_language');

        if ($languageSetting) {
            $languages = explode(',', $languageSetting);
            $defaultFromSettings = trim($languages[0]);
        } else {
            $defaultFromSettings = $defaultLanguage;
        }

        if (!session()->has('app_language')) {
            session(['app_language' => $defaultFromSettings]);
            App::setLocale($defaultFromSettings);
        } else {
            App::setLocale(session('app_language'));
        }

        return $next($request);
    }

}
