<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\SiteMovement;
use Illuminate\Support\Facades\Cache;
class CheckSiteMovement
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $siteMovement = Cache::remember('site_movement_status', 3600, function () {
            return SiteMovement::find(1);
        });
        if ($siteMovement && $siteMovement->extra1 == 1) {
            return redirect()->route('site_closed');
        }

        return $next($request);
    }
}
