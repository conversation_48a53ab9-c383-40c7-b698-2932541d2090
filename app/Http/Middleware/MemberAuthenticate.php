<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
class MemberAuthenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (Auth::guard('member')->guest()) {
            return redirect()->route('member_login');
        }

        $userId = Auth::guard('member')->id();
        $user = Cache::member($userId)
            ->remember("member_{$userId}", 600, function () use ($userId) {
                return Auth::guard('member')->user();
            });

        return $next($request);
    }
}
