<?php

namespace App\Models;
use App\Models\CustomBenefit;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Member;
use App\Models\Benefit;
use App\Models\MemberBenefitCart;
use App\Models\MemberBenefit;
use Illuminate\Support\Facades\DB;

class CustomBenefitsSpending extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = ["id"];
    protected $table = 'custom_benefits_spending';

    public static function clear($member_id)
    {
        return self::where('member_id', $member_id)->delete();
    }
}
