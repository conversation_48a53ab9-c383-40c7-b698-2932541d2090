<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MemberBenefitBalanceLedger extends Model
{
    protected $table = 'member_benefit_balance_ledger';

    protected $fillable = [
        'member_id',
        'benefit_id',
        'benefit_option_id',
        'fin_code',
        'fin_type',
        'operation_type',
        'price_brut',
        'price_net',
        'reference_price_net',
        'reference_price_brut',
        'used_from_positive_balance',
        'used_from_sales_earning',
        'refunded_to_balance',
        'sale_net',
        'sale_brut',
    ];
}
