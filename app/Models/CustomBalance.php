<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class CustomBalance extends Model
{

    use HasFactory;

    protected $table = 'custom_balance';
    protected $guarded = ["id"];

    public function getCustomBalance($member_id)
    {
        $customBalance = DB::table('custom_balance')
            ->where('member_id', $member_id)
            ->orderBy('id', 'desc')
            ->first();

        return $customBalance;
    }

    public static function clear($member_id)
    {
        return self::where('member_id', $member_id)->delete();
    }

}
