<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;
    protected $table = 'order_items';
    public $timestamps = false;
    protected $guarded = ["id"];

    public static function getItemsByOrder(int $order_id)
    {
        return self::where('order_id', $order_id)->get();
    }
}
