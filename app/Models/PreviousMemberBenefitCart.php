<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class PreviousMemberBenefitCart extends Model
{
    use HasFactory;
    protected $table = 'previous_member_benefits_cart';
    protected $guarded = ["id"];


    public function getPreviousMemberBenefitsCart($member_id)
    {
        $basket = DB::table('previous_member_benefits_cart')
            ->where('member_id', $member_id)
            ->get()
            ->toArray();

        $basket_categorize=[];
        foreach($basket as $key => $value)
        {
            $basket_categorize[$value->benefit_id]=(array) $value;
        }

        return $basket_categorize;
    }

    public function getPreviousMemberBenefit($member_id,$benefit_id)
    {
        $basket = DB::table('previous_member_benefits_cart')
            ->select('amount')
            ->where('member_id', $member_id)
            ->where('benefit_id', $benefit_id)
            ->get()
            ->first();

        return $basket;
    }

    public static function clear($member_id)
    {
        return self::where('member_id', $member_id)->delete();
    }

}
