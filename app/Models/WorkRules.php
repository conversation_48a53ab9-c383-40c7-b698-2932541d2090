<?php

namespace App\Models;
use App\Models\Member;
use App\Models\MemberBenefit;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use App\Models\Benefit;
use App\Models\MemberBenefitCart;
use Illuminate\Support\Facades\DB;
use App\Models\MemberBenefitPref;
use App\Models\BenefitOption;


class WorkRules extends Model
{
    public function isAvailable($member_id,$option_id,$benefit_id,$rule=null)
    {
        $status=false;
        $message="";
        $message_en="";
        if($rule==null || $rule=="0"){
            $status=true;
        }

        if($rule=="1"){

            $benefitModel = new Benefit();

            $benefitDetails = $benefitModel->find($benefit_id);
            if(isset($benefitDetails) && !empty($benefitDetails)){

            if($benefitDetails['benefit_group_id']!="18"){

                if($benefitDetails['benefit_group_id']=="19"){

                    $benefitCartModel = new MemberBenefitCart();
                    $optionModel= new BenefitOption();
                    $memberBenefitModel = new MemberBenefit();

                    $benefitCarts = $benefitCartModel->where('member_id', $member_id)->where('benefit_id', 123)->first();

                    if(!empty($benefitCarts)){
                        $main_option_id_details=$optionModel->getOptionDetails($benefitCarts->benefit_option_id);
                    }else{
                        $member_benefits=$memberBenefitModel->where('member_id', $member_id)->where('benefit_id', 123)->first();
                        $main_option_id_details=$optionModel->getOptionDetails($member_benefits->benefit_option_id);
                    }

                    $request_option_id_details=$optionModel->getOptionDetails($option_id);

                    if($request_option_id_details->name==$main_option_id_details->name || $request_option_id_details->name_en==$main_option_id_details->name_en){
                        $status=true;
                    }else if($request_option_id_details->name=="İstemiyorum" || $request_option_id_details->name_en=="I don't want"){
                        $status=true;
                    }else{
                        $status=false;
                        $message="Çalışan hangi paketi seçerse bağımlıların sigorta paketi (eş ve çocuklar) aynı olmalıdır. Sadece eş veya çocuklar için istemiyorum seçeneği seçilebilir.";
                        $message_en="Whichever working part is active, the insurance package of the disabled ones (spouse and children) must be the same. The I don't want option can be selected only for spouse or children.";
                    }

                }else{
                    $status=true;
                }

            }else{
                $benefitCartModel = new MemberBenefitCart();
                $optionModel= new BenefitOption();
                $memberBenefitModel = new MemberBenefit();

                $benefitCarts = $benefitCartModel->where('member_id', $member_id)->where('benefit_id', 123)->first();

                if(!empty($benefitCarts)){
                    $main_option_id_details=$optionModel->getOptionDetails($benefitCarts->benefit_option_id);
                }else{
                    $member_benefits=$memberBenefitModel->where('member_id', $member_id)->where('benefit_id', 123)->first();
                    $main_option_id_details=$optionModel->getOptionDetails($member_benefits->benefit_option_id);
                }

                $request_option_id_details=$optionModel->getOptionDetails($option_id);


                if($request_option_id_details->name==$main_option_id_details->name && $request_option_id_details->name_en==$main_option_id_details->name_en){
                    $status=true;
                }else if($request_option_id_details->name=="İstemiyorum" && $request_option_id_details->name_en=="I don't want"){
                    $status=true;
                }else{
                    $status=false;
                    $message="Çalışan hangi paketi seçerse bağımlıların sigorta paketi (eş ve çocuklar) aynı olmalıdır. Sadece eş veya çocuklar için istemiyorum seçeneği seçilebilir.";
                    $message_en="Whichever working part is active, the insurance package of the disabled ones (spouse and children) must be the same. The I don't want option can be selected only for spouse or children.";
                }

            }
            }else{
                $status=true;
            }
        }


        if($rule=="2")
        {
            $benefitModel = new Benefit();

            $benefitDetails = $benefitModel->find($benefit_id);
            if($benefitDetails['benefit_group_id']!="18"){
                $status=true;
            }else{
                $benefitCartModel = new MemberBenefitCart();
                $optionModel= new BenefitOption();
                $memberBenefitModel = new MemberBenefit();

                $benefitCarts = $benefitCartModel->where('member_id', $member_id)->where('benefit_id', 123)->first();

                if(!empty($benefitCarts)){
                    $main_option_id_details=$optionModel->getOptionDetails($benefitCarts->benefit_option_id);
                }else{
                    $member_benefits=$memberBenefitModel->where('member_id', $member_id)->where('benefit_id', 123)->first();
                    $main_option_id_details=$optionModel->getOptionDetails($member_benefits->benefit_option_id);
                }

                $request_option_id_details=$optionModel->getOptionDetails($option_id);

                if($request_option_id_details->price >= $main_option_id_details->price){
                    $status=false;
                    $message_en="Whichever package the employee chooses, the insurance package of dependents (spouse and children) may be the same or lower than that package. A package with a higher amount than the employee cannot be selected for spouse and children. The I don't want option can be selected only for spouse or children.";
                    $message="Çalışan hangi paketi seçerse bağımlıların sigorta paketi (eş ve çocuklar) o paketle aynı veya daha düşük tutarlı olabilir. Eş ve çocuklar için çalışandan daha yüksek tutarlı bir paket seçilemez. Sadece eş veya çocuklar için istemiyorum seçeneği seçilebilir.";
                }else{
                    $status=true;
                }
            }

        }

        return [
            "status"=>$status,
            'message' => $message,
            'message_en' => $message_en
        ];
    }


    public function processCustomBalance($member_id, $option_id, $benefit_id, $rule = null, $netBrutMode = 1)
    {
        $status = false;

        if ($rule != "1") {
            return $status;
        }

        $m_member_benefit = new MemberBenefit();
        $m_basket = new MemberBenefitCart();
        $m_custom_balance = new CustomBalance();
        $m_previous_basket = new PreviousMemberBenefitCart();
        $m_custom_process_benefits = new CustomProcessBenefit();
        $bulutfin = app('App\Services\Bulutfin\BulutfinService');

        $member = Member::find($member_id);
        if (!$member) return false;

        if (!$benefit_id && $option_id) {
            $benefit_id = BenefitOption::getBenefitIdByOptionId($option_id);
        }

        $percentage = $member->percentage;
        $balance = optional($m_custom_balance->getCustomBalance($member_id))->amount ?? $member->positive_balance;

        $current_benefit = $m_basket->getCurrentMemberBenefit($member_id, $benefit_id);
        $previous_benefit = $m_previous_basket->getPreviousMemberBenefit($member_id, $benefit_id);
        $predefined_benefit = $m_member_benefit->getMemberBenefit($member_id, $benefit_id);
        $custom_process = $m_custom_process_benefits->getCustomProcessBenefitsForBenefitID($member_id, $benefit_id);

        $option = BenefitOption::find($option_id);
        $finCode = $option->benefit_fin_code ?? null;

        $priceNet = 0;
        $priceBrut = 0;

        if ($finCode) {
            $resp = $bulutfin->request('yillikozet');
            $list = array_merge(
                data_get($resp, 'Data.YanHakSumList', []),
                data_get($resp, 'Data.HSOHSumList', [])
            );

            $bulut = collect($list)->firstWhere('CODE', $finCode);
            $priceNet = (float) data_get($bulut, 'YanHakNet', 0);
            $priceBrut = (float) data_get($bulut, 'YanHakBurut', 0);
        }

        $priceUsed = $netBrutMode === 1 ? $priceNet : $priceBrut;

        $old = optional($previous_benefit)->amount ?? optional($predefined_benefit)->amount ?? 0;
        $current = optional($current_benefit)->amount ?? 0;

        $diff = $current - $old;
        if ($diff !== 0) {
            if ($netBrutMode === 1) {
                $amount = ($diff > 0 ? -$diff : abs($diff)) * $percentage / 100;
            } else {
                $amount = $diff > 0 ? -$diff : abs($diff);
            }

            // Önceki custom process varsa düşülmeli
            $prevProcessAmount = optional($custom_process)->amount ?? 0;
            if ($prevProcessAmount !== 0) {
                $balance -= $prevProcessAmount;
            }

            $balance += $amount;

            // Balance güncelle
            $m_custom_balance->updateOrInsert(
                ['member_id' => $member_id],
                [
                    'amount' => $balance,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );

            // Process güncelle
            $m_custom_process_benefits->updateOrInsert(
                [
                    'member_id' => $member_id,
                    'benefit_id' => $benefit_id
                ],
                [
                    'amount' => $amount,
                    'is_updated' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );

            $status = true;
        }

        return $status;
    }



    public function getBenefitPrice($optionId, $memberId, $defaultPrice)
    {
        // Önce member_benefits tablosuna bakıyoruz
        if ($optionId) {
            $benefitResult = DB::table('member_benefits')
                ->where('benefit_option_id', $optionId)
                ->where('member_id', $memberId)
                ->first();

            if ($benefitResult) {
                return $benefitResult->amount;
            }
        }

        // Eğer member_benefits'te sonuç bulunamazsa member_benefit_prefs'e bakıyoruz
        if ($optionId) {
            $prefsResult = DB::table('member_benefit_prefs')
                ->where('benefit_option_id', $optionId)
                ->where('member_id', $memberId)
                ->where('is_visible', 1)
                ->first();

            if ($prefsResult) {
                return $prefsResult->price;
            }
        }

        // Hiçbir sonuç bulunamazsa varsayılan fiyatı dönüyoruz
        return $defaultPrice;
    }


}
