<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MemberBenefitPref extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = ["id"];
    // protected $attributes = [
    //     'benefit_option_id' => 0,
    // ];

    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'member_id', 'id')->select('id', 'name');
    }

    public function benefit(): BelongsTo
    {
        return $this->belongsTo(Benefit::class, 'benefit_id', 'id')->select('id', 'name');
    }

    public static function getByMemberId($memberId)
    {
        return self::where('member_id', $memberId)->get();
    }

    public function benefitOption()
    {
        return $this->belongsTo(BenefitOption::class, 'benefit_option_id');
    }

    public function getCheckBenefitOptionPref($optionId, $memberId, $price)
    {
        if ($optionId) {
            $selectedPrice = $this->where('benefit_option_id', $optionId)
                                  ->where('member_id', $memberId)
                                  ->where('is_visible', 1)
                                  ->value('price');

            return $selectedPrice ? $selectedPrice : $price;
        } else {
            return $price;
        }
    }

    public function getCheckBenefitOptionPrefByBenefitID($optionId,$benefit_id,$memberId, $price)
    {
        if ($benefit_id) {
            $selectedPrice = $this->where('benefit_option_id', $optionId)
                ->where('member_id', $memberId)
                ->where('member_id', $memberId)
                ->where('is_visible', 1)
                ->value('price');

            return $selectedPrice ? $selectedPrice : $price;
        } else {
            return $price;
        }
    }

    public function getVisibleBenefitOptionsByMemberId($memberId)
    {
        $benefitOptions = $this->where('member_id', $memberId)
            ->where('is_visible', 1)
            ->pluck('benefit_option_id');

        return $benefitOptions;
    }

    public function getOptionsByBenefitId($memberId,$benefitId)
    {
        $benefitOptions = $this->where('member_id', $memberId)
            ->where('benefit_id', $benefitId)
            ->pluck('benefit_option_id');

        return $benefitOptions;
    }

}
