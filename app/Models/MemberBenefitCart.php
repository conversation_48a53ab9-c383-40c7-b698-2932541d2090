<?php

namespace App\Models;

use App\Models\PreviousMemberBenefitCart;
use App\Traits\FamilyBenefitTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\CustomSettings;
use Illuminate\Support\Facades\Http;

use App\Models\Member;
use App\Models\Benefit;
use Illuminate\Support\Facades\Auth;
use App\Models\PreviousMemberBenefitsCartLeave;
use App\Models\CustomProcessBenefit;

use Carbon\Carbon;

class MemberBenefitCart extends Model
{
    use HasFactory;
    use FamilyBenefitTrait;


    protected $table = 'member_benefits_cart';
    public $timestamps = false;
    protected $guarded = ["id"];

    // public static function getBenefits($member_id)
    // {
    //     if(!$member_id) {
    //         die('No member_id on getBenefits');
    //     }

    //     $data = Benefit::select(
    //         'benefits.*',
    //         'benefits.id AS benefit_id',
    //         'benefit_groups.id as group_id',
    //         'benefit_groups.name AS group_name',
    //         'benefit_options.id as option_id',
    //         'benefit_options.name as option_name',
    //         DB::raw('COALESCE(p.price,benefit_options.price) as option_price'),
    //         'member_benefits.amount',
    //         DB::raw('
    //             IF(
    //                 has_flexible_price,
    //                 IF(flexible_price = "Adet",
    //                    COALESCE(p.price, benefits.price) * member_benefits.amount,
    //                    member_benefits.amount
    //                 ),
    //                 COALESCE(p.price,benefit_options.price, benefits.price)
    //             ) AS real_price'
    //         )
    //     )
    //     ->leftJoin('member_benefits', 'benefits.id', '=', 'member_benefits.benefit_id')
    //     ->leftJoin('benefit_options', 'benefit_options.id', '=', 'member_benefits.benefit_option_id')
    //     ->leftJoin('benefit_groups', 'benefits.benefit_group_id', '=', 'benefit_groups.id')
    //     ->leftJoin('member_benefit_prefs as p', function($join) use ($member_id) {
    //         $join->on('p.benefit_option_id', '=', 'benefit_options.id')
    //              ->orWhere(function($query) use ($member_id) {
    //                  $query->on('p.benefit_id', '=', 'member_benefits.benefit_id')
    //                        ->where('p.benefit_option_id', 0);
    //              })
    //              ->where('member_benefits.member_id', $member_id)
    //              ->where('p.member_id', $member_id)
    //              ->where('p.is_visible', 1);
    //     })
    //     ->where('member_benefits.member_id', $member_id)
    //     ->orderBy('benefit_groups.order_no', 'ASC')
    //     ->get();

    //     return $data;
    // }


    public static function getBenefitsPDF($memberId = null)
    {
        if (!$memberId) {
            $memberId = Member::get('id');
        }

        $query = DB::table('member_benefits_cart')
            ->selectRaw("
            benefits.*,
            benefits.id AS benefit_id,
            benefit_groups.id as group_id,
            benefit_groups.`name` AS group_name,
            benefit_options.`id` as option_id,
            benefit_options.`name` as option_name,
            COALESCE(p.price, benefit_options.price) as option_price,
            member_benefits_cart.amount,
            IF(
                has_flexible_price,
                IF(flexible_price = 'Adet',
                    COALESCE(p.price, benefits.price) * member_benefits_cart.amount,
                    member_benefits_cart.amount
                ),
                COALESCE(p.price, benefit_options.price, benefits.price)
            ) AS real_price
        ")
            ->where('member_benefits_cart.member_id', $memberId)
            ->where('member_benefits_cart.member_family_id', 0)
            ->orderBy('benefit_groups.order_no');

        // Tabloları birleştir
        $query->leftJoin('benefit_options', 'benefit_options.id', '=', 'member_benefits_cart.benefit_option_id');
        $query->leftJoin('benefits', 'member_benefits_cart.benefit_id', '=', 'benefits.id');
        $query->leftJoin('benefit_groups', 'benefits.benefit_group_id', '=', 'benefit_groups.id');
        $query->leftJoin('member_benefit_prefs as p', function ($join) use ($memberId) {
            $join->on('p.benefit_option_id', '=', 'benefit_options.id')
                ->orWhere(function ($query) use ($memberId) {
                    $query->on('p.benefit_id', '=', 'member_benefits_cart.benefit_id')
                        ->where('p.benefit_option_id', 0);
                })
                ->where('p.member_id', $memberId)
                ->where('p.is_visible', 1);
        });

        // Sorguyu çalıştır ve sonuçları al
        $results = $query->get();

        // Sonuçları düz bir diziye dönüştür
        $benefitsArray = $results->mapWithKeys(function ($item) {
            if (is_object($item)) {
                // Eğer $item bir nesne ise, array olarak dönüştür
                return [$item->id => (array)$item];
            } else {
                // Eğer $item zaten bir array ise, olduğu gibi dönüştür
                return [$item['id'] => $item];
            }
        });

        return $benefitsArray;


    }

    public static function getBenefits($memberId = null)
    {
        if (!$memberId) {
            $memberId = Member::get('id');
        }

        $benefits = DB::table('member_benefits_cart')
            ->join('benefits', 'member_benefits_cart.benefit_id', '=', 'benefits.id')
            ->leftJoin('benefit_options', 'benefit_options.id', '=', 'member_benefits_cart.benefit_option_id')
            ->leftJoin('benefit_groups', 'benefits.benefit_group_id', '=', 'benefit_groups.id')
            ->leftJoin('member_benefit_prefs as p', function ($join) use ($memberId) {
                $join->on('p.benefit_option_id', '=', 'benefit_options.id')
                    ->orWhere(function ($q) {
                        $q->on('p.benefit_id', '=', 'member_benefits_cart.benefit_id')
                            ->where('p.benefit_option_id', 0);
                    })
                    ->where('p.member_id', $memberId)
                    ->where('p.is_visible', 1);
            })
            ->where('member_benefits_cart.member_id', $memberId)
            ->where('member_benefits_cart.member_family_id', 0)
            ->orderBy('benefit_groups.order_no')
            ->selectRaw(<<<SQL
            /* 1) benefits tablosundaki tüm orijinal sütunlar */
            benefits.*,
            /* 2) ekstra almak istediğin finansal kolonlar */
            benefits.benefit_fin_typ,
            benefits.benefit_fin_nr,
            benefits.benefit_fin_code,
            benefits.benefit_bulutfin_mode,
            /* 3) gruplama ve seçenek alias’ları */
            benefits.id                            AS benefit_id,
            benefit_groups.id                     AS group_id,
            benefit_groups.name                   AS group_name,
            benefit_groups.name_en                AS group_name_en,
            benefit_options.id                    AS option_id,
            benefit_options.name                  AS option_name,
            benefit_options.name_en               AS option_name_en,
            COALESCE(p.price, benefit_options.price) AS option_price,
            member_benefits_cart.amount,
            /* 4) gerçek fiyat hesaplaması */
            IF(
              benefits.has_flexible_price,
              IF(benefits.flexible_price = 'Adet',
                 COALESCE(p.price, benefits.price) * member_benefits_cart.amount,
                 member_benefits_cart.amount
              ),
              COALESCE(p.price, benefit_options.price, benefits.price)
            ) AS real_price
        SQL
            )
            ->get();

        // benefit_id’ye göre gruplanmış şekilde dönmeye devam ediyor
        return $benefits->groupBy('benefit_id');
    }
    public static function getBenefitsOLD($memberId = null)
    {
        if (!$memberId) {
            $memberId = Member::get('id');
        }

        $benefits = DB::table('member_benefits_cart')
            ->selectRaw("
            benefits.*,
            benefits.id AS benefit_id,
            benefit_groups.id as group_id,
            benefit_groups.`name` AS group_name,
            benefit_groups.`name_en` AS group_name_en,
            benefit_options.`id` as option_id,
            benefit_options.`name` as option_name,
            benefit_options.`name_en` as option_name_en,
            COALESCE(p.price, benefit_options.price) as option_price,
            member_benefits_cart.amount,
            IF(
                has_flexible_price,
                IF(flexible_price = 'Adet',
                    COALESCE(p.price, benefits.price) * member_benefits_cart.amount,
                    member_benefits_cart.amount
                ),
                COALESCE(p.price, benefit_options.price, benefits.price)
            ) AS real_price
        ")
            ->join('benefits', 'member_benefits_cart.benefit_id', '=', 'benefits.id')
            ->leftJoin('benefit_options', 'benefit_options.id', '=', 'member_benefits_cart.benefit_option_id')
            ->leftJoin('benefit_groups', 'benefits.benefit_group_id', '=', 'benefit_groups.id')
            ->leftJoin('member_benefit_prefs as p', function ($join) use ($memberId) {
                $join->on('p.benefit_option_id', '=', 'benefit_options.id')
                    ->orWhere(function ($query) use ($memberId) {
                        $query->on('p.benefit_id', '=', 'member_benefits_cart.benefit_id')
                            ->where('p.benefit_option_id', 0);
                    })
                    ->where('p.member_id', $memberId)
                    ->where('p.is_visible', 1);
            })
            ->where('member_benefits_cart.member_id', $memberId)
            ->where('member_benefits_cart.member_family_id', 0)
            ->orderBy('benefit_groups.order_no')
            ->get();

        $benefitsGrouped = $benefits->groupBy('benefit_id');

        return $benefitsGrouped;

    }


    public function addBenefitsByOptionId($option_id, $member_id = null, $benefit_id = null, $priceNet = null, $priceBrut = null, $netBrutMode = 1)
    {
        $rulesValue = CustomSettings::getRules();
        $benefitModel = new Benefit();
        $member = Member::where('id', Auth::guard('member')->id())->first();

        if (!$member_id) {
            $member_id = $member->id;
        }

        if ($benefit_id === null) {
            $memberBenefit = new MemberBenefit();
            $benefit_id = $memberBenefit->getBenefitDataWithOptionID($option_id, $member_id);
        }

        if ($rulesValue === "1") {
            $this->getBasketForPrevious($member_id, $benefit_id, $option_id);
        }

        $checkResult = DB::table('member_benefits_cart')
            ->where('member_id', $member_id)
            ->where('benefit_option_id', $option_id)
            ->where('member_family_id', 0)
            ->exists();

        $result = [];

        if ($checkResult) {
            $result['success'] = false;
            $result['message'] = 'Bu hak zaten mevcut. Yeniden eklenemez.';
        } else {
            $option_detail = $this->getBenefitOptionsDetailById($option_id);

            // Eski kayıt varsa sil
            DB::table('member_benefits_cart')
                ->where('member_id', $member_id)
                ->where('benefit_id', $option_detail->benefit_id)
                ->where('member_family_id', 0)
                ->delete();

            // Net mi brüt mü?
            $finalAmount = $netBrutMode == 1 ? $priceNet : $priceBrut;

            $data = [
                'benefit_id'         => $option_detail->benefit_id,
                'amount'             => $finalAmount,
                'member_id'          => $member_id,
                'update_status'      => 1,
                'benefit_option_id'  => $option_id,
                'member_family_id'   => 0,
                'benefit_fin_code'   => $option_detail->benefit_fin_code ?? null,
                'benefit_fin_type'   => $option_detail->benefit_fin_type ?? null,
                'benefit_fin_nr'     => $option_detail->benefit_fin_nr ?? null,
            ];

            if (DB::table('member_benefits_cart')->insert($data)) {
                $result['success']     = true;
                $result['message']     = 'Hak eklendi.';
                $result['benefit_id']  = $data['benefit_id'];
                $result['usedAmount']  = number_format((float) $finalAmount, 2, ',', '.');
                $result['netPrice']    = number_format((float) $priceNet, 2, ',', '.');
                $result['brutPrice']   = number_format((float) $priceBrut, 2, ',', '.');
                $result['mode']        = $netBrutMode == 1 ? 'Net' : 'Brüt';
            } else {
                Log::error('Hak eklenirken hata oluştu. benefit option: ' . $option_id);
                $result['success']     = false;
                $result['message']     = 'Bir hata oluştu. Hak eklenemedi.';
                $result['benefit_id']  = $option_detail->benefit_id;
            }

            // Ek çoklu hak mantığı gerekiyorsa
            if ($rulesValue === "1" && $benefit_id == "123") {
                $result['multiple'] = true;

                if ($result['success']) {
                    $sameOptions = DB::table('benefit_options')
                        ->join('benefits', 'benefit_options.benefit_id', '=', 'benefits.id')
                        ->select('benefit_options.id', 'benefit_options.name', 'benefit_options.name_en', 'benefit_options.benefit_id', 'benefit_options.price')
                        ->where('benefit_options.name', 'like', '%' . $option_detail->name . '%')
                        ->whereNot('benefit_options.benefit_id', $option_detail->benefit_id)
                        ->where('benefits.benefit_group_id', 18)
                        ->get()
                        ->toArray();

                    $benefitIds = array_column($sameOptions, 'benefit_id');
                    $benefitOptionIds = array_column($sameOptions, 'id');

                    $result['benefit_ids'] = $benefitIds;
                    $result['benefit_options_ids'] = $benefitOptionIds;
                    $result['multiple'] = false;
                }
            }
        }

        return $result;
    }
    public function getBenefitOptionsDetailById($option_id)
    {
        return DB::table('benefit_options')->where('id', $option_id)->first();
    }

    public function getCheckBenefitOptionPref($option_id, $member_id, $price=null)
    {
        if ($option_id) {
            $select = DB::table('member_benefit_prefs')
                ->where('benefit_option_id', $option_id)
                ->where('member_id', $member_id)
                ->where('is_visible', 1)
                ->first();

            if ($select) {
                return $select->price;
            } else {
                return $price;
            }
        } else {
            return $price;
        }
    }

    public function getCurrentMemberBenefit($member_id,$benefit_id)
    {
        $basket = DB::table('member_benefits_cart')
            ->select('amount')
            ->where('member_id', $member_id)
            ->where('benefit_id', $benefit_id)
            ->get()
            ->first();

        return $basket;
    }

    public function getBasketForPrevious($member_id, $benefit_id = null, $option_id = null, $amount = null)
    {
        $basket = DB::table('member_benefits_cart')
            ->where('member_id', $member_id)
            ->get()
            ->toArray();

        $existingData = DB::table('previous_member_benefits_cart')
            ->where('member_id', $member_id)
            ->where('benefit_id', 123)
            ->get()
            ->toArray();

        if ($benefit_id == "123" && empty($existingData)) {

            $m_member_benefit = new MemberBenefit();

            $main_option_detail = $this->getBenefitOptionsDetailById($option_id);
            $insertData = [];
            $sameOptions = DB::table('benefit_options')
                ->select('id', 'name', 'name_en', 'benefit_id', 'price')
                ->where('name', 'like', '%' . $main_option_detail->name . '%')
                ->get()
                ->toArray();

            foreach ($sameOptions as $data) {
                $insertData[] = [
                    'benefit_id' => $data->benefit_id,
                    'amount' => $m_member_benefit->getCheckBenefitOptionPredefined($data->benefit_id, $member_id, $data->price),
                    'benefit_option_id' => $data->id,
                    'member_id' => $member_id,
                    'update_status' => 0,
                    'member_family_id' => 0,
                    'family_benefit_detail_id' => null,
                    'is_removed' => 0,
                    'family_option' => null,
                    'updated_at' => Carbon::now()
                ];
            }

            $benefitIds = array_column($insertData, 'benefit_id');

            DB::table('previous_member_benefits_cart')
                ->where('member_id', $member_id)
                ->whereIn('benefit_id', $benefitIds)
                ->delete();

            $result = DB::table('previous_member_benefits_cart')->insert($insertData);

            return $result;
        } if(empty($basket)){
            return true;
    } else {

            foreach ($basket as $item) {

                if ($item->benefit_id == $benefit_id) {
                    DB::table('previous_member_benefits_cart')->updateOrInsert(
                        [
                            'member_id' => $item->member_id,
                            'benefit_id' => $item->benefit_id,
                            'member_family_id' => $item->member_family_id
                        ],
                        [
                            'benefit_id' => $item->benefit_id,
                            'amount' => $item->amount,
                            'benefit_option_id' => $item->benefit_option_id,
                            'member_id' => $item->member_id,
                            'update_status' => $item->update_status,
                            'member_family_id' => $item->member_family_id,
                            'family_benefit_detail_id' => $item->family_benefit_detail_id,
                            'is_removed' => $item->is_removed,
                            'family_option' => $item->family_option,
                            'updated_at' => Carbon::now()
                        ]
                    );
                }
            }
        }

        return $basket;
    }

    public function addBenefitsByBenefitId($benefitId, $amount, $append = false, $priceNet = null, $priceBrut = null, $netBrutMode = 1, $finCode = null, $finType = null, $finNr = null)
    {
        $benefitDetail = Benefit::find($benefitId);
        $rulesValue = CustomSettings::getRules();

        if (!$benefitDetail) {
            Log::error('Belirtilen benefit_id ile bir fayda bulunamadı: ' . $benefitId);
            return ['success' => false, 'message' => 'Hak eklenirken bir hata oluştu.'];
        }

        $member = Member::where('id', Auth::guard('member')->id())->first();
        $memberId = $member->id ?? null;

        if ($rulesValue == "1") {
            $this->getBasketForPrevious($memberId, $benefitId, null, $amount);
        }

        // Net veya Brüt tutara göre kullanılacak gerçek fiyat
        $currentValue = $netBrutMode == 1 ? floatval($priceNet) : floatval($priceBrut);


        if ($benefitDetail->type == 5) {
            // type 5 ise doğrudan amount kullan
            $currentValue = floatval($amount);
        } else {
            // diğerlerinde netBrutMode’a göre fiyat
            $currentValue = $netBrutMode == 1
                ? floatval($priceNet)
                : floatval($priceBrut);
        }
        $finalAmount = $currentValue;

        if ($append) {
            $existingBenefit = DB::table('member_benefits_cart')
                ->where('member_id', $memberId)
                ->where('member_family_id', 0)
                ->where('benefit_id', $benefitId)
                ->first();

            if ($existingBenefit) {
                $existingAmount = floatval($existingBenefit->amount ?? 0);
                $finalAmount = $existingAmount + $currentValue;
            }
        }

        try {
            DB::table('member_benefits_cart')->updateOrInsert([
                'member_id' => $memberId,
                'member_family_id' => 0,
                'benefit_id' => $benefitId
            ], [
                'benefit_id'        => $benefitId,
                'member_id'         => $memberId,
                'member_family_id'  => 0,
                'update_status'     => 1,
                'amount'            => $finalAmount,
                'benefit_fin_code'  => $finCode,
                'benefit_fin_type'  => $finType,
                'benefit_fin_nr'    => $finNr,
            ]);

            return [
                'success'     => true,
                'message'     => 'Hak Eklendi.',
                'benefit_id'  => $benefitId,
                'netPrice'    => number_format((float) $priceNet, 2, ',', '.'),
                'brutPrice'   => number_format((float) $priceBrut, 2, ',', '.'),
                'usedAmount'  => number_format((float) $finalAmount, 2, ',', '.'),
                'mode'        => $netBrutMode == 1 ? 'Net' : 'Brüt'
            ];

        } catch (\Exception $e) {
            Log::error('Hak eklenirken bir hata oluştu: ' . $e->getMessage());
            return ['success' => false, 'message' => 'Hak eklenirken bir hata oluştu.'];
        }
    }
    public function getFamilyBenefits($memberId = false, $noFamilyOption = false)
    {
        if (!$memberId) {
            $memberId = auth()->user()->id;
        }

        $memberFamilyModel = new MemberFamily();
        $partner = $memberFamilyModel->getPartner($memberId);
        $relatives = $memberFamilyModel->getChildren($memberId);

        if (!empty($partner)) {
            $relatives[] = $partner;
        }

        if (empty($relatives)) {
            return [];
        }

        $relativeIds = collect($relatives)->pluck('id');

        $member = Member::find($memberId);
        $familyPercentage = $member->family_percentage ?: 0;

        // Real Balance hesabı için, noFamilyOption true olduğunda, her zaman tam fiyat döndürüyoruz.
        $familyPrice = $noFamilyOption ? 'family_benefit_details.price' : DB::raw("IF(member_benefits_cart.family_option = 1, family_benefit_details.price * {$familyPercentage} / 100, family_benefit_details.price) as real_price");

        $familyBenefits = $this->select('member_benefits_cart.*', 'member_family.name AS relative_name', 'family_benefits.benefit_option_id AS option_id',
            'benefits.benefit_group_id AS group_id', DB::raw($familyPrice), 'benefits.*', 'family_benefits.prerequisite_benefit_id')
            ->join('member_family', 'member_family.id', '=', 'member_family_id')
            ->leftJoin('family_benefit_details', 'family_benefit_details.id', '=', 'member_benefits_cart.family_benefit_detail_id')
            ->leftJoin('family_benefits', 'family_benefits.id', '=', 'family_benefit_details.family_benefit_id')
            ->leftJoin('benefit_options', 'benefit_options.id', '=', 'member_benefits_cart.benefit_option_id')
            ->leftJoin('benefits', 'member_benefits_cart.benefit_id', '=', 'benefits.id')
            ->leftJoin('benefit_groups', 'benefits.benefit_group_id', '=', 'benefit_groups.id')
            ->whereIn('member_family_id', $relativeIds)
            ->where('member_benefits_cart.member_id', $memberId)
            ->get()
            ->groupBy('benefit_id')
            ->map(function ($group) {
                return $group->keyBy('member_family_id');
            });

        return $familyBenefits;
    }

    public static function clear($member_id)
    {
        return self::where('member_id', $member_id)->delete();
    }

    public static function getByMemberId($memberId)
    {
        return self::where('member_id', $memberId)->get();
    }

    public static function getByBasketForMemberID($memberId)
    {
        return self::where('member_id', $memberId)
            ->select('benefit_id', 'amount')
            ->get();
    }

}
