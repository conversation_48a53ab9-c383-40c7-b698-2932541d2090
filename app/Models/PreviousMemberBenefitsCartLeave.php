<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
class PreviousMemberBenefitsCartLeave extends Model
{
    use HasFactory;
    protected $table = 'previous_member_benefits_cart_leave';
    protected $guarded = ["id"];
    public $timestamps = false;

    public function getLatestRecord($member_id)
    {
         $latest_record=$this->where('member_id', $member_id)
            ->orderBy('updated_at', 'desc')
            ->first();

         return $latest_record;
    }

    public static function clear($member_id)
    {
        return self::where('member_id', $member_id)->delete();
    }
}
