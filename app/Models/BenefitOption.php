<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;

class BenefitOption extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = ["id"];

    public function benefit(): BelongsTo
    {
        return $this->belongsTo(Benefit::class, 'benefit_id', 'id');
    }

    // public function getBenefitOptions($memberId = null)
    // {
    //     $query = $this->newQuery();

    //     $query->select([
    //         'update_status',
    //         'status',
    //         'price_original',
    //         DB::raw('IFNULL(p.price, benefit_options.price) AS price'),
    //         'order_no',
    //         'name',
    //         'is_addon',
    //         'image',
    //         'id',
    //         'has_details',
    //         'details',
    //         'description',
    //         'comment',
    //         'benefit_id',
    //         'alert_message',
    //         'alert_color'
    //     ]);

    //     if ($memberId) {
    //         $query->leftJoin('member_benefit_prefs as p', function ($join) use ($memberId) {
    //             $join->on('p.benefit_option_id', '=', 'benefit_options.id')
    //                  ->where('p.member_id', '=', $memberId);
    //         })
    //         ->where(function ($query) {
    //             $query->whereNull('p.id')->orWhere('p.is_visible', 1);
    //         });
    //     }

    //     $query->where('benefit_options.status', 1);

    //     return $query->get()->groupBy('benefit_id')->toArray();
    // }

    public static function getBenefitIdByOptionId($optionId)
    {
        $benefitOption = self::where('id', $optionId)->first();

        return $benefitOption ? $benefitOption->benefit_id : null;
    }

    public function getOptionDetails($optionId)
    {
        $benefitOption = self::where('id', $optionId)->first();

        return $benefitOption;
    }
}
