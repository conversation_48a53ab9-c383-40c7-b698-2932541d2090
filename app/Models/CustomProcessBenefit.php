<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
class CustomProcessBenefit extends Model
{
    use HasFactory;

    protected $table = 'custom_process_benefits';
    protected $guarded = ["id"];

    public function getCustomProcessBenefits($member_id)
    {
        $customProcessBenefits = DB::table('custom_process_benefits')
            ->where('member_id', $member_id)
            ->get()
            ->toArray();

        return $customProcessBenefits;
    }

    public function getCustomProcessBenefitsForBenefitID($member_id,$benefit_id)
    {
        $customProcessBenefits = DB::table('custom_process_benefits')
            ->where('member_id', $member_id)
            ->where('benefit_id', $benefit_id)
            ->first();

        return $customProcessBenefits;
    }

    public function getCustomProcessBenefitWithMaxId($member_id)
    {
        $customProcessBenefit = DB::table('custom_process_benefits')
            ->where('member_id', $member_id)
            ->orderBy('id', 'desc')
            ->first();

        return $customProcessBenefit;
    }

    public function getCustomProcessBenefitLastRecord($member_id)
    {
        $customProcessBenefit = DB::table('custom_process_benefits')
            ->select('id')
            ->where('member_id', $member_id)
            ->orderBy('id', 'desc')
            ->first();

        return $customProcessBenefit;
    }

    public function sumCustomBalance_v2($member_id)
    {
        $positiveRecords = DB::table('custom_process_benefits as t1')
            ->select('t1.*')
            ->where('t1.member_id', $member_id)
            ->where('t1.amount', '>', 0)
            ->whereRaw('t1.created_at = (SELECT MAX(created_at) FROM custom_process_benefits WHERE member_id = ? AND benefit_id = t1.benefit_id AND amount > 0)', [$member_id]);

        $negativeRecords = DB::table('custom_process_benefits as t2')
            ->select('t2.*')
            ->where('t2.member_id', $member_id)
            ->where('t2.amount', '<', 0)
            ->whereRaw('t2.created_at = (SELECT MAX(created_at) FROM custom_process_benefits WHERE member_id = ? AND benefit_id = t2.benefit_id AND amount < 0)', [$member_id]);

        $finalRecords = $positiveRecords->unionAll($negativeRecords)->get();

        $uniqueRecords = $finalRecords->unique(function ($item) {
            return $item->benefit_id . '-' . $item->created_at . '-' . $item->amount;
        });

        return $uniqueRecords->values();
    }

    public function sumCustomBalance($member_id)
    {
        $latestRecords = DB::table('custom_process_benefits as t1')
            ->select('t1.*')
            ->join(
                DB::raw('(SELECT benefit_id, MAX(id) as max_id FROM custom_process_benefits WHERE member_id = ' . (int) $member_id . ' GROUP BY benefit_id) as t2'),
                function ($join) {
                    $join->on('t1.id', '=', 't2.max_id');
                }
            )
            ->where('t1.member_id', $member_id)
            ->get();

        return $latestRecords;
    }

    public static function clear($member_id)
    {
        return self::where('member_id', $member_id)->delete();
    }

}
