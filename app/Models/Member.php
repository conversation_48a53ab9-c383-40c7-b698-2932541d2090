<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Member extends  Authenticatable
{
    use HasFactory;
    protected $table = 'members';
    public $timestamps = false;
    protected $guarded = ["id"];

    protected $attributes = [
        'item_id' => 0,
    ];
    public function firm(): BelongsTo
    {
        return $this->belongsTo(Firm::class, 'firm_id', 'id');
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'department_id', 'id');
    }

    public function title(): BelongsTo
    {
        return $this->belongsTo(Title::class, 'title_id', 'id');
    }

    public function position(): BelongsTo
    {
        return $this->belongsTo(Position::class, 'position_id', 'id');
    }

    public function workplace(): BelongsTo
    {
        return $this->belongsTo(Workplace::class, 'workplace_id', 'id');
    }

    public function memberBenefitPrefs()
    {
        return $this->hasMany(MemberBenefitPref::class);
    }

    public function memberBenefit()
    {
        return $this->hasMany(MemberBenefit::class);
    }

    public function benefits()
{
    return $this->belongsToMany(Benefit::class, 'member_benefits', 'member_id', 'benefit_id')
    ->withPivot('amount','benefit_option_id');
}

public function benefitOptions()
{
    return $this->belongsToMany(BenefitOption::class, 'member_benefits', 'member_id', 'benefit_option_id')
    ->withPivot('amount', 'benefit_id');
}

    public function getPositiveBalance($member_id)
    {
        return $this->where('id', $member_id)->value('positive_balance');
    }

    public function getPercentage($member_id)
    {
        return $this->where('id', $member_id)->value('percentage');
    }


}

