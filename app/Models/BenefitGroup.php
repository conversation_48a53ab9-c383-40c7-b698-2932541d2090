<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BenefitGroup extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = ["id"];

    public function benefits(): HasMany
    {
        return $this->hasMany(Benefit::class, 'benefit_group_id', 'id');
    }

    public function getGroupNames()
    {
        return $this->pluck('name', 'id')->toArray();
    }

    public function getGroupsAllowNegative()
    {
        return $this->where('can_be_negative', 1)->get();
    }




}
