<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
class CustomSettings extends Model
{
    use HasFactory;
    protected $table = 'custom_settings';
    public $timestamps = false;
    protected $guarded = ["id"];

    public static function getRules()
    {
        $cacheKey = 'custom_settings_rules';

        return Cache::remember($cacheKey, 3600, function () {
            $setting = self::where('settings_key', 'rules')->first();
            return $setting ? $setting->settings_value : null;
        });
    }

    public static function getComponents()
    {

        $cacheKey = 'custom_settings_project_components';

        return Cache::remember($cacheKey, 3600, function () {
            $settings = self::where('settings_key', 'project_components')->first();
            return $settings ? $settings->settings_value : null;
        });
    }

}
