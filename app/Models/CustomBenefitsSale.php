<?php

namespace App\Models;

use App\Models\CustomBenefit;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Member;
use App\Models\Benefit;
use App\Models\MemberBenefitCart;
use App\Models\MemberBenefit;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
class CustomBenefitsSale extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = ["id"];
    protected $table = 'custom_benefits_sale';

    public function benefitSale($benefit_id, $member_id, $sale_type, $sales_profit)
    {
        $memberModel = new Member();

        $existingRecord = DB::table('custom_benefits_sale')
            ->where('member_id', $member_id)
            ->where('benefit_id', $benefit_id)
            ->first();

        $columns = [
            'related_sale' => $sale_type == 'related' ? $sales_profit : null,
            'non_related_sale' => $sale_type == 'non-related' ? $sales_profit : null
        ];

        if ($existingRecord) {
            if ($existingRecord->related_sale && $columns['related_sale']) {
                $columns['related_sale'] += $existingRecord->related_sale;
            }

            if ($existingRecord->non_related_sale && $columns['non_related_sale']) {
                $columns['non_related_sale'] = $existingRecord->non_related_sale;
            }

            DB::table('custom_benefits_sale')
                ->where('id', $existingRecord->id)
                ->update($columns);
        } else {
            $initial_balance = $memberModel->getPositiveBalance($member_id);
            DB::table('custom_benefits_sale')->insert([
                'member_id' => $member_id,
                'benefit_id' => $benefit_id,
                'initial_balance' => $initial_balance,
                'related_sale' => $columns['related_sale'],
                'non_related_sale' => $columns['non_related_sale'],
            ]);
        }
    }


    public function is_this_sale($benefit_id,$member_id)
    {


        $status=false;
        $m_member_benefit_cart = new MemberBenefitCart();
        $m_member_benefit = new MemberBenefit();

        $predefined_benefit = $m_member_benefit->getMemberBenefit($member_id, $benefit_id);
        $current_benefit = $m_member_benefit_cart->getCurrentMemberBenefit($member_id, $benefit_id);

        //previous member benefit cartı da dahil et sisteme

        //is_this_sale fonksiyonunda mevcut sepetle yeni sepeti kıyasla
        //eğer mevcut sepet boşsa da memberbenefit ile kıyasla amountlarını satış mı değil mi ona göre karar ver

        //zaten eski sepeti de tuttuğun için fonksyion daha da refactor edilebilir hale gelir.

    }

    public function checkAvailableAction($benefit_id,$member_id,$amount)
    {
        $status=true;
        $message = '';
        $message_en='';

        //$test=$this->is_this_sale($benefit_id,$member_id);



        //öncelikle satış mı alış mı karar ver
        //satışsa status true dondur

        //alımsa related alım mı non related alım mı buna bak ve
        //doğrudan custom benefits sale'yi update ettir


        $memberModel = new Member();
        $benefitModel = new Benefit();
        $benefitCartModel = new MemberBenefitCart();
        $memberBenefitModel = new MemberBenefit();
        $custom_benefits = CustomBenefit::getAllCustomBenefits()->toArray();
        $m_previous_basket = new PreviousMemberBenefitCart();
        $m_member_benefit_pref = new MemberBenefitPref();

        $member = Member::find($member_id);
       $member_id=$member->id;
        $percentage=$member->percentage;

        $custom_benefits_name=[];
        foreach ($custom_benefits as $key => $custom_benefit) {
            $existing_names = array_column($custom_benefits_name, 'name');
            $existing_names_en = array_column($custom_benefits_name, 'name_en');

            if (!in_array($custom_benefit['benefit_name'], $existing_names) && !in_array($custom_benefit['benefit_name'], $existing_names_en)) {
                $custom_benefits_name[$key] = [
                    'name' => $custom_benefit['benefit_name'],
                    'name_en' => $custom_benefit['benefit_name'],
                ];
            }
        }

        $member_percentage=$memberModel->getPercentage($member_id);


        $benefitCarts = $benefitCartModel->where('member_id', $member_id)->get();

        $cacheKey = "member_benefit_predefined_{$member_id}";

        $memberBenefitPredefined = Cache::remember($cacheKey, 3600, function () use ($memberBenefitModel, $member_id) {
            return $memberBenefitModel->where('member_id', $member_id)->get();
        });

        $amountArray = [];

        $predefined_benefit = $memberBenefitModel->getMemberBenefit($member_id, $benefit_id);
        $previous_benefit = $m_previous_basket->getPreviousMemberBenefit($member_id, $benefit_id);

        $existingCartEntry = $benefitCartModel->where('member_id', $member_id)
            ->where('benefit_id', $benefit_id)
            ->first();

        if (!empty($existingCartEntry)) {
            if ($existingCartEntry->benefit_id == 149) {
                if ($existingCartEntry->amount < $amount) {
                    $amountDifference = $amount - $existingCartEntry->amount;
                    $amountArray[$existingCartEntry->benefit_id] = number_format((float)$amountDifference, 1, '.', '');
                }
            }else{
                if($amount < $existingCartEntry->amount){
                    $status =true;
                }else{
                    if($amount==$predefined_benefit->amount){

                        $diff_custom=$amount-$existingCartEntry->amount;
                        $amountArray[$benefit_id] = number_format((float)$diff_custom, 1, '.', '');
                    }else{
                        $amountArray[$benefit_id] = number_format((float)$amount, 1, '.', '');
                    }
                }
            }
        } else {
            if(isset($predefined_benefit) && !empty($predefined_benefit)){
                if($amount <= $predefined_benefit->amount){
                    $status =true;
                }else{
                    $amountArray[$benefit_id] = number_format((float)$amount, 1, '.', '');
                }
            }
        }

        $customMainIds = DB::table('custom_benefits')
            ->select('benefit_id')
            ->get()
            ->toArray();

        $keysToRemove = [];
        foreach($customMainIds as $id)
        {
            $keysToRemove[]=$id->benefit_id;
        }

        if ($benefit_id != 149) {
            $keysToRemove[] = 149;
        }

        foreach ($keysToRemove as $key) {
            if (isset($amountArray[$key])) {
                unset($amountArray[$key]);
            }
        }



        if(array_key_exists(149,$amountArray)){
            $default_price=$m_member_benefit_pref->getCheckBenefitOptionPrefByBenefitID(0,$benefit_id,$member_id,$amount);
            $adet=$amountArray[149];
            $amountArray[149]=($adet*$default_price)*$percentage/100;
        }


        $benefitRelatedIds = CustomBenefit::withRelatedBenefit()->pluck('benefit_related_id');

        $relatedId = $benefitRelatedIds->unique()->values()->all();



        $customMainIds = DB::table('custom_benefits')
            ->select('benefit_id')
            ->where('benefit_id',$benefit_id)
            ->get()
            ->toArray();


        $matching = [];
        $nonMatching = [];

        foreach ($amountArray as $key => $amount) {
            if (in_array($key, $relatedId)) {
                $matching[] = $amount;
            } else {
                $nonMatching[] = $amount;
            }
        }

        $nonMatchingSum=array_sum($nonMatching);
        $matcingSum=array_sum($matching);

        $cartEntries = $benefitCartModel->where('member_id', $member_id)
            ->whereIn('benefit_id', $relatedId)
            ->get();
        $updatedEntries = [];
        foreach ($cartEntries as $entry) {
            $benefitId = $entry->benefit_id;

            if (isset($matching[$benefitId])) {
                $entry->amount = $matching[$benefitId];
            }

            $updatedEntries[] = [
                'benefit_id' => $benefitId,
                'amount' => $entry->amount
            ];
        }
        foreach ($matching as $benefitId => $amount) {
            $existsInCart = $cartEntries->firstWhere('benefit_id', $benefitId);

            if (!$existsInCart) {
                $updatedEntries[] = [
                    'benefit_id' => $benefitId,
                    'amount' => $amount
                ];
            }
        }

        $totalRelatedSpending = array_reduce($updatedEntries, function($carry, $entry) {
            return $carry + $entry['amount'];
        }, 0);



        $non_related_spending = DB::table('custom_benefits_spending')
            ->where('member_id', $member_id)
            ->get();

        $non_related_sales = DB::table('custom_benefits_sale')
            ->where('member_id', $member_id)
            ->whereNull('related_sale')
            ->get();

        $non_related_sales_gain=0;
        foreach ($non_related_sales as $sales){
            $non_related_sales_gain+=$sales->non_related_sale;
        }


        $non_related_spending_total=0;
        foreach ($non_related_spending as $spending){
            $non_related_spending_total+=$spending->non_related_spending;
        }
        $initial_balance = $memberModel->getPositiveBalance($member_id);

        $non_related_sales_gain+=$initial_balance;

        $related_sales = DB::table('custom_benefits_sale')
            ->where('member_id', $member_id)
            ->whereNull('non_related_sale')
            ->get();


        $related_sales_gain=0;
        foreach ($related_sales as $sales){
            $related_sales_gain+=$sales->related_sale;
        }

        if($related_sales_gain > 0){
            $related_spendable_balance=$related_sales_gain-$totalRelatedSpending;
        }else{
            $related_spendable_balance=0;
        }


        $balance=$benefitModel->getMemberBalance($member_id);
        $non_related_spendable_balance=$balance-$related_sales_gain;
        $non_related_spending_total_custom=$non_related_spending_total+$nonMatchingSum;


        if (!in_array($benefit_id,$relatedId) && empty($customMainIds)) {

            if($non_related_spendable_balance >= $nonMatchingSum && isset($nonMatching) && !empty($nonMatching)){

                $data=[
                    'member_id' => $member_id,
                    'benefit_id' => $benefit_id,
                    'non_related_spending' => $nonMatching[0],
                ];

                $result= DB::table('custom_benefits_spending')->updateOrInsert([
                    'member_id' => $data['member_id'],
                    'benefit_id' => $data['benefit_id'],
                ], $data);

                if($result){
                    $status=true;
                }

            }else if($non_related_spendable_balance < $nonMatchingSum  && isset($related_sales) && !empty($related_sales) && $related_spendable_balance!=0){
              $status=false;

              if($related_spendable_balance > $balance){
                  $related_spendable_balance=$balance;
              }
                $benefit_names = array_column($custom_benefits_name, 'name');
                $benefit_names_en = array_column($custom_benefits_name, 'name_en');

                $related_benefits = implode(', ', $benefit_names);
                $related_benefits_en = implode(', ', $benefit_names_en);

              $message = sprintf(
                    "Toplam Yan Hak bütçeniz %s TL’dir.\nBu hakta harcayabileceğiniz maksimum bütçeniz %s TL’dir.\nGeriye kalan %s TL bütçenizi sadece %s  satın almak için kullanabilirsiniz.\nLütfen seçiminizi buna göre güncelleyiniz.",
                    $balance,
                    $non_related_spendable_balance,
                    $related_sales_gain,
                    $related_benefits
              );

                $message_en = sprintf(
                    "Your total side benefit budget is %s TL.\nThe maximum budget you can spend on these benefits is %s TL.\nThe remaining %s TL can only be used to purchase %s.\nPlease update your selection accordingly.",                    $balance,
                    $non_related_spendable_balance,
                    $related_sales_gain,
                    $related_benefits_en
                );

            }

        }

        //quick fix for the issue
        if($balance < 0 && $non_related_spendable_balance < $balance){
            $status=true;
        }

        if($non_related_spendable_balance < 0){
            $status=true;
        }
        //quick fix for the issue


        return [
            'status' => $status,
            'message' => $message,
            'message_en' => $message_en
        ];

    }



    public static function clear($member_id)
    {
        return self::where('member_id', $member_id)->delete();
    }
}
