<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
class CustomBenefit extends Model
{
    use HasFactory;

    protected $fillable = ['benefit_id', 'benefit_group_id', 'benefit_related_id', 'benefit_price','benefit_name','benefit_name_en'];
    public $timestamps = false;

    public function benefit()
    {
        return $this->belongsTo(Benefit::class, 'benefit_id');
    }
    public function scopeWithRelatedBenefit($query)
    {
        return $query->whereNotNull('benefit_related_id');
    }

    public static function getByBenefitId($benefitId)
    {
        return self::where('benefit_id', $benefitId)->get();
    }

    public static function getAllCustomBenefits()
    {
        return self::all();
    }
}
