<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Season;
use App\Models\Benefit;
use App\Models\OrderItem;

class Order extends Model
{
    use HasFactory;
    public $timestamps = false;
    protected $guarded = ["id"];

    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'member_id', 'id')->select('id','name','is_approved','identity_number');
    }

    public static function getOrdersByMember(int $member_id)
    {
        return self::where('member_id', $member_id)
            ->with('member')
            ->get();
    }

    public function season(): BelongsTo
    {
        return $this->belongsTo(Season::class, 'season_id', 'id')->select('id', 'name');
    }

    public function createOrder($member_id)
    {
        // Aktif sezonu al
        $season = Season::getActiveSeason();


        // Üye faydalarını ve aile faydalarını al
        $benefit = new Benefit();
        $finalBenefits = $benefit->getFinalBenefits($member_id);
        $finalFamilyBenefits = $benefit->getFinalFamilyBenefits($member_id);

        // Toplam tutarı hesapla
        $grandTotal = $benefit->getPredefinedGrandTotal($member_id);

        // Üye bakiyelerini al
        $member_balance = $benefit->getMemberBalance($member_id);
        $real_balance = $benefit->getMemberBalance($member_id ,false, true);
        $is_changed = $benefit->getIfIsChanged($member_id);

        // Yeni sipariş oluştur
        $order = new Order();
        $order->member_id = $member_id;
        $order->season_id = $season->id;
        $order->grand_total = $grandTotal;
        $order->date = now()->subDay();
        $order->ip = Request::ip();
        $order->member_balance = $member_balance['balance'];
        $order->real_balance   = $member_balance['brut_balance'];
        $order->is_changed = $is_changed;
        $order->save();

        $optionIds = $finalBenefits->pluck('option_id')->filter()->unique()->all();
        $optionCodes = BenefitOption::whereIn('id', $optionIds)
            ->pluck('benefit_fin_code', 'id')
            ->all(); // [option_id => 'CODE']
        $bulutfin = app(\App\Services\BordroService::class);

        $resp = $bulutfin->request('yillikozet');
        $data   = data_get($resp, 'Data', []);
        $yanHak = data_get($data, 'YanHakSumList', []);
        $hsoh   = data_get($data, 'HSOHSumList', []);
        $allBF  = array_merge($yanHak, $hsoh);

        $groupedByCode = collect($allBF)
            ->groupBy('CODE')
            ->map(fn($items) => [
                'priceBrut' => $items->sum('YanHakBurut'),
                'priceNet'  => $items->sum('YanHakNet'),
            ])
            ->all();

        $izinLedgerByBenefitId = DB::table('member_benefit_balance_ledger')
            ->where('member_id', $member_id)
            ->where('fin_type', 'izin_to_balance')
            ->get()
            ->keyBy('benefit_id');



        foreach ($finalBenefits as $benefit) {

            $code = $optionCodes[$benefit->option_id] ?? $benefit->benefit_fin_code;

            if (isset($groupedByCode[$code])) {
                $benefit->priceBrut = $groupedByCode[$code]['priceBrut'];
                $benefit->priceNet  = $groupedByCode[$code]['priceNet'];
            } else {
                $benefit->priceBrut = 0;
                $benefit->priceNet  = 0;
            }

            if ($benefit->type == 5 && isset($izinLedgerByBenefitId[$benefit->id])) {
                $ledger = $izinLedgerByBenefitId[$benefit->id];
                $benefit->priceBrut = floatval($ledger->price_brut);
                $benefit->priceNet  = floatval($ledger->price_net);
            }


            $orderItem = new OrderItem();
            $orderItem->order_id = $order->id;
            $orderItem->benefit_id = $benefit['id'];
            $orderItem->benefit_option_id = $benefit['option_id'];
            $orderItem->member_family_id = 0;
            $orderItem->price = $benefit['priceNet'] ?? 0;
            $orderItem->brut_price = $benefit['priceBrut'] ?? 0;
            $orderItem->update_status = 0;
            $orderItem->save();
        }

        foreach ($finalFamilyBenefits as $benefit_id => $memberFamily) {
            foreach ($memberFamily as $member_family_id => $benefit) {
                if ($benefit['real_price'] == '0.00' || empty($benefit['real_price'])) {
                    continue;
                }

                $orderItem = new OrderItem();
                $orderItem->order_id = $order->id;
                $orderItem->benefit_id = $benefit_id;
                $orderItem->benefit_option_id = $benefit['option_id'];
                $orderItem->member_family_id = $member_family_id;
                $orderItem->price = $benefit['real_price'];
                $orderItem->save();
            }
        }

        return $order->id;
    }
}
