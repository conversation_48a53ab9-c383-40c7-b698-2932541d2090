<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;

class QuestionAnswer extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = ["id"];
    protected $fillable = ["question_id", "member_id", "answer","date","answer_en"];

    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class, 'question_id', 'id')->select('id', 'text','text_en');
    }

    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'member_id', 'id')->select('id', 'name', 'identity_number');
    }


    public static function getQuestionAnswer($memberId)
    {
        return self::where('member_id', $memberId)->get();
    }
}
