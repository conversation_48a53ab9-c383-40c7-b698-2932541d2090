<?php

namespace App\Models;

use App\Models\CustomBalance;
use App\Services\BordroService;
use App\Traits\FamilyBenefitTrait;


use Carbon\Carbon;
use Cassandra\Custom;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;
use App\Models\BenefitGroup;
use App\Models\MemberBenefitPref;
use App\Models\BenefitOption;
use App\Models\MemberBenefit;
use App\Models\MemberBenefitCart;
use App\Models\CustomSettings;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class Benefit extends Model
{
    use HasFactory;
    use FamilyBenefitTrait;

    public $timestamps = false;
    protected $guarded = ["id"];

    public function benefitGroup(): BelongsTo
    {
        return $this->belongsTo(BenefitGroup::class, 'benefit_group_id', 'id');
    }


    public function getAll($memberId = '')
    {
        $groups = $this->getGroups();
        $benefits = $this->getBenefits($memberId);
        $benefitOptions = $this->getBenefitOptions($memberId);


        // foreach ($groups as &$group) {
        //     $group = (array) $group;

        //     $groupId = $group['benefit_group_id'];

        //     if (!empty($benefits[$groupId])) {
        //         foreach ($benefits[$groupId] as &$benefit) {
        //             $benefit = (array) $benefit;
        //             $benefitId = $benefit['id'];
        //             $benefit['options'] = $benefitOptions[$benefitId] ?? [];
        //         }

        //         $group['benefits'] = $benefits[$groupId];
        //     }
        // }

        // $groups'tan gelen veriye göre kodun bu sekilde revize edilmesi gerekli

        foreach ($groups as $groupId => &$group) {
            if (!empty($benefits[$groupId])) {
                foreach ($benefits[$groupId] as &$benefit) {
                    $benefit = (array)$benefit;
                    $benefitId = $benefit['id'];
                    $benefit['options'] = $benefitOptions[$benefitId] ?? [];
                }

                $group['benefits'] = $benefits[$groupId];
            }
        }


        return $groups;
    }

    public function getAllBenefits()
    {
        $groups = $this->getGroups();
        $benefits = $this->getBenefits();
        $benefitOptions = $this->getBenefitOptions();

        $allBenefits = [];


        // foreach ($groups as &$group) {
        //     if (!empty($benefits[$group['id']])) {
        //         foreach ($benefits[$group['id']] as &$benefit) {
        //             $benefit['options'] = $benefitOptions[$benefit['id']] ?? [];
        //             $allBenefits[$benefit['id']] = $benefit;
        //         }
        //     }
        // }

        foreach ($groups as &$group) {
            if (!empty($benefits[$group['id']])) {
                foreach ($benefits[$group['id']] as &$benefit) {
                    $benefit->options = $benefitOptions[$benefit->id] ?? [];
                    $allBenefits[$benefit->id] = $benefit;
                }
            }
        }


        return $allBenefits;
    }

    public function customBenefits(): HasMany
    {
        return $this->hasMany(CustomBenefit::class, 'benefit_id');
    }


    public function getGroups()
    {
        return BenefitGroup::where('status', 1)
            ->orderBy('order_no')
            ->get()
            ->keyBy('id')
            ->toArray();
    }



    public function getBenefits($memberId = '')
    {
        $cacheKey = "benefits_{$memberId}";
        return Cache::member($memberId)->remember($cacheKey, 3600, function () use ($memberId) {
            $query = DB::table('benefits as b')
                ->select([
                    'b.id',
                    'b.benefit_fin_code',
                    'b.benefit_fin_typ',
                    'b.benefit_fin_nr',
                    'b.benefit_bulutfin_mode',
                    'b.order_no',
                    'b.name',
                    'b.name_en',
                    'b.benefit_group_id',
                    'b.description',
                    'b.description_en',
                    'b.desc_image',
                    'b.desc_image_en',
                    'b.alert_message',
                    'b.alert_message_en',
                    'b.image',
                    'b.image_en',
                    'b.comment',
                    'b.comment_en',
                    'b.alert_color',
                    'b.alert_color_en',
                    'b.alert_bg_color',
                    'b.alert_bg_color_en',
                    'b.price as benefit_price',
                    'b.status',
                    'b.has_choice',
                    'b.has_flexible_price',
                    'b.flexible_price',
                    'b.type',
                    'b.flexible_amount_title',
                    'b.flexible_amount_title_en',
                    'b.title',
                    'b.title_en',
                    'b.hide_if_not_predefined',
                    'b.show_on_positive_balance',
                    'b.price_original',
                    'b.details',
                    'b.details_en',
                    'b.has_details',
                    'b.show_option_info',
                    'b.convert_price',
                    'b.equal_price',
                    'b.really_equal_price',
                    DB::raw('IFNULL(p.price, b.price) as final_price')
                ])
                ->leftJoin('member_benefit_prefs as p', function ($join) use ($memberId) {
                    $join->on('b.id', '=', 'p.benefit_id')
                        ->where('p.member_id', '=', $memberId);
                })
                ->where('b.status', 2)
                ->orderBy('b.order_no')
                ->get()
                ->groupBy('benefit_group_id');
                return $query instanceof \Illuminate\Support\Collection ? $query->toArray() : $query;

        });
    }


    public function getBenefitOptions($memberId = '')
    {
        $cacheKey = "benefit_options_{$memberId}";

        return Cache::member($memberId)->remember($cacheKey, 3600, function () use ($memberId) {
            $query = BenefitOption::select([
                'benefit_options.update_status',
                'benefit_options.benefit_fin_code',
                'benefit_options.benefit_fin_typ',
                'benefit_options.benefit_fin_nr',
                'benefit_options.status',
                'benefit_options.price_original',
                'benefit_options.price',
                'benefit_options.order_no',
                'benefit_options.name',
                'benefit_options.name_en',
                'benefit_options.is_addon',
                'benefit_options.image',
                'benefit_options.id',
                'benefit_options.has_details',
                'benefit_options.details',
                'benefit_options.details_en',
                'benefit_options.description',
                'benefit_options.description_en',
                'benefit_options.comment',
                'benefit_options.comment_en',
                'benefit_options.benefit_id',
                'benefit_options.alert_message',
                'benefit_options.alert_message_en',
                'benefit_options.alert_color',
                DB::raw('IFNULL(member_benefit_prefs.price, benefit_options.price) AS price'),
            ])
                ->leftJoin('member_benefit_prefs', function ($join) use ($memberId) {
                    $join->on('member_benefit_prefs.benefit_option_id', '=', 'benefit_options.id')
                        ->where('member_benefit_prefs.member_id', '=', $memberId);
                })
                ->where('benefit_options.status', '=', 1)
                ->orderBy('benefit_options.order_no');

            if ($memberId != '') {
                $query->where(function ($query) {
                    $query->whereNull('member_benefit_prefs.id')
                        ->orWhere('member_benefit_prefs.is_visible', '=', 1);
                });
            }

            $benefitOptions = $query->get()->groupBy('benefit_id')->toArray();

            return $benefitOptions;
        });
    }


    public function getFinalBenefits($member_id)
    {
        $m_member_benefits_cart = new MemberBenefitCart();
        $m_member_benefit = new MemberBenefit();

        // Kullanıcının seçtiği ürünleri getiriyoruz. Benefit_id indexinde geliyor
        $selectedBenefitsData = $m_member_benefits_cart::getBenefits($member_id);



        // Ön tanımlı olanları getiriyoruz, benefit_id indexine göre getiriyoruz.
        $predefinedBenefits = $m_member_benefit->getBenefits($member_id)->keyBy('benefit_id');



        // Eğer aynı benefit hem seçilmişlerde hem de diğerlerinde varsa, ignore edecek.
        $finalBenefits = $predefinedBenefits;




        foreach ($selectedBenefitsData as $benefitId => $selectedBenefit) {
            if ($selectedBenefit instanceof Collection) {
                foreach ($selectedBenefit as $item) {
                    $finalBenefits[$benefitId] = $this->convertToMemberBenefit($item);
                }
            } else {
                $finalBenefits[$benefitId] = $selectedBenefit;
            }
        }

        $normalizedData = $this->normalizeData($finalBenefits);

        return $normalizedData;
    }


    public function getFinalBenefitsPDF($member_id)
    {
        $m_member_benefits_cart = new MemberBenefitCart();
        $m_member_benefit = new MemberBenefit();

        $selectedBenefitsData = $m_member_benefits_cart::getBenefits($member_id);


        $predefinedBenefits = $m_member_benefit->getBenefitsCustomPDF($member_id)->keyBy('benefit_id');



        $finalBenefits = $predefinedBenefits;


        foreach ($selectedBenefitsData as $benefitId => $selectedBenefit) {
            if ($selectedBenefit instanceof Collection) {
                foreach ($selectedBenefit as $item) {
                    $finalBenefits[$benefitId] = $this->convertToMemberBenefit($item);
                }
            } else {
                $finalBenefits[$benefitId] = $selectedBenefit;
            }
        }

        $normalizedData = $this->normalizeData($finalBenefits);

        return $normalizedData;
    }

    public function convertToMemberBenefit($data)
    {
        $rules = CustomSettings::getRules();

        $memberBenefit = new MemberBenefit;

        $memberBenefit->id = $data->id ?? null;
        $memberBenefit->order_no = $data->order_no ?? null;
        $memberBenefit->name = $data->name ?? null;
        $memberBenefit->name_en = $data->name_en ?? null;
        $memberBenefit->benefit_group_id = $data->benefit_group_id ?? null;
        $memberBenefit->description = $data->description ?? null;
        $memberBenefit->alert_message = $data->alert_message ?? null;
        $memberBenefit->image = $data->image ?? null;
        $memberBenefit->comment = $data->comment ?? null;
        $memberBenefit->alert_color = $data->alert_color ?? null;
        $memberBenefit->alert_color_en = $data->alert_color_en ?? null;
        $memberBenefit->price = $data->price ?? null;
        $memberBenefit->status = $data->status ?? null;
        $memberBenefit->has_choice = $data->has_choice ?? null;
        $memberBenefit->has_flexible_price = $data->has_flexible_price ?? null;
        $memberBenefit->flexible_price = $data->flexible_price ?? null;
        $memberBenefit->type = $data->type ?? null;
        $memberBenefit->flexible_amount_title = $data->flexible_amount_title ?? null;
        $memberBenefit->title = $data->title ?? null;
        $memberBenefit->title_en = $data->title_en ?? null;
        $memberBenefit->hide_if_not_predefined = $data->hide_if_not_predefined ?? null;
        $memberBenefit->show_on_positive_balance = $data->show_on_positive_balance ?? null;
        $memberBenefit->price_original = $data->price_original ?? null;
        $memberBenefit->details = $data->details ?? null;
        $memberBenefit->has_details = $data->has_details ?? null;
        $memberBenefit->show_option_info = $data->show_option_info ?? null;
        $memberBenefit->convert_price = $data->convert_price ?? null;
        $memberBenefit->equal_price = $data->equal_price ?? null;
        $memberBenefit->really_equal_price = $data->really_equal_price ?? null;
        $memberBenefit->min_amount = $data->min_amount ?? null;
        $memberBenefit->max_amount = $data->max_amount ?? null;
        $memberBenefit->benefit_id = $data->benefit_id ?? null;
        $memberBenefit->group_id = $data->group_id ?? null;
        $memberBenefit->group_name = $data->group_name ?? null;
        $memberBenefit->group_name_en = $data->group_name_en ?? null;
        $memberBenefit->option_id = $data->option_id ?? null;
        $memberBenefit->option_name = $data->option_name ?? null;
        $memberBenefit->option_name_en = $data->option_name_en ?? null;
        $memberBenefit->option_price = $data->option_price ?? null;
        $memberBenefit->amount = $data->amount ?? null;

        $memberBenefit->benefit_fin_typ            = $data->benefit_fin_typ ?? null;
        $memberBenefit->benefit_fin_nr             = $data->benefit_fin_nr ?? null;
        $memberBenefit->benefit_fin_code           = $data->benefit_fin_code ?? null;
        $memberBenefit->benefit_bulutfin_mode      = $data->benefit_bulutfin_mode ?? null;

        if($rules=="1"){
            $memberBenefit->real_price = $data->amount ?? null;
        }else{
            $memberBenefit->real_price = $data->real_price ?? null;
        }

        return $memberBenefit;
    }

    public function normalizeData($data)
    {
        return $data->map(function ($item) {
            if ($item instanceof Collection) {
                return $item->map(function ($subItem) {
                    return $this->convertToMemberBenefit($subItem);
                });
            } elseif ($item instanceof MemberBenefit) {
                return $item;
            } else {
                return $this->convertToMemberBenefit($item);
            }
        })->flatten();
    }


    public function getMemberBalanceByGroupId($member_id, $group_id)
    {
        $m_member_benefit = new MemberBenefit;
        $predefinedBenefits = $m_member_benefit->getBenefits($member_id);
        $predefinedFamilyBenefits = $m_member_benefit->getFamilyBenefits($member_id);

        $m_member_benefit_cart = new MemberBenefitCart;
        $selectedBenefits = $m_member_benefit_cart->getBenefits($member_id);


        $finalFamilyBenefits = $this->getFinalFamilyBenefits($member_id);

        // Eğer aynı benefit hem seçilmişlerde hem de diğerlerinde varsa, ignore edecek.
        $finalBenefits = $predefinedBenefits;


        $finalBenefitsSeleceted = [];
        foreach ($finalBenefits as $i => $finalBenefit) {
            $finalBenefits[$finalBenefit['id']] = $finalBenefit;
        }


        foreach ($selectedBenefits as $benefitId => $selectedBenefit) {

            if ($selectedBenefit instanceof Collection) {
                foreach ($selectedBenefit as $item) {
                    $finalBenefits[$benefitId] = $this->convertToMemberBenefit($item);
                }
            } else {
                $finalBenefits[$benefitId] = $selectedBenefit;
            }
        }

        // $finalBenefits = $this->normalizeData($finalBenefits);

        // Kategorilere göre gruplanmış olarak predefinedları al
        $predefinedBenefitsByCategory = [];
        foreach ($predefinedBenefits as $benefit) {
            if (!empty($predefinedBenefitsByCategory[$benefit['benefit_group_id']])) {
                $predefinedBenefitsByCategory[$benefit['benefit_group_id']] += $benefit['real_price'];
            } else {
                $predefinedBenefitsByCategory[$benefit['benefit_group_id']] = null;
            }
        }

        foreach ($predefinedFamilyBenefits as $familyMember) {
            foreach ($familyMember as $benefit) {
                if (!empty($predefinedBenefitsByCategory[$benefit['benefit_group_id']])) {
                    $predefinedBenefitsByCategory[$benefit['benefit_group_id']] += $benefit['real_price'];
                } else {
                    $predefinedBenefitsByCategory[$benefit['benefit_group_id']] = null;
                }
            }
        }

        // finalBenefitleri grupla
        $finalBenefitsByCategory = [];
        foreach ($finalBenefits as $benefit) {
            if (!empty($finalBenefitsByCategory[$benefit['benefit_group_id']])) {
                $finalBenefitsByCategory[$benefit['benefit_group_id']] += $benefit['real_price'];
            } else {
                $finalBenefitsByCategory[$benefit['benefit_group_id']] = $benefit['real_price'];
            }
        }


        // familyBenefitlerini de uyguluyoruz.
        foreach ($finalFamilyBenefits as $familyMember) {
            foreach ($familyMember as $benefit) {

                if (!empty($finalBenefitsByCategory[$benefit['benefit_group_id']])) {
                    $finalBenefitsByCategory[$benefit['benefit_group_id']] += $benefit['real_price'];
                } else {
                    $finalBenefitsByCategory[$benefit['benefit_group_id']] = null;
                }
            }
        }

        return $predefinedBenefitsByCategory[$group_id] - $finalBenefitsByCategory[$group_id];
    }


    public function getFamilyBenefits($member_id = false)
    {
        if (!$member_id) {
            $member_id = Member::get('id');
        }

        $benefits = $this->getAllBenefits();


        $m_member_family = new MemberFamily();

        // Array of children
        $children = $m_member_family->getChildren($member_id);

        // Single Result
        $partner = $m_member_family->getPartner($member_id);

        $family_benefits = [];
        foreach ($benefits as $benefit) {
            if ($partner) {
                $info = $this->getBenefitInfo($member_id, $benefit['id'], null, 'partner');
                if (!empty($info)) {
                    // ID'leri aile birey id'si ile değiştiriyoruz.
                    $info['id'] = $partner['id'];
                    $info['relative_name'] = $partner['name'];
                    $family_benefits[$benefit['id']][$partner['id']] = $info;
                }
            }

            if ($children) {
                foreach ($children as $child) {
                    $child_info = $this->getBenefitInfo($member_id, $benefit['id'], $child['birth_date'], 'child');
                    if (!empty($child_info)) {
                        // ID'leri aile birey id'si ile değiştiriyoruz.
                        $child_info['id'] = $child['id'];
                        $child_info['relative_name'] = $child['name'];
                        $family_benefits[$benefit['id']][$child['id']] = $child_info;
                    }
                }
            }
        }

        return $family_benefits;
    }

    public function getPredefinedGrandTotal($member_id)
    {
//        $member = Member::where('id', $member_id)->first();
        $member = Cache::remember("member_{$member_id}", 3600, function () use ($member_id) {
            return Member::where('id', $member_id)->select('id', 'percentage')->first();
        });

        if (!$member) {
            return 0;
        }

        $m_member_benefits = new MemberBenefit();

        // $m_member_optional_benefits = new Benefit();

        $benefits = $m_member_benefits->getBenefits($member_id);

        // $benefits_izin = $m_member_optional_benefits->getBenefits($member_id);

        $family_benefits = $m_member_benefits->getFamilyBenefits($member_id);


        $percentage = $member->percentage;
//        $percentage = Member::where('id', $member_id)->value('percentage'); // Tek bir değer döndür

        $grandTotal = 0;

        $processedBenefits = [];
        $realPrices = [];
        foreach ($benefits as $benefit) {
            if (in_array($benefit['id'], $processedBenefits)) {
                continue; // Bu döngüyü atla
            }

            if ($benefit['type'] != "5") {
                if ($benefit['convert_price']) {
                    if (($benefit['benefit_group_id'] == "14") && (is_null($benefit['real_price']) || $benefit['real_price'] == "0.00")) {
                        $real_price = $benefit['amount'] / ($percentage / 100);
                    } else {
                        $real_price = $benefit['real_price'] / ($percentage / 100);
                    }
                }else if ($benefit['equal_price']) {
                    if ($benefit['benefit_group_id'] == "14" && (is_null($benefit['real_price']) || $benefit['real_price'] == "0.00")) {
                        $real_price = $benefit['amount'];
                    } else {
                        $real_price = $benefit['real_price'];
                    }
                }else {
                    if ($benefit['benefit_group_id'] == "14" && (is_null($benefit['real_price']) || $benefit['real_price'] == "0.00")) {
                        $real_price = $benefit['amount'];
                    } else {
                        $real_price = $benefit['real_price'];
                    }
                }


            } else {
                    $real_price = $benefit['amount'] * $benefit['option_price'];

            }
            $processedBenefits[] = $benefit['id'];
            $realPrices[] = [
                'benefit_id' => $benefit['id'],
                'amount' => $benefit['amount'],
                'real_price' => $real_price,
                'calculation' => $benefit['type'] != "5" ?
                    ($benefit['convert_price'] ?
                        ($benefit['group_id'] == "14" && (is_null($benefit['real_price']) || $benefit['real_price'] == "0.00") ?
                            "amount / (percentage / 100)" : "real_price / (percentage / 100)"
                        ) :
                        ($benefit['group_id'] == "14" && (is_null($benefit['real_price']) || $benefit['real_price'] == "0.00") ?
                            "amount" : "real_price"
                        )
                    ) :
                    "amount * option_price"
            ];



            $grandTotal += $real_price;

        }




        foreach ($family_benefits as $family_member) {
            foreach ($family_member as $benefit) {
                if ($benefit['convert_price']) {
                    $benefit['real_price'] = $benefit['real_price'] / ($percentage / 100);
                }
                $grandTotal += $benefit['real_price'];
            }
        }

        if ($percentage != 0) {
            $balance = $member->positive_balance / ($percentage / 100);
//            $balance = $member->positive_balance;
        } else {
            $balance = 0;
        }

        $grandTotal += $balance;
        return $grandTotal;
    }


    public function getFinalFamilyBenefits($memberId, $dontOmitPrerequisites = false, $noFamilyOption = false)
    {
        $memberBenefitsCartModel = new MemberBenefitCart();
        $memberModel = new Member();

        $memberInfo = $memberModel->find($memberId);

        $selectedFamilyBenefitsData = $memberBenefitsCartModel->getFamilyBenefits($memberId, $noFamilyOption);
        $predefinedFamilyBenefits = $this->getFamilyBenefits($memberId, true);

        if ($noFamilyOption) {
            foreach ($predefinedFamilyBenefits as $memberFamilyId => $memberFamily) {
                foreach ($memberFamily as $benefitId => $familyBenefit) {
                    if (!isset($selectedFamilyBenefitsData[$memberFamilyId][$benefitId])) {
                        $tmp = $predefinedFamilyBenefits[$memberFamilyId][$benefitId];
                        $tmp['real_price'] = $tmp['real_price'] / $memberInfo->family_percentage * 100;
                        $selectedFamilyBenefitsData[$memberFamilyId][$benefitId] = $tmp;
                    }
                }
            }
        }

        $finalFamilyBenefits = $predefinedFamilyBenefits;
        foreach ($selectedFamilyBenefitsData as $key => $selectedBenefit) {
            foreach ($selectedBenefit as $key_2 => $familyMemberBenefit) {
                if ($familyMemberBenefit['real_price'] != '0.00') {
                    $finalFamilyBenefits[$key][$key_2] = $familyMemberBenefit;
                } else {
                    unset($finalFamilyBenefits[$key][$key_2]);
                }
            }
        }

        if ($dontOmitPrerequisites) {
            return $finalFamilyBenefits;
        }

        // Prerequisites kontrolü
        foreach ($finalFamilyBenefits as $benefitId => $familyBenefit) {
            foreach ($familyBenefit as $memberFamilyId => $memberFamilyBenefit) {
                if ($memberFamilyBenefit['prerequisite_benefit_id']) {
                    if (
                        !isset($finalFamilyBenefits[$memberFamilyBenefit['prerequisite_benefit_id']][$memberFamilyId]) ||
                        $finalFamilyBenefits[$memberFamilyBenefit['prerequisite_benefit_id']][$memberFamilyId]['real_price'] == '0.00'
                    ) {
                        unset($finalFamilyBenefits[$benefitId][$memberFamilyId]);
                    }
                }
            }
        }

        return $finalFamilyBenefits;
    }

    public function getSelectedBenefitOption($memberId, $benefitId)
    {
        $memberBenefit = MemberBenefit::where('member_id', $memberId)
            ->where('benefit_id', $benefitId)
            ->first();

        $memberBenefitCart = MemberBenefitCart::where('member_id', $memberId)
            ->where('benefit_id', $benefitId)
            ->where('member_family_id', 0)
            ->first();

        if (!empty($memberBenefitCart->benefit_option_id)) {
            return $memberBenefitCart->benefit_option_id;
        } elseif (!empty($memberBenefit->benefit_option_id)) {
            return $memberBenefit->benefit_option_id;
        } else {
            return 0;
        }
    }

    public function getBenefitInfo($memberId, $benefitId, $birthDate, $type, $benefitOptionId = false)
    {
        $age = $this->calculateAge($birthDate);

        if (!$benefitOptionId) {
            $benefitOptionId = $this->getSelectedBenefitOption($memberId, $benefitId);
        }

        $query = DB::table('family_benefits')
            ->selectRaw('fbd.*, family_benefits.prerequisite_benefit_id, family_benefits.has_company_support, fbd.price AS real_price, fbd.id AS detail_id, benefits.*, benefits.benefit_group_id AS group_id, benefit_options.name AS option_name, family_benefits.benefit_option_id AS option_id')
            ->join('family_benefit_details as fbd', 'fbd.family_benefit_id', '=', 'family_benefits.id')
            ->join('benefits', 'family_benefits.benefit_id', '=', 'benefits.id')
            ->leftJoin('benefit_options', 'benefit_options.id', '=', 'family_benefits.benefit_option_id')
            ->where('family_benefits.benefit_id', '=', $benefitId)
            ->where('fbd.relation', '=', $type)
            ->where(function ($query) use ($benefitOptionId) {
                $query->where('family_benefits.benefit_option_id', '=', 0)
                    ->orWhere('family_benefits.benefit_option_id', '=', $benefitOptionId);
            });

        if ($type == 'child') {
            $query->where('age_start', '<=', $age)
                ->where('age_end', '>=', $age);
        }

        return $query->first();
    }

    public function options()
    {
        return $this->hasMany(BenefitOption::class, 'benefit_id');
    }

    public function optionsUpdated()
    {
        return $this->hasMany(BenefitOption::class, 'benefit_id')->where('update_status', 1);
    }


    /**
     * Check the min and max limits for a given benefit.
     *
     * @param int $benefit_id
     * @return array|null
     */
    public static function checkLimits(int $benefit_id)
    {
        return self::select('min_amount', 'max_amount')
            ->where('id', $benefit_id)
            ->where(function ($query) {
                $query->whereNotNull('min_amount')
                    ->orWhereNotNull('max_amount');
            })
            ->first();
    }

    public static function getByBenefitId($benefitId)
    {
        $benefit = Benefit::join('benefit_groups', 'benefit_groups.id', '=', 'benefits.benefit_group_id')
            ->where('benefits.id', $benefitId)
            ->select('benefit_groups.announcement_id')
            ->first();

        return $benefit ? $benefit->announcement_id : 0;
    }

    public function getFinalFamilyBenefitsTotalsByGroup($memberId)
    {
        $finalFamilyBenefits = $this->getFinalFamilyBenefits($memberId);

        $finalFamilyBenefitTotalsByGroup = [];
        foreach ($finalFamilyBenefits as $familyBenefit) {
            foreach ($familyBenefit as $familyMemberBenefit) {
                if (!isset($finalFamilyBenefitTotalsByGroup[$familyMemberBenefit['benefit_group_id']])) {
                    $finalFamilyBenefitTotalsByGroup[$familyMemberBenefit['benefit_group_id']] = 0;
                }

                $finalFamilyBenefitTotalsByGroup[$familyMemberBenefit['benefit_group_id']] += $familyMemberBenefit['real_price'];
            }
        }

        return $finalFamilyBenefitTotalsByGroup;
    }


    public function getMemberBalanceOLD($member_id, $get_if_it_is_changed = false, $calculate_real_balance = false)
    {
        $rules = CustomSettings::getRules();



        $member = Member::find($member_id);
        if (!$member) {
            return null;
        }

        if($rules=="1")
        {
            $m_custom_balance=new CustomBalance();
            $custom_balance_db=$m_custom_balance->getCustomBalance($member_id);
        }

        $percentage = $member->percentage;

        $m_member_benefit = new MemberBenefit();
        $predefinedBenefits = $m_member_benefit->getBenefits($member_id);

        $predefinedFamilyBenefits = $m_member_benefit->getFamilyBenefits($member_id);
        $finalBenefits = $this->getFinalBenefits($member_id);
        $finalFamilyBenefits = $this->getFinalFamilyBenefits($member_id, $calculate_real_balance);


        $predefinedBenefitsByCategory = [];


        $m_member_benefits_cart = new MemberBenefitCart();
        $processedBenefitIds = [];
        $debugLogs = []; // Debug loglarını tutmak için bir dizi

        foreach ($predefinedBenefits as $benefit) {
            // Eğer benefit_id daha önce işlendiyse bu iterasyonu atla
            if (in_array($benefit['benefit_id'], $processedBenefitIds)) {
                continue;
            }

            // Sadece group_id = 14 için debug yap
            if ($benefit['group_id'] == "14") {
                $debugLogs['predefined'][] = [
                    'benefit_id' => $benefit['benefit_id'],
                    'amount' => $benefit['amount'],
                    'real_price' => $benefit['real_price'],
                    'really_equal_price' => $benefit['really_equal_price'],
                ];
            }

            // Eğer group_id yoksa başlangıç değeri ata
            if (empty($predefinedBenefitsByCategory[$benefit['group_id']])) {
                $predefinedBenefitsByCategory[$benefit['group_id']] = 0;
            }

            if ($benefit['benefit_group_id'] == "17" || $benefit['benefit_group_id'] == "14") {
                if (is_null($benefit['real_price']) || $benefit['real_price'] == "0.00") {
                    $predefinedBenefitsByCategory[$benefit['group_id']] += $benefit['amount'] / $percentage * 100;
                } else {
                    $predefinedBenefitsByCategory[$benefit['group_id']] += $benefit['really_equal_price'] ? $benefit['real_price'] / $percentage * 100 : $benefit['real_price'];
                }
            } else {
                $predefinedBenefitsByCategory[$benefit['group_id']] += $benefit['really_equal_price'] ? $benefit['real_price'] / $percentage * 100 : $benefit['real_price'];
            }

            // İşlenmiş benefit_id'yi diziye ekle
            $processedBenefitIds[] = $benefit['benefit_id'];
        }

        foreach ($predefinedFamilyBenefits as $familyMember) {
            foreach ($familyMember as $benefit) {
                if ($benefit['group_id'] == "14") {
                    $debugLogs['predefinedFamily'][] = [
                        'amount' => $benefit['amount'],
                        'real_price' => $benefit['real_price'],
                        'really_equal_price' => $benefit['really_equal_price'],
                    ];
                }

                if (empty($predefinedBenefitsByCategory[$benefit['group_id']])) {
                    $predefinedBenefitsByCategory[$benefit['group_id']] = 0;
                }
                $predefinedBenefitsByCategory[$benefit['group_id']] += $benefit['really_equal_price'] ? $benefit['real_price'] / $percentage * 100 : $benefit['real_price'];
            }
        }

        $finalBenefitsByCategory = [];

        foreach ($finalBenefits as $benefit) {
            if ($benefit['group_id'] == "14") {
                $debugLogs['final'][] = [
                    'benefit_id' => $benefit['benefit_id'],
                    'amount' => $benefit['amount'],
                    'real_price' => $benefit['real_price'],
                    'really_equal_price' => $benefit['really_equal_price'],
                ];
            }

            if (empty($finalBenefitsByCategory[$benefit['group_id']])) {
                $finalBenefitsByCategory[$benefit['group_id']] = 0;
            }

            if ($benefit['group_id'] == "17" || $benefit['group_id'] == "14") {
                if (is_null($benefit['real_price']) || $benefit['real_price'] == "0.00") {
                    $finalBenefitsByCategory[$benefit['group_id']] += $benefit['amount'] / $percentage * 100;
                } else {
                    $finalBenefitsByCategory[$benefit['group_id']] += $benefit['really_equal_price'] ? $benefit['real_price'] / $percentage * 100 : $benefit['real_price'];
                }
            } else {
                $finalBenefitsByCategory[$benefit['group_id']] += $benefit['really_equal_price'] ? $benefit['real_price'] / $percentage * 100 : $benefit['real_price'];
            }
        }

        foreach ($finalFamilyBenefits as $familyMember) {
            foreach ($familyMember as $benefit) {
                if (empty($finalBenefitsByCategory[$benefit['group_id']])) {
                    $finalBenefitsByCategory[$benefit['group_id']] = 0;
                }
                $finalBenefitsByCategory[$benefit['group_id']] += $benefit['really_equal_price'] ? $benefit['real_price'] / $percentage * 100 : $benefit['real_price'];
            }
        }

        if($rules=="1"){
            $balance=$custom_balance_db->amount ?? $member->positive_balance;

        }else{
            $balance = $member->positive_balance;
            $is_changed = false;


            foreach ($predefinedBenefitsByCategory as $group_id => $categoryPriceTotal) {

                if (!isset($finalBenefitsByCategory[$group_id])) {
                    $balance += $categoryPriceTotal * $percentage / 100;
                    $is_changed = true;
                } elseif ($finalBenefitsByCategory[$group_id] > $categoryPriceTotal) {
                    $balance -= ($finalBenefitsByCategory[$group_id] - $categoryPriceTotal);
                    $is_changed = true;
                } elseif ($finalBenefitsByCategory[$group_id] <= $categoryPriceTotal) {
                    $balance += (($categoryPriceTotal - $finalBenefitsByCategory[$group_id]) * $percentage / 100);
                    $is_changed = true;
                }
            }

            foreach ($finalBenefitsByCategory as $group_id => $priceTotal) {
                if (!isset($predefinedBenefitsByCategory[$group_id])) {
                    $balance -= $priceTotal;
                    $is_changed = true;
                }
            }
            if ($get_if_it_is_changed) {
                return $is_changed;
            }

        }

        return sprintf('%.2f', $balance);
    }


    public function getMemberBalance($member_id)
    {
        $member = Member::find($member_id);
        if (!$member) {
            return ['balance' => 0, 'brut_balance' => 0];
        }

        // Başlangıç NET (veritabanından)
        $initialNet = floatval($member->positive_balance ?? 0);

        // Ledger kayıtları
        $rows = \DB::table('member_benefit_balance_ledger')
            ->where('member_id', $member_id)
            ->where('fin_type', '!=', 'izin_to_balance')
            ->select('benefit_id', 'reference_price_net', 'price_net', 'reference_price_brut', 'price_brut')
            ->get();

        $deltaNetByBenefit  = [];
        $deltaBrutByBenefit = [];

        foreach ($rows as $row) {
            $benefitId = $row->benefit_id;

            $refNet  = floatval($row->reference_price_net ?? 0);
            $newNet  = floatval($row->price_net ?? 0);
            $deltaNet = $refNet - $newNet;
            $deltaNetByBenefit[$benefitId] = ($deltaNetByBenefit[$benefitId] ?? 0) + $deltaNet;

            $refBrut  = floatval($row->reference_price_brut ?? 0);
            $newBrut  = floatval($row->price_brut ?? 0);
            $deltaBrut = $refBrut - $newBrut;
            $deltaBrutByBenefit[$benefitId] = ($deltaBrutByBenefit[$benefitId] ?? 0) + $deltaBrut;
        }

        $totalDeltaNet  = array_sum($deltaNetByBenefit);
        $totalDeltaBrut = array_sum($deltaBrutByBenefit);

        // Başlangıç BRÜT (BulutFin'den NR01 koduyla)
        $bulutfinResponse = app(BordroService::class)->request('yillikozet');
        $yanHakList = data_get($bulutfinResponse, 'Data.YanHakSumList', []);
        $bonusItem = collect($yanHakList)->firstWhere('CODE', 'NR01');
        $initialBrut = $bonusItem ? floatval(data_get($bonusItem, 'YanHakBurut', 0)) : 0;


        $izinBrutTotal = \DB::table('member_benefit_balance_ledger')
            ->where('member_id', $member_id)
            ->where('fin_type', 'izin_to_balance')
            ->sum('price_brut');

        $izinNetTotal = \DB::table('member_benefit_balance_ledger')
            ->where('member_id', $member_id)
            ->where('fin_type', 'izin_to_balance')
            ->sum('price_net');

        $balance     = $initialNet + $totalDeltaNet + $izinNetTotal;
        $brutBalance = $initialBrut + $totalDeltaBrut + $izinBrutTotal;
        // Toplam hesaplama
//        $balance     = $initialNet + $totalDeltaNet;
//        $brutBalance = $initialBrut + $totalDeltaBrut;


        return [
            'balance'      => round($balance, 2),
            'brut_balance' => round($brutBalance, 2),
        ];
    }
    public function getMemberBalanceTEST($member_id)
    {
        $member = Member::find($member_id);
        if (!$member) {
            return ['balance' => 0, 'brut_balance' => 0];
        }

        $initial = floatval($member->positive_balance ?? 0);

        $rows = \DB::table('member_benefit_balance_ledger')
            ->where('member_id', $member_id)
            ->select('benefit_id', 'reference_price_net', 'price_net')
            ->get();

        $deltaByBenefit = [];

        foreach ($rows as $row) {
            $benefitId = $row->benefit_id;

            $ref = floatval($row->reference_price_net ?? 0);
            $new = floatval($row->price_net ?? 0);

            // Pozitif bakiye için etki: ref - net (örnek: 60 - 48 = 12 kazanç)
            $delta = $ref - $new;

            if (!isset($deltaByBenefit[$benefitId])) {
                $deltaByBenefit[$benefitId] = 0;
            }

            $deltaByBenefit[$benefitId] += $delta;
        }

        $totalDelta = array_sum($deltaByBenefit);
        $balance = $initial + $totalDelta;

        // NR01 koduna sahip brut değeri bul
        $bulutfinResponse = app(BordroService::class)->request('yillikozet');
        $yanHakList = data_get($bulutfinResponse, 'Data.YanHakSumList', []);
        $bonusItem = collect($yanHakList)->firstWhere('CODE', 'NR01');
        $brutBalance = $bonusItem ? floatval(data_get($bonusItem, 'YanHakBurut', 0)) : 0;

        return [
            'balance' => round($balance, 2),
            'brut_balance' => $brutBalance,
        ];
    }
    public function getInitialPositiveBalance(int $member_id): float
    {
        return (float) Member::find($member_id)?->initial_balance ?? 0;
    }

    public function getIfIsChanged($member_id)
    {
        // Genel kategori toplamlarına bakalım. Eğer değiştiyse, sepet değişti olarak işaretleyebiliriz.
        $isChanged = $this->getMemberBalance($member_id, true);
        if ($isChanged) {
            return true;
        }

        // Genel toplamlar tutuyor; fakat kategori içerisinde seçeneklerde oynama yapmış olabilir, kontrol edelim.
        $m_member_benefit = new MemberBenefit();
        $predefinedBenefits = $m_member_benefit->getBenefits($member_id);
        $predefinedFamilyBenefits = $m_member_benefit->getFamilyBenefits($member_id);

        $finalBenefits = $this->getFinalBenefits($member_id);

        $finalFamilyBenefits = $this->getFinalFamilyBenefits($member_id);

        // Ön tanımlı kendi haklarını tek tek son haliyle kıyaslıyoruz.
        // Ön tanımlı olan bir şeyler finalde yoksa, değişmiştir.
        // Finalde varsa, final arrayinden siliyoruz.
        // En sonunda final arrayinde hak kalırsa, yeni haklar eklenmiş demektir.
        foreach ($predefinedBenefits as $benefit) {
            $finalBenefit = $finalBenefits[$benefit['id']] ?? false;

            if (!$finalBenefit || $benefit['real_price'] != $finalBenefit['real_price']) {
                return true;
            } else {
                unset($finalBenefits[$benefit['id']]);
            }
        }

        // Ön tanımlı kendi haklarında yaptığımız işlemleri burada da uyguluyoruz.
        foreach ($predefinedFamilyBenefits as $benefitId => $familyBenefit) {
            foreach ($familyBenefit as $familyMemberId => $familyMember) {
                $finalFamilyBenefit = $finalFamilyBenefits[$benefitId][$familyMemberId] ?? false;

                if (!$finalFamilyBenefit || $familyMember['real_price'] != $finalFamilyBenefit['real_price']) {
                    return true;
                } else {
                    unset($finalFamilyBenefits[$benefitId][$familyMemberId]);
                    if (empty($finalFamilyBenefits[$benefitId])) {
                        unset($finalFamilyBenefits[$benefitId]);
                    }
                }
            }
        }

        // Ön tanımlı hakları array'den temizledik ama hala hak var. Demek ki yeni seçimler yaptı.
        if ($finalFamilyBenefits || $finalBenefits) {
            return true;
        }

        return false;
    }

    public function getGroupNames()
    {
        return DB::table('benefit_groups')->pluck('name', 'id')->toArray();
    }


    public function getGroupNamesEng()
    {
        return DB::table('benefit_groups')->pluck('name_en', 'id')->toArray();
    }



    public function getCheckBenefitOptionPref($optionId, $memberId, $defaultPrice)
    {
        if ($optionId) {
            $result = DB::table('member_benefit_prefs')
                ->where('benefit_option_id', $optionId)
                ->where('member_id', $memberId)
                ->where('is_visible', 1)
                ->first();

            if(is_null($result)){
                $result_predefined = DB::table('member_benefits')
                    ->where('benefit_option_id', $optionId)
                    ->where('member_id', $memberId)
                    ->first();
            }

            if ($result) {
                return $result->price;
            }else if($result_predefined){
                return $result_predefined->amount;
            }
        }

        return $defaultPrice;
    }

    public function getShowOnPositiveBenefits($memberId)
    {

        $benefits = DB::table('benefits')
            ->leftJoin('benefit_groups as bg', 'bg.id', '=', 'benefits.benefit_group_id')
            ->select('benefits.*', 'bg.image as group_image')
            ->where('show_on_positive_balance', 1)
            ->get();

        $m_member_benefit = new MemberBenefit();
        $predefinedBenefits = $m_member_benefit->getBenefits($memberId);

        $predefinedBenefitsArray = $predefinedBenefits->pluck('id')->toArray();

        foreach ($benefits as $key => $benefit) {
            if ($benefit->hide_if_not_predefined && !in_array($benefit->id, $predefinedBenefitsArray)) {
                $benefits->forget($key);
            }
        }

        return $benefits;
    }

    public function benefitOptions(): HasMany
    {
        return $this->hasMany(BenefitOption::class, 'benefit_id', 'id');
    }


    // public function getGroups()
    // {
    //     return $this->where('status', 1)
    //                 ->orderBy('order_no', 'ASC')
    //                 ->get()
    //                 ->keyBy('id')
    //                 ->toArray();
    // }

    // public function getBenefits($memberId = null)
    // {
    //     $query = $this->newQuery();

    //     $query->select([
    //         'id',
    //         'order_no',
    //         'name',
    //         'benefit_group_id',
    //         'description',
    //         'alert_message',
    //         'image',
    //         'comment',
    //         'alert_color',
    //         'status',
    //         'has_choice',
    //         'has_flexible_price',
    //         'flexible_price',
    //         'type',
    //         'flexible_amount_title',
    //         'title',
    //         'hide_if_not_predefined',
    //         'show_on_positive_balance',
    //         'price_original',
    //         'details',
    //         'has_details',
    //         'show_option_info',
    //         'convert_price',
    //         'equal_price',
    //         'really_equal_price'
    //     ]);

    //     if ($memberId) {
    //         $query->addSelect([DB::raw('IFNULL((SELECT price FROM member_benefit_prefs p WHERE p.benefit_id = benefits.id AND p.member_id = ' . $memberId . ' LIMIT 1), benefits.price) AS price')]);
    //     }

    //     $query->where('status', 1)
    //           ->orderBy('order_no', 'ASC');

    //     return $query->get()->groupBy('benefit_group_id')->toArray();
    // }

    // public function getBenefitOptions($memberId = null)
    // {
    //     $query = $this->newQuery();

    //     $query->select([
    //         'update_status',
    //         'status',
    //         'price_original',
    //         DB::raw('IFNULL(p.price, benefit_options.price) AS price'),
    //         'order_no',
    //         'name',
    //         'is_addon',
    //         'image',
    //         'id',
    //         'has_details',
    //         'details',
    //         'description',
    //         'comment',
    //         'benefit_id',
    //         'alert_message',
    //         'alert_color'
    //     ]);

    //     if ($memberId) {
    //         $query->leftJoin('member_benefit_prefs as p', function ($join) use ($memberId) {
    //             $join->on('p.benefit_option_id', '=', 'benefit_options.id')
    //                  ->where('p.member_id', '=', $memberId);
    //         })
    //         ->where(function ($query) {
    //             $query->whereNull('p.id')->orWhere('p.is_visible', 1);
    //         });
    //     }

    //     $query->where('benefit_options.status', 1);

    //     return $query->get()->groupBy('benefit_id')->toArray();
    // }

    public function getStatusAttribute($value)
    {
        return $value == 2;
    }

    public function setStatusAttribute($value)
    {
        $this->attributes['status'] = $value ? 2 : 0;
    }
}
