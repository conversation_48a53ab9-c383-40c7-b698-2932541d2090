<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;


class Report extends Model
{
    use HasFactory;

    protected $guarded = ["id"];

    public static function getCustomReports()
    {
        $data = DB::table('members')
            ->select(
                'name as NAME',
                'email',
                'identity_number',
                'last_login_date'
            )
            ->get();

        $columns = ['Name', 'Email', 'Identity Number', 'Last Login Date'];

        return ['data' => $data, 'columns' => $columns, 'name' => 'CustomReports'];
    }

    public static function predefinedBenefitsReports()
    {
        $sql = "SELECT
        IF(benefits.name LIKE 'ARABA%', 'ARABA',
            IF(benefits.name LIKE 'SERVİS BEDELİ%', 'SERVİS BEDELİ',
                IF(benefits.name LIKE 'IPAD DATA HATTI BEDELİ%', 'IPAD DATA HATTI BEDELİ', CONCAT(benefits.name)
                )
            )
        ) AS Hak,
        members.name,
        IF(has_flexible_price,
                IF(flexible_price = 'Adet', benefits.price * member_benefits.amount, member_benefits.amount ),
                IFNULL(benefit_options.price, benefits.price)
        ) AS real_price,
        /* firms.name AS 'Firma', */
        members.identity_number AS 'TC Kimlik NO'
    FROM member_benefits
    INNER JOIN members ON members.id = member_benefits.member_id
    LEFT JOIN firms ON firms.id = members.firm_id
    INNER JOIN benefits ON benefits.id = member_benefits.benefit_id
    LEFT JOIN benefit_options ON benefit_options.id = member_benefits.benefit_option_id
    WHERE IF(has_flexible_price,
                IF(flexible_price = 'Adet', benefits.price * member_benefits.amount, member_benefits.amount ),
                IFNULL(benefit_options.price, benefits.price)
            ) != '0.00' OR benefits.id = 36
    ORDER BY member_id ASC";

        $data = DB::select($sql);
        $columns = [
            'Benefit',
            'Name',
            'Real Price',
            /* 'Firma', */
            'Identity Number'];
        return ['data' => $data, 'columns' => $columns, 'name' => 'PredefinedBenefitsReports'];

    }

    public static function hakListesiSecenekli()
    {
        $sql = "SELECT
            IF(benefits.name LIKE 'ARABA%', 'ARABA',
                IF(benefits.name LIKE 'SERVİS BEDELİ%', 'SERVİS BEDELİ',
                    IF(benefits.name LIKE 'IPAD DATA HATTI BEDELİ%', 'IPAD DATA HATTI BEDELİ', CONCAT(benefits.name)
                    )
                )
            ) AS Hak,
            members.name,
            members.identity_number,
            benefit_options.name AS Secenek
        FROM orders
        INNER JOIN order_items ON order_items.order_id = orders.id
        INNER JOIN members ON members.id = orders.member_id
        INNER JOIN benefits ON benefits.id = order_items.benefit_id
        INNER JOIN benefit_groups ON benefit_groups.id = benefits.benefit_group_id
        LEFT JOIN benefit_options ON benefit_options.id = order_items.benefit_option_id
        WHERE order_items.member_family_id = 0 AND benefits.has_choice
        ORDER BY member_id ASC, benefit_groups.order_no ASC";

        $columns = ['Benefit', 'Name', 'Identity Number', 'Option'];
        $data = DB::select($sql);




        return ['data' => $data, 'columns' => $columns, 'name' => 'HakListesiSecenekli'];
    }

    public static function secimSonuclari()
    {
        // En son order'ları bul (member_id bazlı en yüksek id'yi al)
        $latestOrders = DB::table('orders')
            ->select('member_id', DB::raw('MAX(id) as latest_order_id'))
            ->groupBy('member_id')
            ->pluck('latest_order_id', 'member_id'); // [member_id => latest_order_id]

        // Hakları (header) getir
        $header = DB::table('benefits')
            ->join('benefit_groups', 'benefit_groups.id', '=', 'benefits.benefit_group_id')
            ->orderBy('benefit_groups.order_no')
            ->orderBy('benefits.order_no')
            ->pluck('benefits.name')
            ->toArray();


        $header[] = 'Yıllık İzin Harçlığı Tutar';

        // Member Benefit Cart'taki tutarları al
        $cartData = DB::table('member_benefits_cart')
            ->select('member_id', 'benefit_id', 'amount')
            ->get()
            ->groupBy('member_id');

        // Member Benefits'teki tutarları al
        $benefitsData = DB::table('member_benefits')
            ->select('member_id', 'benefit_id', 'amount')
            ->get()
            ->groupBy('member_id');

        // Member Benefit Prefs'teki 149 ID'li hak için price değerlerini al
        $prefsData = DB::table('member_benefit_prefs')
            ->where('benefit_id', 149)
            ->select('member_id', 'price')
            ->pluck('price', 'member_id'); // [member_id => price]

        // En son order'ı olan üyeleri getir
        $members = DB::table('members')
            ->whereIn('id', array_keys($latestOrders->toArray())) // Sadece latest order'ı olanlar
            ->select('id', 'identity_number', 'name', 'firm_id')
            ->get();

        $reportData = [];

        foreach ($members as $member) {
            $row = [
                'Identity Number' => $member->identity_number,
                'Name' => $member->name,
                'Firm' => $member->firm_id ? DB::table('firms')->where('id', $member->firm_id)->value('name') : '-',
            ];

            foreach ($header as $benefitName) {
                // Benefit ID'yi belirle
                $benefitId = DB::table('benefits')->where('name', $benefitName)->value('id');

                // Öncelikle Cart'ta var mı kontrol et
                if (isset($cartData[$member->id])) {
                    $benefitCart = $cartData[$member->id]->firstWhere('benefit_id', $benefitId);
                    $row[$benefitName] = $benefitCart ? (string)$benefitCart->amount : '-';
                }

                // Cart'ta yoksa Benefits'te var mı kontrol et
                if (!isset($row[$benefitName]) || $row[$benefitName] === '-') {
                    if (isset($benefitsData[$member->id])) {
                        $benefit = $benefitsData[$member->id]->firstWhere('benefit_id', $benefitId);
                        $row[$benefitName] = $benefit ? (string)$benefit->amount : '-';
                    }
                }

                // Eğer hiçbirinde yoksa "-" atanır
                $row[$benefitName] = $row[$benefitName] ?? '-';
            }

            // 149 ID'li hak için Kalan İzin Günü (Hesap) hesapla
            $prefsPrice = $prefsData[$member->id] ?? 0;
            $benefitsAmount = isset($benefitsData[$member->id])
                ? $benefitsData[$member->id]->firstWhere('benefit_id', 149)->amount ?? 0
                : 0;
            $cartAmount = isset($cartData[$member->id])
                ? $cartData[$member->id]->firstWhere('benefit_id', 149)->amount ?? 0
                : 0;
            $fark = $benefitsAmount - $cartAmount;
            $row['Yıllık İzin Harçlığı Tutar'] = (string)($prefsPrice * $fark);


            $reportData[] = $row;
        }

        // Boş olan değerleri "0" yap
        $reportData = array_map(function ($row) {
            foreach ($row as $key => $value) {
                // Eğer değer boş string ise, "0" olarak değiştir
                if ($value === "") {
                    $row[$key] = "0";
                }
            }
            return $row;
        }, $reportData);

        return [
            'data' => $reportData,
            'columns' => array_merge(['Identity Number', 'Name', 'Firm'], $header),
            'name' => 'secim_sonuclari',
        ];
    }

    public static function uyeAileListesi()
    {
        $sql = "SELECT
                    members.name,
                    members.identity_number,
                    firms.name AS Firma,
                    members.percentage,
                    members.family_percentage,
                    members.maritial_status,
                    GROUP_CONCAT(DISTINCT member_family.type) as family_types,
                    GROUP_CONCAT(DISTINCT member_family.name SEPARATOR '|') as family_names
                FROM
                    members
                LEFT JOIN member_family ON members.id = member_family.member_id
                LEFT JOIN firms ON firms.id = members.firm_id
                GROUP BY members.id, members.name, members.identity_number, firms.name, members.percentage, members.family_percentage, members.maritial_status";

        $data = DB::select($sql);
        $columns = ['Name', 'Identity Number', 'Firm', 'Percentage', 'Family Percentage', 'Maritial Status', 'Family Type', 'Family Name'];
        return ['data' => $data, 'columns' => $columns, 'name' => 'UyeAileListesi'];
    }

    public static function ApprovedMembers()
    {
        $sql = "SELECT
            members.email AS 'E-posta',
            members.name AS 'İsim',
            members.identity_number AS 'TC Kimlik No',
            FROM_UNIXTIME(members.approval_date) AS 'Onaylanma Tarihi'
        FROM
            members
        WHERE
            is_approved = 1";

        $data = DB::select($sql);
        $columns = ['Email', 'Name', 'Identity Number', 'Approval Date'];

        return ['data' => $data,
            'columns' => $columns,
            'name' => 'ApprovedMembers'
        ];
    }

    public static function hakSecenekleriHediyeCekleri()
    {
        $sql = "SELECT
        CONCAT(
            IF(benefits.name LIKE 'ARABA%', 'ARABA',
                IF(benefits.name LIKE 'SERVİS BEDELİ%', 'SERVİS BEDELİ',
                    IF(benefits.name LIKE 'IPAD DATA HATTI BEDELİ%', 'IPAD DATA HATTI BEDELİ', CONCAT(benefits.name)
                    )
                )
            ), ' / ', COALESCE(benefits.price,0.00), ' TL') AS Hak,
        members.name,
        FLOOR(order_items.price / benefits.price) AS Adet
    FROM orders
        INNER JOIN order_items ON order_items.order_id = orders.id
        INNER JOIN members ON members.id = orders.member_id
        INNER JOIN benefits ON benefits.id = order_items.benefit_id
        INNER JOIN benefit_groups ON benefit_groups.id = benefits.benefit_group_id
        LEFT JOIN benefit_options ON benefit_options.id = order_items.benefit_option_id
        INNER JOIN (
            SELECT member_id, MAX(date) as MaxDate
            FROM orders
            GROUP BY member_id
        ) AS latest_orders ON latest_orders.member_id = orders.member_id AND latest_orders.MaxDate = orders.date
    WHERE order_items.member_family_id = 0 AND benefits.has_flexible_price = 1 AND benefits.flexible_price = 'Adet'
    ORDER BY members.id ASC, benefit_groups.order_no ASC";

        $data = DB::select($sql);
        $columns = ['Benefit / Price', 'Name', 'Amount', 'hakbazinda'];
        return ['data' => $data, 'columns' => $columns, 'name' => 'HakSecenekleriHediyeCekleri'];
    }

    // public static function hakSecenekleriHediyeCekleri()
    // {
        // $sql = "SELECT
        //     CONCAT(
        //         IF(benefits.name LIKE 'ARABA%', 'ARABA',
        //             IF(benefits.name LIKE 'SERVİS BEDELİ%', 'SERVİS BEDELİ',
        //                 IF(benefits.name LIKE 'IPAD DATA HATTI BEDELİ%', 'IPAD DATA HATTI BEDELİ', CONCAT(benefits.name)
        //                 )
        //             )
        //         ), ' / ', benefits.price, ' TL') AS Hak,
        //     members.name,
        //     FLOOR(order_items.price / benefits.price) AS Adet
        // FROM orders
        //     INNER JOIN order_items ON order_items.order_id = orders.id
        //     INNER JOIN members ON members.id = orders.member_id
        //     INNER JOIN benefits ON benefits.id = order_items.benefit_id
        //     INNER JOIN benefit_groups ON benefit_groups.id = benefits.benefit_group_id
        //     LEFT JOIN benefit_options ON benefit_options.id = order_items.benefit_option_id
        //     INNER JOIN (
        //         SELECT member_id, MAX(date) as MaxDate
        //         FROM orders
        //         GROUP BY member_id
        //     ) AS latest_orders ON latest_orders.member_id = orders.member_id AND latest_orders.MaxDate = orders.date
        // WHERE order_items.member_family_id = 0 AND benefits.has_flexible_price = 1 AND benefits.flexible_price = 'Adet'
        // ORDER BY members.id ASC, benefit_groups.order_no ASC";

    //     $data = DB::select($sql);
    //     $columns = ['Benefit / Price', 'Name', 'Amount', 'hakbazinda'];
    //     return ['data' => $data, 'columns' => $columns, 'name' => 'HakSecenekleriHediyeCekleri'];
    // }



    public static function anketCevaplariRaporu()
    {
        $sql = "SELECT
                q.text AS 'Soru',
                m.name AS 'Uye Adi',
                qa.answer AS 'Cevap',
                qa.date AS 'Cevap Tarihi'
            FROM
                question_answers qa
            INNER JOIN members m ON qa.member_id = m.id
            INNER JOIN questions q ON qa.question_id = q.id
            ORDER BY qa.date DESC";

        $data = DB::select($sql);
        $columns = ['Question', 'Name', 'Answer', 'Answer Date'];
        return ['data' => $data, 'columns' => $columns, 'name' => 'AnketCevaplariRaporu'];
    }


    public static function anketCevaplariOzetRaporu()
    {
        $sql = "SELECT
            q.text AS `Soru`,
            COUNT(DISTINCT qa.member_id) AS `Cevap Veren Kisi Sayisi`,
            AVG(CASE
                WHEN qa.answer REGEXP '^[0-9]+$' THEN qa.answer
                ELSE NULL END) AS `Ortalama Cevap`
        FROM
            question_answers qa
        LEFT JOIN
            questions q ON qa.question_id = q.id
        GROUP BY
            q.id, q.text -- `q.text` GROUP BY içinde
        ORDER BY
            q.order_no ASC";


        $data = DB::select($sql);

        // Kolon başlıkları (Header)
        $columns = ['Question', 'Answer Count', 'Average Answer'];

        // Rapor adını belirle
        $name = 'AnketCevaplariOzetRaporu';


        return [
            'data' => $data,
            'columns' => $columns,
            'name' => $name
        ];
    }

    public static function firmaDepartmanBazindaSectigiHaklariOnaylamayanlarListesi()
    {
        $sql = "SELECT
        firms.name AS 'Firma',
        departments.name AS 'Departman',
        COUNT(*) AS 'Onaylanmayan Kisi Sayisi'
    FROM
        members
    LEFT JOIN firms ON firms.id = members.firm_id
    LEFT JOIN departments ON departments.id = members.department_id
    WHERE
        is_approved = 0
    GROUP BY
        firms.id, firms.name,
        departments.id, departments.name
    ORDER BY
        firms.id ASC";


        $data = DB::select($sql);
        $columns = ['Firm', 'Department', 'Number of Unapproved People'];
        return ['data' => $data, 'columns' => $columns, 'name' => 'firmaDepartmanBazindaSectigiHaklariOnaylamayanlarListesi'];
    }

    public static function firmaDepartmanBazindaSectigiHaklariOnaylayanlarListesi()
    {
        $sql = "SELECT
                firms.name AS 'Firma',
                departments.name AS 'Departman',
                COUNT(*) AS 'Onaylanan Kisi Sayisi'
            FROM
                members
            LEFT JOIN firms ON firms.id = members.firm_id
            LEFT JOIN departments ON departments.id = members.department_id
            WHERE
                is_approved = 1
            GROUP BY
                firms.id, firms.name,
                departments.id, departments.name
            ORDER BY
                firms.id ASC";

        $data = DB::select($sql);
        $columns = ['Firm', 'Department', 'Number of Approved People'];
        return ['data' => $data, 'columns' => $columns, 'name' => 'firmaDepartmanBazindaSectigiHaklariOnaylayanlarListesi'];
    }

    public static function hakBazindaSecimler($benefitId)
    {
//         $sql = "SELECT
//     members.name,
//     members.email,
//     members.identity_number AS 'TC_Kimlik_No',
//     firms.name AS 'Firma',
//     benefits.name AS 'Hak',
//     benefit_options.name AS 'Secenek',
//     oi_filtered.price AS 'Brut_Tutar',
//     oi_filtered.price * members.percentage / 100 AS 'Net_Tutar'
// FROM
//     orders
// INNER JOIN (
//     SELECT
//         order_items.*
//     FROM
//         order_items
//     INNER JOIN (
//         SELECT
//             order_id,
//             MAX(id) AS max_id
//         FROM
//             order_items
//         WHERE
//             benefit_id = :benefitId
//         GROUP BY
//             order_id
//     ) AS oi_max ON order_items.id = oi_max.max_id
// ) AS oi_filtered ON oi_filtered.order_id = orders.id
// INNER JOIN members ON members.id = orders.member_id
// INNER JOIN benefits ON benefits.id = oi_filtered.benefit_id
// LEFT JOIN firms ON firms.id = members.firm_id
// LEFT JOIN benefit_options ON benefit_options.id = oi_filtered.benefit_option_id
// ";

$sql="SELECT
members.name,
members.email,
members.identity_number AS 'TC_Kimlik_No',
firms.name AS 'Firma',
benefits.name AS 'Hak',
benefit_options.name AS 'Secenek',
oi_filtered.price AS 'Brut_Tutar',
oi_filtered.price * members.percentage / 100 AS 'Net_Tutar'
FROM (
SELECT *,
       ROW_NUMBER() OVER(PARTITION BY member_id ORDER BY date DESC) as rn
FROM orders
) AS o_filtered
INNER JOIN (
SELECT
    oi.*
FROM
    order_items oi
INNER JOIN (
    SELECT
        oi.order_id,
        MAX(oi.id) AS max_id
    FROM
        order_items oi
    INNER JOIN orders o ON oi.order_id = o.id
    WHERE
        oi.benefit_id = :benefitId
    GROUP BY
        oi.order_id
) AS max_oi ON oi.id = max_oi.max_id
) AS oi_filtered ON oi_filtered.order_id = o_filtered.id
INNER JOIN members ON members.id = o_filtered.member_id
INNER JOIN benefits ON benefits.id = oi_filtered.benefit_id
LEFT JOIN firms ON firms.id = members.firm_id
LEFT JOIN benefit_options ON benefit_options.id = oi_filtered.benefit_option_id
WHERE
o_filtered.rn = 1 AND
benefits.id = :benefitId2;
";
$benefitOptional=$benefitId;

        $data = DB::select($sql, ['benefitId' => $benefitId , 'benefitId2' => $benefitOptional]);
        $columns = ['Name', 'Email', 'Identity Number', 'Firm', 'Benefit', 'Option', 'Gross Amount', 'Net Amount'];
        return ['data' => $data, 'columns' => $columns, 'name' => 'hakBazindaSecimler'];
    }

    public static function haklariniOnaylayanKisiListesi()
    {
        $data = DB::table('members')->select('email', 'name', 'identity_number', DB::raw('approval_date'))->where('is_approved', '1')->get()->toArray();

        $data = array_map(function ($item) {
            $item->approval_date = date('Y-m-d H:i:s', $item->approval_date);
            return $item;
        }, $data);

        $columns = ['Email', 'Name', 'Identity Number', 'Approval Date'];
        return ['data' => $data, 'columns' => $columns, 'name' => 'haklariniOnaylayanKisiListesi'];
    }

    public static function hangiHakKacKereSecildi()
    {
        $sql = "SELECT
                benefits.name AS 'Hak',
                benefit_options.`name` AS 'Secenek',
                SUM(IF(order_items.member_family_id = 0, 1, 0)) AS 'Calisan_Secilme_Adedi',
                SUM(IF(order_items.member_family_id = 0, 0, 1)) AS 'Calisan_Aile_Secilme_Adedi',
                SUM(order_items.price) AS 'Brut_Tutar',
                SUM(order_items.price * members.percentage / 100) AS 'Net_Tutar'
            FROM orders
            INNER JOIN order_items ON order_items.order_id = orders.id
            INNER JOIN members ON members.id = orders.member_id
            INNER JOIN benefits ON benefits.id = order_items.benefit_id
            LEFT JOIN benefit_options ON benefit_options.id = order_items.benefit_option_id
            GROUP BY
                order_items.benefit_id, order_items.benefit_option_id,
                benefits.name, benefit_options.`name`";

        $data = DB::select($sql);
        $columns = ['Hak', 'Seçenek', 'Çalışan Seçilme Adedi', 'Çalışan Aile Seçilme Adedi', 'Brüt Tutar', 'Net Tutar'];
        return ['data' => $data, 'columns' => $columns, 'name' => 'hangiHakKacKereSecildi'];
    }

    public static function getOrders()
    {
        $sql = "SELECT
                members.name AS 'Ad',
                members.identity_number AS 'Kimlik No',
                COALESCE(orders.real_balance, 0.0) AS 'Real Balance',
                orders.date AS 'Tarih'
            FROM orders
            INNER JOIN members ON members.id = orders.member_id
            WHERE members.is_approved = 1";

        $data = DB::select($sql);
        $columns = ['Name', 'Identity Number', 'Member Balance', 'Date'];
        return ['data' => $data, 'columns' => $columns, 'name' => 'getOrders'];
    }



}
