<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
class MemberDashboard extends Model
{
    use HasFactory;
    public $timestamps = false;
    protected $guarded = ["id"];
    public static function clearMemberData($member_id)
    {
        DB::transaction(function () use ($member_id) {
            DB::table('member_benefits_cart')->where('member_id', $member_id)->delete();
            DB::table('custom_benefits_sale')->where('member_id', $member_id)->delete();
            DB::table('previous_member_benefits_cart')->where('member_id', $member_id)->delete();
            DB::table('previous_member_benefits_cart_leave')->where('member_id', $member_id)->delete();
            DB::table('custom_balance')->where('member_id', $member_id)->delete();
            DB::table('custom_process_benefits')->where('member_id', $member_id)->delete();
            DB::table('custom_benefits_spending')->where('member_id', $member_id)->delete();
        });
    }
}
