<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

use App\Helpers\CustomSettingsHelper;
use App\Models\CustomSettings;
use Illuminate\Support\Facades\Cache;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

        Cache::macro('member', function ($memberId) {
            return new class($memberId) {
                protected $memberId;

                public function __construct($memberId)
                {
                    $this->memberId = $memberId;
                }

                protected function version(): int
                {
                    return Cache::get("member:{$this->memberId}:version", 1);
                }

                public function remember($key, $ttl, $callback)
                {
                    $v = $this->version();
                    $fullKey = "member:{$this->memberId}:v{$v}:{$key}";
                    return Cache::remember($fullKey, $ttl, $callback);
                }

                public function flush(): void
                {
                    Cache::increment("member:{$this->memberId}:version");
                }
            };
        });
        view()->composer('*', function ($view) {
            $customSettings = new CustomSettingsHelper();
            $view->with('customSettings', $customSettings);
        });
    }
}
