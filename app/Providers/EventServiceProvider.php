<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Models\MemberBenefit;
use App\Models\Member;
use App\Models\BenefitGroup;
use App\Models\Benefit;
use App\Models\BenefitOption;

use App\Observers\MemberBenefitObserver;
use App\Observers\MemberObserver;
use App\Observers\BenefitGroupObserver;
use App\Observers\BenefitObserver;
use App\Observers\BenefitOptionObserver;
class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        'Illuminate\Auth\Events\Login' => [
            'App\Listeners\UpdateLastLoginDate',
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        MemberBenefit::observe(MemberBenefitObserver::class);
        Member::observe(MemberObserver::class);
        BenefitGroup::observe(BenefitGroupObserver::class);
        Benefit::observe(BenefitObserver::class);
        BenefitOption::observe(BenefitOptionObserver::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
