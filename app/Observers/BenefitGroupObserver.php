<?php

namespace App\Observers;

use App\Models\BenefitGroup;
use App\Models\Member;
use Illuminate\Support\Facades\Cache;

class BenefitGroupObserver
{
    protected function flushAllMembers(): void
    {
        Member::pluck('id')->each(fn($id) => Cache::member($id)->flush());
    }

    public function created(BenefitGroup $model): void
    {
        $this->flushAllMembers();
    }

    public function updated(BenefitGroup $model): void
    {
        $this->flushAllMembers();
    }

    public function deleted(BenefitGroup $model): void
    {
        $this->flushAllMembers();
    }

    public function restored(BenefitGroup $model): void
    {
        $this->flushAllMembers();
    }

    public function forceDeleted(BenefitGroup $model): void
    {
        $this->flushAllMembers();
    }
}
