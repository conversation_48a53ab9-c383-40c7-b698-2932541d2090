<?php

namespace App\Observers;

use App\Models\Member;
use Illuminate\Support\Facades\Cache;

class MemberObserver
{
    public function created(Member $model): void
    {
        Cache::member($model->id)->flush();
    }

    public function updated(Member $model): void
    {
        Cache::member($model->id)->flush();
    }

    public function deleted(Member $model): void
    {
        Cache::member($model->id)->flush();
    }

    public function restored(Member $model): void
    {
        Cache::member($model->id)->flush();
    }

    public function forceDeleted(Member $model): void
    {
        Cache::member($model->id)->flush();
    }
}
