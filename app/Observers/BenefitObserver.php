<?php

namespace App\Observers;

use App\Models\Benefit;
use App\Models\Member;
use Illuminate\Support\Facades\Cache;

class BenefitObserver
{
    protected function flushAllMembers(): void
    {
        Member::pluck('id')->each(fn($id) => Cache::member($id)->flush());
    }

    public function created(Benefit $model): void
    {
        $this->flushAllMembers();
    }

    public function updated(Benefit $model): void
    {
        $this->flushAllMembers();
    }

    public function deleted(Benefit $model): void
    {
        $this->flushAllMembers();
    }

    public function restored(Benefit $model): void
    {
        $this->flushAllMembers();
    }

    public function forceDeleted(Benefit $model): void
    {
        $this->flushAllMembers();
    }
}
