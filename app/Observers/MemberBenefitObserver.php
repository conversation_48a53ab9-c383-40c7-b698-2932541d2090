<?php

namespace App\Observers;

use App\Models\MemberBenefit;
use Illuminate\Support\Facades\Cache;

class MemberBenefitObserver
{
    public function created(MemberBenefit $model): void
    {
        Cache::member($model->member_id)->flush();
    }

    public function updated(MemberBenefit $model): void
    {
        Cache::member($model->member_id)->flush();
    }

    public function deleted(MemberBenefit $model): void
    {
        Cache::member($model->member_id)->flush();
    }

    public function restored(MemberBenefit $model): void
    {
        Cache::member($model->member_id)->flush();
    }

    public function forceDeleted(MemberBenefit $model): void
    {
        Cache::member($model->member_id)->flush();
    }
}
