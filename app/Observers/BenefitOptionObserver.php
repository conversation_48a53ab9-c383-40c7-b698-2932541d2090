<?php

namespace App\Observers;

use App\Models\BenefitOption;
use App\Models\Member;
use Illuminate\Support\Facades\Cache;

class BenefitOptionObserver
{
    protected function flushAllMembers(): void
    {
        Member::pluck('id')->each(fn($id) => Cache::member($id)->flush());
    }

    public function created(BenefitOption $model): void
    {
        $this->flushAllMembers();
    }

    public function updated(BenefitOption $model): void
    {
        $this->flushAllMembers();
    }

    public function deleted(BenefitOption $model): void
    {
        $this->flushAllMembers();
    }

    public function restored(BenefitOption $model): void
    {
        $this->flushAllMembers();
    }

    public function forceDeleted(BenefitOption $model): void
    {
        $this->flushAllMembers();
    }
}
