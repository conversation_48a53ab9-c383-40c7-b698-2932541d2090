<?php

namespace App\Helpers;

use App\Models\Benefit;
use App\Models\BenefitOption;
use App\Services\BordroService;


class BordroHelper
{
    public function getOldSelectedPriceNet($member_id, $benefit_id, $option_id = null): float
    {
        $response = app(BordroService::class)->request('yillikozet');
        $yanHakList = data_get($response, 'Data.YanHakSumList', []);
        $hsohList   = data_get($response, 'Data.HSOHSumList', []);
        $allBenefits = array_merge($yanHakList, $hsohList);

        $groupedByCode = collect($allBenefits)
            ->groupBy('CODE')
            ->map(fn($items) => $items->values()->all());

        $code = null;


        // 1. Öncelik: Sepette varsa, oradaki fin_code’u al
        $cartItemQuery = \App\Models\MemberBenefitCart::query()
            ->where('member_id', $member_id)
            ->where('benefit_id', $benefit_id);

        if ($option_id) {
            $cartItemQuery->where('benefit_option_id', $option_id);
        } else {
            $cartItemQuery->whereNull('benefit_option_id');
        }

        $cartItem = $cartItemQuery->first();
        if ($cartItem && $cartItem->benefit_fin_code) {
            $code = $cartItem->benefit_fin_code;
        }

        // 2. Sepette yoksa: MemberBenefit tablosundan al
        if (!$code) {
            $benefitQuery = \App\Models\MemberBenefit::query()
                ->where('member_id', $member_id)
                ->where('benefit_id', $benefit_id);

            if ($option_id) {
                $benefitQuery->where('benefit_option_id', $option_id);
            } else {
                $benefitQuery->whereNull('benefit_option_id');
            }


            $code = $benefitQuery->value('benefit_fin_code');
        }


        // 3. Kod bulunduysa yillikozet datasından karşılaştır
        if ($code && isset($groupedByCode[$code])) {
            return (float) array_sum(array_column($groupedByCode[$code], 'YanHakNet'));
        }

        return 0;
    }
    public function getOldSelectedPriceNetFromCode(string $code): float
    {
        $response = app(\App\Services\BordroService::class)->request('yillikozet');
        $yanHakList = data_get($response, 'Data.YanHakSumList', []);
        $hsohList = data_get($response, 'Data.HSOHSumList', []);
        $allBenefits = array_merge($yanHakList, $hsohList);

        $groupedByCode = collect($allBenefits)->groupBy('CODE')->map(fn($items) => $items->values()->all());

        if ($code && isset($groupedByCode[$code])) {
            return (float) array_sum(array_column($groupedByCode[$code], 'YanHakNet'));
        }



        return 0;
    }

    public function getTotalCurrentSelectedNet(int $member_id): float
    {
        $finalBenefits = app(\App\Models\Benefit::class)->getFinalBenefits($member_id);
        $response = app(\App\Services\BordroService::class)->request('yillikozet');

        $yanHakList = data_get($response, 'Data.YanHakSumList', []);
        $hsohList   = data_get($response, 'Data.HSOHSumList', []);
        $allBenefits = array_merge($yanHakList, $hsohList);

        // CODE’e göre gruplama
        $groupedByCode = collect($allBenefits)
            ->groupBy('CODE')
            ->map(fn($items) => $items->values()->all());

        $total = 0;

        foreach ($finalBenefits as $benefit) {
            $code = $benefit->option_id
                ? \App\Models\BenefitOption::find($benefit->option_id)?->benefit_fin_code
                : $benefit->benefit_fin_code;

            if ($code && isset($groupedByCode[$code])) {
                $netSum = array_sum(array_column($groupedByCode[$code], 'YanHakNet'));
                $total += (float) $netSum;
            }
        }

        return $total;
    }

    public function getOldSelectedPriceBrutTset($member_id, $benefit_id, $benefit_option_id = null)
    {
        $query = \DB::table('member_benefit_balance_ledger')
            ->where('member_id', $member_id)
            ->where('benefit_id', $benefit_id)
            ->where('operation_type', 'purchase');

        if ($benefit_option_id) {
            $query->where('benefit_option_id', $benefit_option_id);
        }

        return floatval($query->orderByDesc('id')->value('price_brut'));
    }

    public function getOldSelectedPriceBrut($code): float
    {
        $response = app(BordroService::class)->request('yillikozet');

        $yanHakList = data_get($response, 'Data.YanHakSumList', []);
        $hsohList   = data_get($response, 'Data.HSOHSumList', []);

        $allBenefits = array_merge($yanHakList, $hsohList);

        $groupedByCode = collect($allBenefits)
            ->groupBy('CODE')
            ->map(fn($items) => $items->values()->all());


        if ($code && isset($groupedByCode[$code])) {

            return (float) array_sum(array_column($groupedByCode[$code], 'YanHakBurut'));
        }

        return 0;
    }

    public static function parseTlToFloat(string $value): float
    {
        $clean = str_replace([' TL', 'TL', ' '], '', $value);

        if (preg_match('/^\d{1,3}(,\d{3})*(\.\d+)?$/', $clean)) {
            $clean = str_replace(',', '', $clean);
            return floatval($clean);
        }

        if (preg_match('/^\d{1,3}(\.\d{3})*(,\d+)?$/', $clean)) {
            $clean = str_replace('.', '', $clean);
            $clean = str_replace(',', '.', $clean);
            return floatval($clean);
        }

        return floatval(str_replace(',', '.', $clean));
    }
}

