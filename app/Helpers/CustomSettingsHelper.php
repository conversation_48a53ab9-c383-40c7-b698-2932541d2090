<?php

namespace App\Helpers;

use App\Models\CustomSettings;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
class CustomSettingsHelper
{
    protected $settings;
    protected $availableLocales;

    public function __construct()
    {

        $this->settings = Cache::remember('custom_settings_all', 3600, function () {
            return CustomSettings::all()->keyBy('settings_key')->toArray();
        });
        $this->availableLocales = $this->getAvailableLocales();
    }

    protected function getAvailableLocales()
    {
        $siteLanguageSetting = $this->settings['site_language']['settings_value'] ?? 'tr';

        return explode(',', $siteLanguageSetting);
    }


    public function getText($key, $default = null)
    {
        $locale = App::getLocale();
        $column = $locale == 'en' ? 'settings_value_en' : 'settings_value';

        return $this->settings[$key][$column] ?? $default;
    }

    public function getTextLanguage($key, $default = null)
    {
        $locale = App::getLocale();
        $column = $locale == 'en' ? 'settings_value' : 'settings_value';

        return $this->settings[$key][$column] ?? $default;
    }

    public function getFile($key, $default = null)
    {
        $locale = App::getLocale();
        $column = $locale == 'en' ? 'settings_file_en' : 'settings_file';



        return $this->settings[$key][$column] ?? $default;
    }

    public function all()
    {
        return $this->settings;
    }
}
