@php
    $c_email_to=$customSettings->getText('contact_email_to');
    $c_email_cc=$customSettings->getText('contact_email_cc');
@endphp
</head>
    <header
      class="shadow-[0px_0px_10px_#00000021] h-[90px] flex justify-center items-center relative z-10 px-2"
    >
      <nav class="flex justify-between items-center container m-auto px-3">
        <button class="md:hidden cursor-pointer" id="hamburger_menu">
          <div class="h-[4px] bg-[#7F35B2] rounded-[3px] w-[27px]"></div>
          <div class="h-[4px] bg-[#7F35B2] rounded-[3px] w-[27px] mt-1"></div>
        </button>
        <div class="flex items-center">
          <img

            src="{{ asset('storage/' . (App::getLocale() == 'en' ? $customSettings->getFile('logo') : $customSettings->getFile('logo'))) }}"
            alt="logo"
            class="w-[104px] md:w-[143px]"
          />
          <div class="pl-4 md:pl-10 md:flex gap-3 text-[#7F35B2] hidden">
            <div>
              <a href="{{ route('member_dashboard') }}" class="{{ Route::currentRouteName() == 'member_dashboard' ? 'font-bold underline underline-offset-4 decoration-2' : '' }}">{{ __('contents.menuHome') }}</a>
              <!-- <div class="bg-[#7F35B2] py-[1px] max-w-[50px]"></div> -->
            </div>
            <a href="{{ route('help') }}" class="{{ Route::currentRouteName() == 'help' ? 'font-bold underline underline-offset-4 decoration-2' : '' }}">{{ __('contents.menuHelp') }}</a>
              <a href="mailto:{{ $c_email_to }}?cc={{ $c_email_cc }}">{{ __('contents.menuContact') }}</a>
              <a href="{{ route('member_logout') }}">{{ __('contents.logoutText') }}</a>
          </div>
        </div>

        <div class="lg:flex gap-3 items-center justify-center hidden">
          <div class="text-center">
            <p class="text-[#7F35B2]">{{ __('contents.welcome') }}!</p>
            <p class="font-bold text-[#7F35B2]">
            @if(Auth::guard('member')->check())
              {{ Auth::guard('member')->user()->name }}!
            @endif
            </p>
          </div>
          <!-- <img
            src="{{ asset('assets/images/random_user.png') }}"
            alt="user"
            class="w-[50px] h-[50px] rounded-full border-2 border-[#7F35B2]"
          /> -->
        </div>
        <button
        class="lg:hidden"
        type="button"
        id="cart_icon_btn"
      >
      <img src="{{ asset('assets/images/cart.png') }}" alt="cart"  />
      </button>

      </nav>
      <nav
        id="mobile_navbar"
        class="scale-0 bg-[#7F35B2]/[0.9] fixed top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] w-full h-full py-8 px-6 md:hidden z-50 transition-all duration-500 ease-out overflow-y-scroll"
      >
        <img
          src="{{ asset('assets/images/white_close_btn.png') }}"
          alt="close_btn"
          id="mobile_close_btn"
          class="cursor-pointer"
        />
        <ul class="pt-12 container">
          <li
            class="text-white border-b border-white py-4 mt-1 text-[20px] font-bold" id="home-link"
          >
            <a href="{{ route('member_dashboard') }}">{{ __('contents.menuHome') }}</a>
          </li>
          <li
            class="text-white border-b border-white py-4 mt-1 text-[20px] font-bold"
          >

            <a href="{{ route('help') }}" class="font-bold">{{ __('contents.menuHelp') }}</a>
          </li>
          <li
            class="text-white border-b border-white py-4 mt-1 text-[20px] font-bold"
          >
              <a href="mailto:{{ env('CONTACT_EMAIL_TO') }}?cc={{ env('CONTACT_EMAIL_CC') }}">{{ __('contents.menuContact') }}</a>
          </li>
          <li
            class="text-white border-b border-white py-4 mt-1 text-[20px] font-bold"
          >
            <a href="{{ route('member_logout') }}">{{ __('contents.logoutText') }}</a>
          </li>
        </ul>
      </nav>
    </header>
