@php
    extract($data);

@endphp
@extends('theme.layout')
@section('title')
    {{ __('contents.menuHome') }}
@endsection
@section('content')
    <main
        class="main_login relative bg-[#FBFAFC] flex justify-center items-center pb-[5rem] sm:py-[5rem] py-6 px-4 sm:px-[2rem]">
        <div class="w-full max-w-[582px] shadow-[0px_0px_20px_#00000029] rounded-[10px]">
            <p
                class="flex flex-col justify-between md:flex-row items-start md:items-center font-semibold py-4 border-b border-slate-300/[0.3] px-2 sm:px-5">
                <span class="text-[#7F35B2] text-[22px] font-bold"> {{ __('contents.yan_haklar_onay_baslik') }}</span>
                <span class="text-[18px] text-black"></span>
            </p>


            @foreach ($groupedBenefits as $key => $benefit)
                @php
                    $brutTotal = $totalPricesByGroup[$key]['brut'] ?? 0;
              $netTotal  = $totalPricesByGroup[$key]['net']  ?? 0;

              $tt_group_brut = explode(',', number_format($brutTotal, 2, ',', '.'));
              $tt_group_net  = explode(',', number_format($netTotal, 2, ',', '.'));
                @endphp

                <div class="px-2 sm:px-5 {{ $loop->iteration % 2 == 0 ? 'bg-[#48106D]/[0.04]' : '' }}">
                    <h1 class="text-[#7F35B2] font-semibold pt-3 flex justify-between items-start">
                        <span
                            class="text-[13px] md:text-[16px] w-full max-w-[219px] sm:max-w-[415px]">{{ App::getLocale() == 'en' ? $benefit[0]->group_name_en : $benefit[0]->group_name }}</span>
                            <div class="flex flex-col whitespace-nowrap">
                        <span class="text-[13px] md:text-[16px]">   <span class="inline-block w-[35px]"> Brüt: </span> {{ $tt_group_brut[0] }},{{ $tt_group_brut[1] }} TL</span>
                        <span class="text-[13px] md:text-[16px]">    <span class="inline-block w-[35px]"> Net: </span> {{ $tt_group_net[0] }},{{ $tt_group_net[1] }} TL</span>
                        </div>
                    </h1>

                    @foreach ($benefit as $item)


                        <div class="flex justify-start items-center gap-3 border-b border-slate-300/[0.3] pt-3 pb-5">
                            <img src="{{ asset('storage/' . (App::getLocale() == 'en' ? $item->image : $item->image)) }}"
                                alt="car" class="w-[46px] h-[46px] object-contain" />
                            <div>
                                <p class="text-[#000] font-semibold text-[15px] md:text-[16px]">
                                    {{ App::getLocale() == 'en'
                                         ? ($item->name_en . ($item->option_name_en ? ' - ' . $item->option_name_en : ''))
                                         : ($item->name . ($item->option_name ? ' - ' . $item->option_name : ''))
                                     }}
                                </p>
                                <p class="text-[14px] text-[#5F5F5F]">
                                    <span class="font-bold text-black">Net: {{ round($item->priceNet) }}
                                        TL</span>
                                    - {{ __('contents.brut') }}:
                                    {{ $item->priceBrut }} TL
                                </p>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endforeach

            <div class="px-2 sm:px-5 mt-5" id="beyanname_div">
                <button id="taahutnameBtn" class="text-[#EFF1F3] bg-[#7F35B2] rounded-md w-full h-[50px]"
                    data-user-id="{{ $user_info['id'] }}">
                    {{ __('contents.yan_haklar_beyanname') }}
                </button>
                @if (App::getLocale() == 'en')
                    <p class="text-[14px] font-semibold max-w-[457px] py-4">
                        Your rights were approved and entered into the system
                        at {{ \Carbon\Carbon::createFromTimestamp($user_info['approval_date'])->format('H:i') }}
                        on {{ \Carbon\Carbon::createFromTimestamp($user_info['approval_date'])->format('d/m/Y') }}.
                    </p>
                @else
                    <p class="text-[14px] font-semibold max-w-[457px] py-4">
                        Hakların {{ \Carbon\Carbon::createFromTimestamp($user_info['approval_date'])->format('d/m/Y') }}
                        tarihinde
                        saat {{ \Carbon\Carbon::createFromTimestamp($user_info['approval_date'])->format('H:i') }}'de
                        onaylanarak sisteme işlenmiştir.
                    </p>
                @endif
            </div>
        </div>

        <div id="survey_modal"
            class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 hidden justify-center py-[5rem] px-2 modal_parent_div z-[1000] overflow-y-scroll">
            <div
                class="w-full h-fit  max-w-[721px]  bg-[#fff] shadow-[0px_0px_20px_#00000017] relative rounded-[10px] p-[10px] sm:p-[2.5rem]">
                <!-- <img src="{{ asset('assets/images/close.png') }}"
                class="absolute top-[18px] sm:top-[2.5rem] right-[10px] sm:right-[2.5rem] cursor-pointer w-[13px] sm:w-auto" alt="close" id="survey_close_btn"/>  -->
                <form action="{{ route('approval_question') }}" method="post" id="survey" class=""
                    autocomplete="off">
                    @csrf
                    <h3 class="font-bold text-[16px] sm:text-[26px] text-[#7F35B2]">
                        {{ __('contents.anket_title') }}
                    </h3>

                    @foreach ($questions as $q)
                        <div class="anket12">

                            @if (isset($options[$q->id]))
                                <label for="question_answers[{{ $q->id }}]"
                                    class="block mb-2 font-bold text-[14px] sm:text-[18px] text-[#7F35B2] mt-3 sm:mt-5">
                                    @if (App::getLocale() == 'en')
                                        {{ $loop->iteration }}-{{ $q->text_en }}
                                    @else
                                        {{ $loop->iteration }}-{{ $q->text }}
                                    @endif
                                </label>
                                <select id="question_answers[{{ $q->id }}]"
                                    name="question_answers[{{ $q->id }}]"
                                    class="bg-white border border-[#7F35B2] text-[#7F35B2] text-sm rounded-[5px] focus:ring-[#7F35B2] focus:border-[#7F35B2] outline-[#7F35B2] block w-full max-w-[323px] p-2.5"
                                    required>
                                    @foreach ($options[$q->id] as $o)
                                        <option value="{{ $o }}">{{ $o }}</option>
                                    @endforeach
                                </select>
                            @else
                                <label for="question_answers[{{ $q->id }}]"
                                    class="font-bold block sm:max-w-[490px] pb-3 text-[14px] sm:text-[18px] text-[#7F35B2] mt-3 sm:mt-5">
                                    @if (App::getLocale() == 'en')
                                        {{ $loop->iteration }}-{{ $q->text_en }}
                                    @else
                                        {{ $loop->iteration }}-{{ $q->text }}
                                    @endif
                                </label>

                                <textarea id="question_answers[{{ $q->id }}]" name="question_answers[{{ $q->id }}]"
                                    class="w-full resize-none border border-[#7F35B2] outline-[#7F35B2] h-[70px] sm:h-[108px]" required></textarea>
                            @endif

                        </div>
                    @endforeach
                    <div class="flex sm:flex-row flex-col justify-between items-center sm:justify-end">
                        <!-- <button  class="bg-[#7F35B2] text-white flex justify-center items-center rounded-sm py-2 w-full sm:max-w-[230px] mt-4 mb-3 sm:mb-0" id="atla_btn">{{ __('contents.anket_atla') }}</button> -->
                        <button type="submit"
                            class="bg-[#7F35B2] text-white flex justify-center items-center rounded-sm py-2 w-full sm:max-w-[230px] mt-4 mb-3 sm:mb-0">{{ __('contents.anket_gonder') }}</button>
                    </div>
                </form>
            </div>
        </div>

        <div id="survey_modal_answer"
            class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 hidden justify-center items-center  modal_parent_div z-[1000] p-4">
            <div
                class="flex flex-col items-center justify-center md:flex-row  md:justify-start max-w-[380px] md:max-w-[756px]  p-5 bg-white shadow-[0px_0px_20px_#00000017;] relative rounded-[10px]">
                <img src="{{ asset('assets/images/close.png') }}" id="answerClose"
                    class="absolute top-3 right-3 cursor-pointer  close">
                <img src="{{ asset('assets/images/160x160px-1.png') }}" alt="thumbsUp"
                    class="w-full max-w-[90px] sm:max-w-[160px]">
                <div class="md:px-[1rem] lg:py-[1rem] px-1">
                    <h3 class="text-[20px] md:text-[26px] font-semibold text-[#7F35B2] pt-2 max-w-[305px] md:max-w-[400px] text-center md:text-left"
                        id="thankyou_title">
                        {{ __('contents.anket_baslik') }}
                    </h3>

                    <p class="text-[#2F2C31] max-w-[324px] md:max-w-[503px]  py-4 text-[14px] md:text-[16px] text-center md:text-left"
                        id="thankyou_text">

                    </p>
                </div>
            </div>
        </div>
    </main>
@endsection

<style>
    #loadingSpinner {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5); /* Yarı şeffaf siyah arka plan */
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .spinner {
        border: 8px solid #f3f3f3; /* Açık gri */
        border-top: 8px solid #3498db; /* Mavi */
        border-radius: 50%;
        width: 60px;
        height: 60px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

</style>

<div id="loadingSpinner" style="display: none;">
    <div class="spinner"></div>
</div>

<!-- <script src="https://code.jquery.com/jquery-3.7.1.js" integrity="sha256-eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="
    crossorigin="anonymous"></script> -->
<script src="{{ asset('assets/js/jquery.js') }}"></script>
@php
    $qA = [];
    foreach ($questions as $key => $q) {
        $qA['data'] = $q->id;
    }

@endphp

@if (!empty($qA['data']) && $user_info['is_approved'])
    <script>
        $(document).ready(function() {
            $('#survey_modal').addClass("flex").removeClass("hidden");
            $('body').addClass("overflow-hidden")
        });
    </script>
@endif



<!-- <script>
    $(document).ready(function() {
        $('#survey').on('submit', function(e) {
            e.preventDefault();

            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                data: $(this).serialize(),
                success: function(response) {
                    console.log(response);
                    $('#survey_modal').hide();
                    $('#survey_modal_answer').show();

                    // $('#thankyou_text').text( "{{ __('contents.anket_metin') }}");
                    // $('#thankyou_title').text( "{{ __('contents.anket_baslik') }}");
                },
                error: function(xhr) {
                    alert('Hata oluştu!');
                }
            });
        });

        $('#answerClose').on('click', function() {
            $('#survey_modal_answer').hide();
            $('body').removeClass("overflow-hidden")
        });
        $('#survey_close_btn').on('click', function() {
            $('#survey_modal').hide();
            $('body').removeClass("overflow-hidden")
        });
        $('#atla_btn').on('click', function() {
            $('#survey_modal').hide();
            $('body').removeClass("overflow-hidden")
        });

        function getCurrentDateFormatted() {
            const now = new Date();
            let day = now.getDate().toString();
            let month = (now.getMonth() + 1).toString();
            const year = now.getFullYear();

            day = day.length < 2 ? '0' + day : day;
            month = month.length < 2 ? '0' + month : month;

            return `${day}.${month}.${year}`;
        }


        $(document).on('click', '#taahutnameBtn', function(e) {
            e.preventDefault();

            var member_id = "{{ $user_info['id'] }}";

            const currentDate = getCurrentDateFormatted();
            const start_date = currentDate;
            const end_date = currentDate;



            var query = $.param({
                member_id: member_id,
                start_date: start_date,
                end_date: end_date
            });

            window.location.href = '/get-export-pdf?' + query;

        });

        // $("#taahutnameBtn").on("click",function(){
        //     $("#home-link").attr("href","#")
        // })


    });
</script> -->
