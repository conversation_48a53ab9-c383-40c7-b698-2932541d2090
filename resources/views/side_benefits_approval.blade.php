@php
$values = array_map(function ($value) {
return $value < 0 ? -$value : $value; }, $values);
session(['not_window_on_load' => true]);
@endphp

@extends('theme.layout') @section('title') {{ __('contents.menuHome') }} @endsection @section('content')





<main class="main_login bg-[#FBFAFC] flex justify-center items-center pb-[5rem] sm:py-[5rem] py-6 px-4 sm:px-[2rem] side_benefit_approval">
    <div class="w-full max-w-[582px] shadow-[0px_0px_20px_#00000029]  rounded-[10px]">

        <p class=" flex flex-col justify-between md:flex-row items-start md:items-center font-semibold py-4 border-b border-slate-300/[0.3] px-2 sm:px-5">
            <span class="text-[#7F35B2] text-[22px] font-bold">{{ __('contents.yan_haklar_onay_baslik') }}</span>
            <span class="text-[18px] text-black"> </span>
        </p>


        @foreach ($groupedBenefits as $key => $benefit)
            @php
                $brutTotal = $totalPricesByGroup[$key]['brut'] ?? 0;
                $netTotal  = $totalPricesByGroup[$key]['net']  ?? 0;

                $tt_group_brut = explode(',', number_format($brutTotal, 2, ',', '.'));
                $tt_group_net  = explode(',', number_format($netTotal, 2, ',', '.'));
            @endphp

            <div class="px-2 sm:px-5 {{ $loop->iteration % 2 == 0 ? 'bg-[#48106D]/[0.04]' : '' }}">
                <h1 class="text-[#7F35B2] font-semibold pt-3 flex justify-between items-start">
            <span class="text-[13px] md:text-[16px] w-full max-w-[219px] sm:max-w-[415px]">
                {{ App::getLocale() == 'en' ? $benefit[0]->group_name_en : $benefit[0]->group_name }}
            </span>
                   <div class="flex flex-col whitespace-nowrap">
                   <span class="text-[13px] md:text-[16px]">
                        <span class="inline-block w-[35px]"> Brüt: </span>
                {{ $tt_group_brut[0] }},{{ $tt_group_brut[1] }} TL
            </span>
                    <span class="text-[13px] md:text-[16px]">
                    <span class="inline-block w-[35px]"> Net: </span>
                {{ $tt_group_net[0] }},{{ $tt_group_net[1] }} TL
            </span>
                   </div>


                </h1>


                @foreach ($benefit as $item)
                    @php
                        $tt_net = number_format($item->priceNet, 2, ',', '.');
                        $tt_brut = number_format($item->priceBrut, 2, ',', '.');

                        [$tt_net_int, $tt_net_dec] = explode(',', $tt_net);
                        [$tt_brut_int, $tt_brut_dec] = explode(',', $tt_brut);
                    @endphp

                    <div class="flex justify-start items-center gap-3 border-b border-slate-300/[0.3] pt-3 pb-5">
                        <img src="{{ asset('storage/' . $item->image) }}" class="w-[46px] h-[46px] object-contain" />
                        <div>
                            <p class="text-[#000] font-semibold text-[15px] md:text-[16px]">
                                {{ App::getLocale() == 'en'
                                    ? ($item->name_en . ($item->option_name_en ? ' - ' . $item->option_name_en : ''))
                                    : ($item->name . ($item->option_name ? ' - ' . $item->option_name : '')) }}
                            </p>
                            <p class="text-[14px] text-[#5F5F5F]">
                        <span class="font-bold text-black">
                            Net: {{ $tt_net_int }},{{ $tt_net_dec }} TL
                        </span>
                                - {{ __('contents.brut') }}:
                                {{ $tt_brut_int }},{{ $tt_brut_dec }} TL
                            </p>
                        </div>
                    </div>
                @endforeach
            </div>
        @endforeach
        @if($user_info['is_approved'] != 1)
            <div class="py-3 flex justify-start items-start px-2 sm:px-5 " id="okudum_anladim_link">
                <input type="radio" id="okudum-anladim" required form="okudum-anladim" class="cursor-pointer accent-[#7F35B2]">
                <label for="okudum-anladim"></label>

                @foreach($static_pages as $key => $static_page)

                @if (App::getLocale() == 'en')
                    <p class="text-[14px] pl-2 leading-[17px] mt-[-2px]"> I have read and accept the<span id="sozlesme_{{ $static_page['id'] }}" class="underline text-[#1363E3] cursor-pointer">  {{  $static_page['name_en']}}</span></p>
                    @else
                    <p class="text-[14px] pl-2 leading-[17px] mt-[-2px]"><span id="sozlesme_{{ $static_page['id'] }}" class="underline text-[#1363E3] cursor-pointer">{{  $static_page['name'] }}</span>'ni okudum, kabul ediyorum </p>
                    @endif
                @endforeach

            </div>
            <div class="flex flex-col sm:flex-row justify-between items-center py-5 px-2 sm:px-5 gap-3 sm:gap-0" id="onayla_bitir_div">
                <button data-modal-target="staticModalOnayla" data-modal-toggle="staticModalOnayla" class="w-full sm:max-w-[249px] h-[30px] text-[#EFF1F3] bg-[#7F35B2] rounded-md " type="button" id="approval_button">
                    <span class="text-[#EFF1F3] ">{{ __('contents.yan_haklar_onayla_ve_bitir') }}</span>
                </button>
                <button data-modal-target="userAgreementModal" data-modal-toggle="userAgreementModal" class="w-full sm:max-w-[249px] h-[30px] text-[#EFF1F3] bg-[#7F35B2] rounded-md " type="button" id="user_agreement_button">
                    <span class="text-[#EFF1F3] ">{{ __('contents.yan_haklar_onayla_ve_bitir') }}</span>
                </button>
                <a href="{{ route('member_dashboard') }}" class="flex items-center justify-center w-full sm:max-w-[249px] h-[30px] border border-[#7F35B2] rounded-md">
                    <span class="text-[#7F35B2]">{{ __('contents.yan_haklar_onay_geri') }}</span>
                    </button>
                </a>
            </div>

            @if(session('can_be_negative')==1)
                <div class="px-2 sm:px-5 py-4" id="beyanname_div">
                    @if(App::getLocale() === 'en')
                        <p class="text-[14px] text-[#7F35B2] rounded-[10px] p-3 bg-[#48106D]/[0.1]">Based on your selection, in addition to the standard amount deducted for Health Insurance, an amount of <b>{{ session('negative_member_balance') }}</b> TL will be collected from your monthly fee in 5 monthly installments.</p>
                    @else
                    <p class="text-[14px]  text-[#7F35B2] rounded-[10px] p-3 bg-[#48106D]/[0.1]">Yapmış olduğun seçim doğrultusunda, Sağlık Sigortasında standart kesilen rakamınıza ek olarak <b>{{ session('negative_member_balance') }}</b> TL bedel aylık ücretinizden 5 ay taksit ile tahsil edilecektir.</p>
                    @endif
                </div>
            @endif

        @endif
    </div>

    <!-- -------------------------------MODAL TAMAMLA------------------------------------------->
    <div id="staticModalOnayla" data-modal-backdrop="static" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative w-full max-w-[683px] max-h-full">
            <!-- Modal content -->
            <div class="relative rounded-lg shadow-[0px_0px_20px_#00000017] bg-white p-4">
                <!-- Modal header -->
                <div class="flex items-start justify-between   rounded-t ">

                    <button type="button" class="bg-transparent rounded-lg  ml-auto inline-flex justify-center items-center " data-modal-hide="staticModalOnayla">
                        <img src="{{ asset('assets/images/close.png') }}" alt="close"/>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div class=" flex flex-col md:flex-row items-center justify-center md:justify-start md:items-start gap-5">
                    <img src="{{ asset('assets/images/144x144px-5.png') }}" alt="lightening" class="w-[80px] md:w-[144px]">
                    <div>
                        <h1 class="text-[#7F35B2] text-[18px] md:text-[26px] font-semibold leading-[26px] text-center md:text-left">
                            {{ __('contents.alert') }}
                        </h1>
                        @if (App::getLocale() == 'en')
                            <p class="text-[#2F2C31] py-3 text-[12px] md:text-[16px] text-center md:text-left">
                                <!-- {{ __('contents.yan_haklar_onay_popup_metin') }} -->
                                We would like to remind you to print the <span class="font-bold text-[#7F35B2]">"Declaration & Undertaking"</span> document in the next step and submit it by signing with the phrase <span class="font-bold text-[#7F35B2]">"I have read, understood, and approve"</span> written on it to the Human Resources office by <span class="font-bold text-[#7F35B2]">December 24, 2024</span>.
                            </p>
                        @else
                        <p class="text-[#2F2C31] py-3 text-[12px] md:text-[16px] text-center md:text-left">
                            <!-- {{ __('contents.yan_haklar_onay_popup_metin') }} -->
                            Bir sonraki adımda yer alan <span class="font-bold text-[#7F35B2]">“Beyanname & Taahhütname” </span> dökümanının çıktısını almanı ve <span class="font-bold text-[#7F35B2]">13 Ocak 2025</span> Tarihine kadar
                             <span class="font-bold text-[#7F35B2]">“Okudum, Anladım ve Onaylıyorum”</span> yazıp imzalayarak İnsan Kaynakları ofisimize teslim etmen gerektiğini hatırlatmak isteriz.
                        </p>
                        @endif
                        <div class="flex items-center justify-center md:justify-start space-x-2 rounded-b ">
                                <a id="beyanneme" data-modal-hide="staticModalOnayla" type="button" class="text-[#EFF1F3] bg-[#7F35B2] focus:outline-none  font-medium rounded-lg text-sm text-center w-full max-w-[121px] h-[30px] flex items-center justify-center cursor-pointer">
                            {{ __('contents.yes') }}</a>

                            <button data-modal-hide="staticModalOnayla" type="button" class="focus:outline-none  rounded-lg text-sm font-medium w-full max-w-[121px] h-[30px] border border-[#7F35B2] text-[#7F35B2]">{{ __('contents.cancel') }}</button>
                        </div>
                    </div>
                </div>
                <!-- Modal footer -->
            </div>
        </div>
    </div>
    <div id="userAgreementModal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative w-full max-w-[400px] md:max-w-[683px] max-h-full">
            <!-- Modal content -->
            <div class="relative rounded-lg shadow-[0px_0px_20px_#00000017] bg-white p-4">
                <!-- Modal header -->
                <div class="flex items-start justify-between   rounded-t ">

                    <button type="button" class="bg-transparent rounded-lg  ml-auto inline-flex justify-center items-center " data-modal-hide="userAgreementModal">
                        <img src="{{ asset('assets/images/close.png') }}" alt="close"/>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div class=" flex flex-col md:flex-row items-center justify-center md:justify-start  gap-5">
                    <img src="{{ asset('assets/images/144x144px-5.png') }}" alt="lightening" class="w-[80px] md:w-[144px]">
                    <div>
                        <!-- <h1 class="text-[#7F35B2] text-[18px] md:text-[26px] font-semibold leading-[26px] text-center md:text-left">
                            {{ __('contents.alert') }}
                        </h1> -->

                        <p class="text-[#2F2C31] py-3 text-[12px] md:text-[16px] text-center md:text-left md:pl-10">
                            {{ __('contents.yan_haklar_alert_message') }}
                        </p>
                    </div>
                </div>
                <!-- Modal footer -->
            </div>
        </div>
    </div>
    <!-- -------------------------------MODAL TAMAMLA------------------------------------------->
    @foreach($static_pages as $key => $static_page)
        <div id="kullanici_overlay_{{$static_page['id']}}" class="bg-[rgba(0,0,0,0.5)] w-full fixed left-0 top-0 h-[100vh] hidden justify-center items-center z-[10000] px-6 py-[5rem] overflow-y-scroll">
            <div class="w-full max-w-[630px] m-auto bg-white shadow-[0px_0px_10px_#00000017] p-2 rounded-xl relative">
                <img id="kullaniciClose_{{$static_page['id']}}" src="{{ asset('assets/images/close.png') }}" class="absolute top-[15px] right-[15px] cursor-pointer w-[13px] sm:w-auto" />
                <h3 class="text-center font-bold py-4 text-[#7F35B2]">{{ App::getLocale() == 'en' ? $static_page['title_en'] : $static_page['title']}}</h3>
                <div class="kvvk_popup text-justify p-2">
                    {!! App::getLocale() == 'en' ? strip_tags($static_page['content_en'], '<br><strong><em>') : strip_tags($static_page['content'], '<br><strong><em>')!!}
                </div>
            </div>
        </div>
    @endforeach


    <!-- ---------------------------------------------------------- -->

    <input type="hidden" id="member_balance" value="{{ $memberBalance['balance'] }}" />
    <input type="hidden" id="member_balance_brut" value="{{ $memberBalance['brut_balance'] }}" />

    </main>
    @endsection
    <script src="{{ asset('assets/js/jquery.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('#beyanneme').on('click', function() {

                var approval = "{{ route('approve') }}";
                var csrfToken = '{{ csrf_token() }}';

                var balance = parseFloat($('#member_balance').val());


                $.ajax({
                    url: approval,
                    type: 'POST',
                    data: {
                        _token: csrfToken,
                        balance: balance
                    },
                    success: function(response) {
                        if (response.success) {
                            window.location.href = "/approval";
                        }

                    },
                    error: function(xhr, status, error) {


                    }
                });

            });
               });
            $(document).ready(function() {

                @foreach($static_pages as $key  => $static_page)
                    $('#sozlesme_{{$static_page["id"]}}').click(function() {
                        $('#kullanici_overlay_{{$static_page["id"]}}').removeClass('hidden').addClass("flex");
                        $('body').addClass("overflow-hidden")
                    });
                    $('#kullaniciClose_{{$static_page["id"]}}').click(function() {
                        $('#kullanici_overlay_{{$static_page["id"]}}').removeClass('flex').addClass("hidden");
                        $('body').removeClass("overflow-hidden")
                    });
                @endforeach

            });

    </script>
