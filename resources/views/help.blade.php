@extends('theme.layout')
@section('title')
{{ __('contents.menuHelp') }}
@endsection
@section('content')

<main class="main_login bg-[#FBFAFC] flex justify-center items-center pb-[5rem] sm:py-[5rem] sm:px-[2rem]">
    <section class="w-full max-w-[680px] shadow-[0px_0px_10px_#00000010]">
        @php $i=0; @endphp
        @foreach($helpers as $help)
        @if($i <= 0) <div class="pb-7">
            <div class="flex items-start justify-start px-3 sm:px-6 pt-7 bg-white gap-3 sm:gap-7">
                <img src="{{ asset('storage/' . (App::getLocale() == 'en' ? $help->image_en : $help->image)) }}" alt="people" class="max-w-[85px] sm:max-w-[130px]" />
                <div>
                    <p class="text-[#7F35B2] font-bold text-[15px]">
                        {{ App::getLocale() == 'en' ? $help->name_en : $help->name }}
                    </p>
                    <p class="text-[#2F2C31] text-[14px] sm:max-w-[580px] py-2 help-desc">
                        {!! App::getLocale() == 'en' ? ($help->description_en) : ($help->description) !!}

                    </p>
                    @if(!empty($help->link))
                    <div class="sm:flex justify-center sm:justify-start items-center mt-2 hidden">
                        <a target="_blank" href="{{ App::getLocale() == 'en' ? $help->link_en : $help->link }}" class="bg-[#7F35B2] flex justify-center text-[#EFF1F3] w-full max-w-[372px] py-2 rounded-[5px]">
                            {{ __('contents.kullanimKilavuzu') }}
                        </a>
                    </div>
                    @endif
                </div>
            </div>
            @if(!empty($help->link))
            <div class="flex justify-center sm:justify-start items-center mt-2 sm:hidden px-3">
                <a target="_blank" href="{{ App::getLocale() == 'en' ? $help->link_en : $help->link }}" class="bg-[#7F35B2] flex justify-center text-[#EFF1F3] w-full py-2 rounded-[5px] text-[13px]">
                    {{ __('contents.kullanimKilavuzu') }}
                </a>
            </div>
            </div>
            @endif
            @else
            <div class="flex items-start justify-start px-3 sm:px-6 py-7 @if($i % 2 == 0) bg-white @else bg-[#F6F4F8] @endif gap-3 sm:gap-7">
                <img src="{{ asset('storage/' . $help->image) }}" alt="target" class="max-w-[85px] sm:max-w-[130px]" />
                <div>
                    <p class="text-[#7F35B2] font-bold text-[15px]">
                        {{ App::getLocale() == 'en' ? $help->name_en : $help->name }}
                    </p>
                    <p class="text-[#2F2C31] text-[14px] sm:max-w-[580px] py-2 help-desc">
                    {!! App::getLocale() == 'en' ? ($help->description_en) : ($help->description) !!}
                    </p>
                </div>
            </div>
            @endif
            @php $i++ @endphp
            @endforeach


            {{-- <div class="flex items-start justify-start px-3 sm:px-6 py-7 bg-white gap-3 sm:gap-7">
                <img src="{{ asset('assets/images/caution.png') }}" alt="caution" class="max-w-[85px] sm:max-w-[130px]" />
            <div>
                <p class="text-[#7F35B2] font-bold text-[15px]">
                    {{ __('contents.unutma_baslik') }}
                </p>
                <p class="text-[#2F2C31] text-[13px] sm:max-w-[580px] py-2">
                    {{ __('contents.unutma_metni') }}
                </p>
            </div>
            </div>
            <div class="flex items-start justify-start px-3 sm:px-6 py-7 bg-[#F6F4F8] gap-3 sm:gap-7">
                <img src="{{ asset('assets/images/two_way_arrows.png') }}" alt="budget" class="max-w-[85px] sm:max-w-[130px]" />
                <div>
                    <p class="text-[#7F35B2] font-bold text-[15px]">
                        {{ __('contents.brut_tutar_baslik') }}
                    </p>
                    <p class="text-[#2F2C31] text-[13px] sm:max-w-[580px] py-2">
                        {{ __('contents.brut_tutar_metin') }}
                    </p>
                </div>
            </div>
            <div class="flex items-start justify-start px-3 sm:px-6 py-7 bg-white gap-3 sm:gap-7">
                <img src="{{ asset('assets/images/arrows_up.png') }}" alt="arrows_up" class="max-w-[85px] sm:max-w-[130px]" />
                <div>
                    <p class="text-[#7F35B2] font-bold text-[15px]">
                        {{ __('contents.sepetiSıfırla') }}
                    </p>
                    <p class="text-[#2F2C31] text-[13px] sm:max-w-[580px] py-2">
                        {{ __('contents.sepetiSıfırla_metin') }}
                    </p>
                </div>
            </div> --}}
    </section>
</main>
@endsection
