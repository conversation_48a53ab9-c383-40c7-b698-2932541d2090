@php
    $rules = $customSettings->getText('rules');
@endphp

<section class="bg-white w-full max-w-[310px] rounded-[10px] shadow-[0px_0px_20px_#00000029] fixed top-[6rem] lg:top-[1rem] right-[-100%] z-[9] pb-1 lg:sticky transition-all duration-500 ease-linear" id="yan_hak_sepeti">

    <h1 class="flex items-center justify-between bg-[#7F35B2] py-3 rounded-tl-[10px] rounded-tr-[10px] px-2">
        <div class="flex items-center">
            <img src="{{ asset('assets/images/gift.png') }}" alt="gift" class="w-[17px] h-[17px] mr-2" />
            <span class="font-bold text-white pt-[2px]">{{ __('contents.benefitsBasket') }}</span>
        </div>

        <button data-modal-target="staticModal" data-modal-toggle="staticModal" class="flex items-center justify-center gap-2 text-white hover:text-slate-300" type="button" id="sepeti_sifirla">
            <i class="fa-solid fa-trash-can"></i>
            <span class="text-[11px] font-bold">{{ __('contents.sepetiSıfırla') }}</span>
        </button>
    </h1>

    <div class="h-full max-h-[400px] overflow-y-scroll" id="sepet_scroll_part">
        <div class="flex justify-between items-center px-2 py-1 bg-[#48106D]/[0.04] font-semibold">
            <p class="text-[12px]">{{ __('contents.yillikToplam') }}</p>
            <p id="tvalues" class="text-[12px]">{{ $tt[0] }}.{{ $tt[1] }} TL</p>
        </div>

        <div id="groupsContainer">
            @foreach ($grouped_benefits as $key => $benefit)
                @php
                    $price = $totalPricesByGroup[$key] ?? 0;
                    $tt_group = explode(',', number_format($price, 2, ',', '.'));
                @endphp

                <div class="accordion_div px-2 bg-[#48106D]/[0.04] border-b border-slate-300/[0.5] overflow-hidden" data-group-id="{{ $key }}">
                    <div class="accordion_btn flex justify-between items-center font-semibold cursor-pointer py-1">
                        <p class="text-[#7F35B2] text-[10px] max-w-[195px]">
                            {{ App::getLocale() == 'en' ? $benefit[0]->group_name_en : $benefit[0]->group_name }}
                        </p>
                        <div class="flex items-center justify-between py-1">
                            <span class="mr-1 text-[#5F5F5F] text-[10px] company_car_total">
                                {{ $tt_group[0] }},{{ $tt_group[1] }} TL
                            </span>
                            <img src="{{ asset('assets/images/select_arrow-down.png') }}" alt="arrow" class="max-w-[11px] white-arrow" />
                        </div>
                    </div>

                    @foreach ($benefit as $item)
                        <div class="accordion_item max-h-0" data-benefit-id="{{ $item->id }}">
                            <div class="pb-2 flex justify-between items-center cart_item">
                                <div>
                                    <p class="text-black text-[10px] font-semibold">
                                        {{ App::getLocale() == 'en' ? $item->name_en : $item->name }}
                                    </p>
                                    <p class="text-[#5F5F5F] text-[10px]">
                                        <span class="net">Net: {{ $item->priceNet }} TL</span> -
                                        <span class="brut">{{ __('contents.brut') }}: {{ $item->priceBrut }} TL</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endforeach
        </div>
    </div>

    @if($is_changed)
        @php
            $tt_balance = explode(',', number_format($balance['balance'], 2, ',', '.'));
            $tt_balance_brut = explode(',', number_format($balance['brut_balance'], 2, ',', '.'));
        @endphp
        <div class="px-2 bg-[#48106D]/[0.04] font-semibold py-1 border-b border-slate-300/[0.5]">
            <p class="text-[#7F35B2] text-[11px]">{{ __('contents.brutTutar') }}</p>
            <p class="text-[#169F22] font-bold pt-1 text-[12px] {{ $balance['brut_balance'] < 0 ? 'negative-balance' : '' }}" id="balanced_brut">
                {{ $tt_balance_brut[0] }},{{ $tt_balance_brut[1] }} TL
            </p>
        </div>

        <div class="px-2 bg-[#48106D]/[0.04] font-semibold py-1 border-b border-slate-300/[0.5]">
            <p class="text-[#7F35B2] text-[11px]">{{ __('contents.netTutar') }}</p>
            <p class="text-[#169F22] font-bold pt-1 text-[12px] {{ $balance['balance'] < 0 ? 'negative-balance' : '' }}" id="balanced">
                {{ $tt_balance[0] }},{{ $tt_balance[1] }} TL
            </p>
        </div>
    @endif

    <div class="px-5 pb-3">
        <p class="text-[#C90A0A] text-[10px] py-1 pb-3"> {{ __('contents.basketFooter') }}</p>
        <form action="{{ route('member_dashboard_addAction') }}" method="post" id="alisverisiTamamla" class="alisverisiTamamla">
            @csrf
            <button id="alısverisi_tamamla" type="button" class="font-semibold bg-[#7F35B2] w-full h-[37px] rounded-md text-[#EFF1F3] {{ $balance['balance'] < 0 ? 'negative-balance-btn' : '' }}">
                {{ __('contents.alisverisTamamla') }}
            </button>
        </form>
    </div>

</section>
