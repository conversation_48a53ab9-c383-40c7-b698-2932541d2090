<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>{{ App::getLocale() == 'en' ? 'LOGIN' : 'GİRİŞ YAP' }}</title>
    <link rel="stylesheet" href="{{ asset('assets/css/style-output.css') }}"/>
    <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}"/>
    <link rel="icon" href="{{ asset('assets/images/favicon_old.png') }}" type="image/png">
</head>
<body>
<header class="shadow-[0px_0px_10px_#00000021] h-[90px] flex justify-center items-center relative z-10">
    <nav class="flex justify-between items-center container m-4">
        <img src="{{ asset('storage/' . (App::getLocale() == 'en' ? $customSettings->getFile('logo') : $customSettings->getFile('logo'))) }}" alt="logo"/>

        @php
            $languages = explode(',',$customSettings->getTextLanguage('site_language'));

        @endphp

        @if (count($languages) > 1)
            <div class="flex">
                @foreach($languages as $lang)
                    <a href="{{ route('set.language', $lang) }}"
                       class="{{ session('app_language') == $lang ? 'font-bold' : 'font-normal' }} text-[#7F35B2]">
                        {{ strtoupper($lang) }}
                    </a>
                    @if (!$loop->last)
                        <span class="font-bold text-[#7F35B2] px-1">|</span>
                    @endif
                @endforeach
            </div>
             @else

            <div class="flex">
                    <a href="{{ route('set.language', $languages[0]) }}"
                       class="{{ session('app_language') == $languages[0] ? 'font-bold' : 'font-normal' }} text-[#7F35B2]">
                        {{ strtoupper($languages[0]) }}
                    </a>
            </div>
        @endif
    </nav>
</header>


@if ($errors->has('error'))
    <div class="alert alert-danger center text-red-600 font-bold text-xl pt-3">
        {{ $errors->first('error') }}
    </div>
@endif

<main class="main_login bg-[#FBFAFC] flex justify-center items-center py-[1rem] 2xl:py-[5rem] px-5 sm:px-[2rem] login_parent">
    <div
        class="w-100 max-w-[610px]  lg:grid grid-cols-2 p-2 rounded-[20px] bg-white shadow-[0px_0px_10px_#00000017] pb-7 md:pb-2">
        <img src="assets/images/login_page.png" alt="login_banner"
             class="object-cover hidden lg:block rounded-l-[10px] h-[415px]"/>
        <section class="px-3 pt-3">
            <img src="assets/images/form_logo.png" alt="logo" class="object-cover max-w-[125px] "/>
            <h3 class="text-[20px]  font-bold text-[#7F35B2] py-1 pt-3  max-w-[300px] sm:max-w-[280px] leading-[22px] ">
                {{ __('contents.welcome_message') }}
            </h3>
            <p class="text-[#2F2C31] max-w-[220px] sm:max-w-none md:max-w-[220px] py-1 pb-1  text-[13px]  leading-[16px] ">
                {{ __('contents.login_info') }}
            </p>
            <form action="{{ route('member_login_process') }}" method="POST" autocomplete="off">
                @csrf
                <div class="mt-1">
                    <label for="sicil_no"
                           class="block font-bold text-[#7F35B2] text-[14px] ">{{ __('contents.sicil_no') }}</label>
                    <input id="sicil_no" name="identity_number" type="text" placeholder="{{ __('contents.sicilNo') }}"
                           required
                           class="inline-block w-[100%] px-2 py-[4px] 2 rounded-[5px] border border-[1px solid #E4E6E8] mt-1 focus:outline-none focus:border-[#7F35B2] focus:ring-1 focus:ring-[#7F35B2] placeholder-[#9A9A9A] text-[#7F35B2] text-[14px] textInput"
                           oninvalid="this.setCustomValidity('{!! session('app_language') == 'en' ? 'Please fill in this box' : 'Lütfen bu alanı doldurun' !!}')"
                           oninput="this.setCustomValidity('')"/>
                </div>
                <div class="mt-1  relative">
                    <label for="password"
                           class="block font-bold text-[#7F35B2] text-[14px] ">{{ __('contents.password') }}</label>
                    <input id="password" name="password" type="password"
                           placeholder="{{ __('contents.enter_password') }}"
                           class="inline-block w-[100%] px-2 py-[4px]  rounded-[5px] border border-[#E4E6E8] mt-1 focus:outline-none focus:border-[#7F35B2] focus:ring-1 focus:ring-[#7F35B2] placeholder-[#9A9A9A] text-[#7F35B2] text-[14px] textInput "
                           required
                           oninvalid="this.setCustomValidity('{!! session('app_language') == 'en' ? 'Please fill in this box' : 'Lütfen bu alanı doldurun' !!}')"
                           oninput="this.setCustomValidity('')"/>
                    <img id="password_eye" src="{{ asset('assets/images/password.png') }}" alt="password_eye"
                         class="absolute right-3 bottom-1 cursor-pointer z-20 w-[18px] h-[18px] grayscale"/>
                </div>
                <a href="forgot_password.html" target="_blank" id="forgot_password"
                   class="text-[#7F35B2] pt-1 pb-1  block text-[13px] 2xl:text-[16px]">{{ __('contents.forgot_password') }}</a>
                @foreach($staticPages as $page)
                    <div class="flex items-center gap-2">
                        <input required type="checkbox" id="kvvk_{{$page->id}}" name="kvvk_{{$page->id}}" value="Okudum"
                               class="cursor-pointer accent-[#7F35B2] checkboxInput"
                               oninvalid="this.setCustomValidity('{!! session('app_language') == 'en' ? 'Check this box to proceed' : 'İlerlemek istiyorsanız lütfen bu kutuyu işaretleyin' !!}')"
                               oninput="this.setCustomValidity('')"/>
                        <label for="kvvk_{{$page->id}}" class="text-[12px]  mb-0">
                            {!! App::getLocale() == 'en' ? strip_tags($page['title_en'], '<br><strong><em>') : strip_tags($page['title'], '<br><strong><em>')!!}
                        </label>
                    </div>
                @endforeach
                {{-- <div class="flex items-center gap-2">
                    <input required type="checkbox" id="kvvk" name="kvvk" value="Okudum" class="cursor-pointer accent-[#7F35B2] checkboxInput"
                    oninvalid="this.setCustomValidity('{!! session('app_language') == 'en' ? 'Check this box to proceed' : 'İlerlemek istiyorsanız lütfen bu kutuyu işaretleyin' !!}')"
                    oninput="this.setCustomValidity('')"
                    />
                    <label for="kvvk" class="text-[12px]  mb-0">
                        {{ __('contents.clicktoViewKvkk') }}</label>
    </div>
    <div class="flex items-center gap-2">
        <input required type="checkbox" id="consent_cb" name="consent_cb" value="Okudum" class="cursor-pointer accent-[#7F35B2] checkboxInput" oninvalid="this.setCustomValidity('{!! session('app_language') == 'en' ? 'Check this box to proceed' : 'İlerlemek istiyorsanız lütfen bu kutuyu işaretleyin' !!}')" oninput="this.setCustomValidity('')" />
        <label for="consent_cb" class="text-[12px]  mb-0">
            {{ __('contents.clicktoViewAcikRiza') }}</label>
    </div> --}}
                <button type="submit"
                        class="bg-[#7F35B2] text-white flex justify-center items-center w-[100%] py-[6px]  rounded-md mt-3 "
                        id="submitButton1">
                    {{ __('contents.loginButon') }}
                </button>
            </form>


            <form action="{{ route('forgot.password.process') }}" id="forgotPassword" class="hidden" autocomplete="off">
                @csrf
                <div class="mt-1">
                    <label for="sicil_no"
                           class="block font-bold text-[#7F35B2] text-[14px] ">{{ __('contents.sicil_no') }}</label>
                    <input id="username" name="username" type="text" placeholder="{{ __('contents.sicilNo') }}" required
                           class="inline-block w-[100%] px-2 py-[4px]  rounded-[5px] border border-[1px solid #E4E6E8] mt-1 focus:outline-none focus:border-[#7F35B2] focus:ring-1 focus:ring-[#7F35B2] placeholder-[#9A9A9A] text-[#7F35B2] text-[14px] textInput"
                           oninvalid="this.setCustomValidity('{!! session('app_language') == 'en' ? 'Please fill in this box' : 'Lütfen bu alanı doldurun' !!}')"
                           oninput="this.setCustomValidity('')"/>
                </div>
                <div class="mt-3  relative">
                    <label for="password"
                           class="block font-bold text-[#7F35B2] text-[14px] ">{{ __('contents.email') }}</label>
                    <input id="email" name="email" type="email" placeholder="{{ __('contents.emailInput') }}"
                           class="inline-block w-[100%] px-2 py-[4px]  rounded-[5px] border border-[#E4E6E8] mt-1 focus:outline-none focus:border-[#7F35B2] focus:ring-1 focus:ring-[#7F35B2] placeholder-[#9A9A9A] text-[#7F35B2] text-[14px] textInput"
                           required
                           oninvalid="this.setCustomValidity('{!! session('app_language') == 'en' ? 'Please fill in this box' : 'Lütfen bu alanı doldurun' !!}')"
                           oninput="this.setCustomValidity('')"/>
                </div>

                <a href="login.html" class="text-[#7F35B2] pt-3  flex gap-2 cursor-pointer text-[13px] ">
                    <img src="{{ asset('assets/images/left_arrow.png') }}" alt="left_arrow" class="object-contain">
                    {{ __('contents.backtologin') }}</a>
                <!-- <div class="flex items-center gap-2">
                    <input type="checkbox" id="kvvk" name="kvvk" value="Okudum">
                    <label for="kvvk" class="text-[12px] 2xl:text-[16px]"><a href="#" target="_blank" class="text-[#00A7FF] underline">KVKK</a> {{ __('contents.clicktoView') }}</label>
                  </div> -->
                <!-- <div class="flex items-center gap-2">
                        <input type="checkbox" id="acik_metin" name="acik_metin" value="Okudum">
                        <label for="kvvk" class="text-[12px] 2xl:text-[16px]"><a href="#" target="_blank" class="text-[#00A7FF] underline">Açık metin</a> görüntülemek için tıklayınız</label>
                      </div> -->
                <button type="submit"
                        class="bg-[#7F35B2] text-white flex justify-center items-center w-[100%] py-[6px]  rounded-md mt-3 "
                        id="submitButton2">{{ __('contents.send') }}</button>

            </form>
        </section>
    </div>
</main>
@foreach($staticPages as $page)
    @if (App::getLocale() == 'en')
        <div id="popup_overlay_{{$page->id}}"
             class="bg-[rgba(0,0,0,0.5)] w-full fixed left-0 top-0 h-[100vh] flex justify-center items-center z-[10000] px-6 hidden">
            <div
                class="w-full max-w-[630px] m-auto bg-white shadow-[0px_0px_10px_#00000017] p-2 rounded-xl overflow-hidden relative content_parent_div h-full max-h-[570px]">

                <img id="kvkk_close_btn_{{$page->id}}" src="{{ asset('assets/images/close.png') }}"
                     class="absolute top-3 right-3 cursor-pointer object-contain"/>

                <h3 class="text-center font-bold py-4 text-[#7F35B2]">{{$page->name_en}}</h3>
                <div
                    class="h-[82%] overflow-y-auto kvvk_popup text-justify px-3 text-[13px] popup_content"> {!! $page->content_en !!}</div>

            </div>
        </div>
    @else
        <div id="popup_overlay_{{$page->id}}"
             class="bg-[rgba(0,0,0,0.5)] w-full fixed left-0 top-0 h-[100vh] flex justify-center items-center z-[10000] px-6 hidden">
            <div
                class="w-full max-w-[630px] m-auto bg-white shadow-[0px_0px_10px_#00000017] p-2 rounded-xl  overflow-hidden relative content_parent_div h-full max-h-[570px]">

                <img id="kvkk_close_btn_{{$page->id}}" src="{{ asset('assets/images/close.png') }}"
                     class="absolute top-3 right-3 cursor-pointer object-contain"/>

                <h3 class="text-center font-bold py-4 text-[#7F35B2] mx-auto max-w-[550px]">{{ $page->name }}</h3>
                <div
                    class="h-[82%] overflow-y-auto kvvk_popup text-justify px-3 text-[13px] popup_content">{!! $page->content !!}</div>
            </div>
        </div>
    @endif
@endforeach


{{-- @if (App::getLocale() == 'en')
<div id="consent_overlay" class="bg-[rgba(0,0,0,0.5)] w-full fixed left-0 top-0 h-full flex justify-center items-center z-[10000] px-6 hidden">
    <div class="w-full max-w-[380px] m-auto bg-white shadow-[0px_0px_10px_#00000017] p-2 rounded-xl relative">
        <!-- <h1 class="text-right font-bold cursor-pointer text-[#7F35B2] text-2xl" id="consent_close_btn">
            x
        </h1> -->
        <img id="consent_close_btn" src="{{ asset('assets/images/close.png') }}" class="absolute top-3 right-3 cursor-pointer object-contain"/>

<h3 class="text-center font-bold pt-[30px] pb-4 text-[#7F35B2]">EXPLICIT CONSENT ON PERSONAL DATA PROCESSING FOR THE PURPOSE OF MANAGEMENT OF FLEXIBLE BENEFITS</h3>
<div class="">
    <!-- <p class="text-sm pb-3 text-justify px-3">
                As per the Privacy Notice provided by Willis Towers Watson (“WTW”) dated February 2022;
            </p> -->
    <p class="text-sm pb-1 px-3">
        I confirm that I read the foresaid Privacy Notice;
    </p>
    <p class="text-sm pb-1 px-3">
        And accordingly; I hereby expressly consent to transfer of my personal data abroad in terms of the storage of the data located in servers abroad.
    </p>
</div>

</div>
</div>

@else
<div id="consent_overlay" class="bg-[rgba(0,0,0,0.5)] w-full fixed left-0 top-0 h-full flex justify-center items-center z-[10000] px-6 hidden">
    <div class="w-full max-w-[380px] m-auto bg-white shadow-[0px_0px_10px_#00000017] p-2 rounded-xl relative">
        <!-- <h1 class="text-right font-bold cursor-pointer text-[#7F35B2] text-2xl" id="consent_close_btn">
            x
        </h1> -->
        <img id="consent_close_btn" src="{{ asset('assets/images/close.png') }}" class="absolute top-3 right-3 cursor-pointer object-contain" />
        <h3 class="text-center font-bold pt-[30px] pb-4 text-[#7F35B2] px-3">ESNEK YAN HAKLAR YÖNETİMİ AMACIYLA
            İŞLENECEK KİŞİSEL VERİLER HAKKINDA AÇIK İZİN</h3>
        <div class="">
            <!-- <p class="text-sm pb-3 px-3">
                Willis Towers Watson (“WTW”) tarafından paylaşılan Şubat 2022 tarihli Aydınlatma Metni uyarınca;
            </p> -->
            <p class="text-sm pb-1 px-3">
                İlgili Aydınlatma Metni’ni okuduğumu;
            </p>
            <p class="text-sm pb-1 px-3">
                Verilerin yurtdışında bulunan sunucularda depolanması açısından, kişisel verilerimin yurtdışına
                aktarılmasına;
            </p>
            <p class="text-sm pb-1 px-3">
                6698 sayılı Kişisel Verilerin Korunması Kanunu uyarınca açık iznimi verdiğimi beyan ederim.
            </p>
        </div>


    </div>
</div>
@endif --}}


<div id="login_info"
     class="fixed left-0 top-0 w-full h-full justify-center items-center z-[1000] bg-black bg-opacity-50 p-[1rem] hidden">
    <div
        class="w-full max-w-[420px] md:max-w-[721px] bg-[#fff] shadow-[0px_0px_20px_#00000017] relative rounded-[10px] p-[10px] sm:p-[2.5rem]">

        <img id="answerClose" src="{{ asset('assets/images/close.png') }}"
             class="absolute top-[18px] sm:top-[2.5rem] right-[10px] sm:right-[2.5rem] cursor-pointer w-[13px] sm:w-auto"/>
        <div class="flex flex-col md:flex-row justify-center md:justify-start items-center  gap-5">
            <img src="{{ asset('assets/images/144x144px-5.png') }}" alt="lightening" class="w-[100px] sm:w-auto"/>
            <p id="loginInfoText"
               class="text-[#2F2C31] py-3 max-w-[400px] text-[15px] md:text-[16px] text-center md:text-left">
                {{ __('contents.forgot_password_text') }}
            </p>
        </div>
    </div>
</div>

<footer class="shadow-[0px_0px_10px_#00000021] h-[90px]"></footer>
<script>
    var appLocale = "{{ App::getLocale() }}";
</script>
<script src="{{ asset('assets/js/jquery.js') }}"></script>
<script src="{{ asset('assets/js/frontend/script.js') }}"></script>
<!-- <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> -->
<script type="application/javascript">
    $(document).ready(function () {


        $('a[href="forgot_password.html"]').click(function (e) {
            e.preventDefault();
            $('#forgotPassword').removeClass("hidden").addClass("block")

            $('form[action="{{ route("member_login_process") }}"]').css('display', 'none');
        });

        $('a[href="login.html"]').click(function (e) {
            e.preventDefault();

            $('#forgotPassword').removeClass("block").addClass("hidden")

            $('form[action="{{ route("member_login_process") }}"]').css('display', 'block');
        });


        $('#forgotPassword').submit(function (e) {
            e.preventDefault();
            $.ajax({
                type: 'POST',
                url: $(this).attr('action'),
                data: $(this).serialize(),
                success: function (response) {
                    var appLocale = "{{ App::getLocale() }}";

                    $('#login_info').removeClass("hidden").addClass("flex");
                    if (appLocale == 'en') {
                        $('#loginInfoText').text(response.message_en);
                    } else {
                        $('#loginInfoText').text(response.message);
                    }

                    // alert(response.message);
                },
                error: function (response) {
                    if (appLocale == 'en') {
                        alert('An error occured');
                    } else {
                        alert('Hata oluştu!');
                    }

                }
            });
        });

        $('#answerClose').on('click', function () {
            $('#login_info').removeClass("flex").addClass("hidden");
        });

    });


    $(document).ready(function () {
        let passwordImg = "{{ asset('assets/images/password.png') }}";
        let passwordShowImg = "{{ asset('assets/images/password_show.png') }}";

        $("#password_eye").on("click", function () {
            let currentSrc = $(this).attr("src");
            let newSrc = "";

            var passwordInput = $("#password");
            if (passwordInput.attr("type") === "password") {
                passwordInput.attr("type", "text");
                newSrc = passwordShowImg;
            } else {
                passwordInput.attr("type", "password");
                newSrc = passwordImg;
            }

            $(this).attr("src", newSrc);
        });
    });
</script>


</body>

</html>
