<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="csrf-token" content="{{ csrf_token() }}"> <!-- CSRF Token -->
    <title>{{ App::getLocale() == 'en' ? 'LOGIN' : 'GİRİŞ YAP' }}</title>
    <link rel="stylesheet" href="{{ asset('assets/css/style-output.css') }}"/>
    <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}"/>
    <link rel="icon" href="{{ asset('assets/images/favicon_old.png') }}" type="image/png">
</head>
<body>

<style>
.main_login {
    min-height: 100vh;
}
#otpDiv{
    width: 100%;
    max-width: 400px;
}
.otpButtons{
    display:flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
}

@media(max-width: 576px){
    .otpButtons{
        flex-direction: column;
        gap:0;
        padding-bottom: 0;
    }
}
</style>
@if ($errors->has('error'))
    <div class="alert alert-danger center text-red-600 font-bold text-xl pt-3">
        {{ $errors->first('error') }}
    </div>
@endif

<main class="main_login bg-[#FBFAFC] flex justify-center items-center py-[1rem] 2xl:py-[5rem] px-5 sm:px-[2rem] login_parent">
    <div class="w-100 max-w-[600px] p-2 rounded-[20px] bg-white shadow-[0px_0px_10px_#00000017] pb-7 md:pb-2" id="otpDiv">
        <section class="px-3 pt-3">
            <form action="{{ route('verify_otp') }}" method="POST">
                @csrf
                <div class="mt-1">
                    <label for="otp" class="block font-bold text-[#7F35B2] text-center text-[20px] ">OTP</label>
                    <input id="otp" name="otp" type="text"
                           required
                           class="inline-block w-[100%] px-2 py-[4px] 2 rounded-[5px] border border-[1px solid #E4E6E8] mt-1 focus:outline-none focus:border-[#7F35B2] focus:ring-1 focus:ring-[#7F35B2] placeholder-[#9A9A9A] text-[#7F35B2] text-[14px] textInput"/>
                </div>

                <div class="otpButtons">
                    <button type="submit"
                            class="bg-[#7F35B2] text-white flex justify-center items-center w-[100%] py-[6px]  rounded-md mt-3 "
                            id="submitButton1">
                        {{ __('contents.loginButon') }}
                    </button>
                    <button type="button"
                            class="bg-[#7F35B2] text-white flex justify-center items-center w-[100%] py-[6px]  rounded-md mt-3 "
                            id="resendOtp">
                        {{__('contents.resendOTP')}}
                    </button>
                </div>

            </form>
        </section>
    </div>
</main>

<script>
    var appLocale = "{{ App::getLocale() }}";
</script>
<script src="{{ asset('assets/js/jquery.js') }}"></script>
<script src="{{ asset('assets/js/frontend/script.js') }}"></script>
<script>
    $(document).ready(function() {
        function resendOtp() {
            var csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            $.ajax({
                url: '/otp/resend',
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                },
                success: function(response) {
                    alert(response.message);
                },
                error: function(xhr) {
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        alert('Hata: ' + xhr.responseJSON.error);
                    } else {
                        alert('Bir hata oluştu.');
                    }
                }
            });
        }

        $('#resendOtp').on('click', function() {
            resendOtp();
        });
    });


</script>


</body>

</html>
