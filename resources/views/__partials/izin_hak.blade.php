@if(!empty($predefinedFlexiblePriceValues[$benefit['id']]))
   @php
       $flexible_text=$customSettings->getText('izin_hakki_text');
       $flexible_text_brut=$customSettings->getText('izin_hakki_text_brut');
    @endphp

    {{-- İZİN HAKKI ÖZEL ALAN --}}
    <div class="py-5 px-2 sm:px-5 flex items-start justify-start gap-3 md:gap-5 izin_hakki_div">
        <img src="{{ asset('storage/' . (App::getLocale() == 'en'  ? $benefit['image_en'] : $benefit['image'])) }}"
             alt="sail" class="w-[70px] sm:w-[130px] izin_hakki_img"/>
        <div class="w-full">
            <h1 class="pb-2 text-[14px] md:text-[17px] font-bold text-[#7F35B2] izin_hakki_title">
                {{ App::getLocale() == 'en' ? $benefit['name_en'] : $benefit['name'] }}

            </h1>
            <p class="text-[#2F2C31] text-[12px] md:text-[13px]">
                {!! App::getLocale() == 'en' ? strip_tags($benefit['description_en'],'<strong>') : strip_tags($benefit['description'],'<strong>') !!}
            </p>
            <p class="text-[#7F35B2] text-[14px] pt-2 hidden md:block">
                {{ __('contents.maxizin') }}:<span class="font-bold pl-[2px]">
                {{ isset($predefinedFlexiblePriceValues[$benefit['id']]) ? sprintf("%.0f", $predefinedFlexiblePriceValues[$benefit['id']]) : "" }} {{ __('contents.day') }}
 </span>
            </p>
        </div>
    </div>
    <p class="text-[#7F35B2] text-[12px] pb-3 md:hidden pl-2 sm:pl-5">
        {{ __('contents.maxizin') }}:<span class="font-bold pl-[2px]">
                {{ isset($predefinedFlexiblePriceValues[$benefit['id']]) ? sprintf("%.0f", $predefinedFlexiblePriceValues[$benefit['id']]) : "" }} {{ __('contents.day') }}
 </span>
    </p>
    <div class="px-2 sm:px-5 mb-3">
        <p class="text-[#7F35B2] text-[14px] font-semibold pb-2">
            {{ $flexible_text_brut }}
        </p>
        <form action="{{ route('member_dashboard_addAction') }}" method="post" id="frm_{{ $benefit['id'] }}"
              class="frmEsnekHak" data-benefit_id="{{ $benefit['id'] }}" autocomplete="off">
            @csrf
            <div class="flex flex-col md:flex-row justify-between items-center gap-3 md:gap-0">
                <div class="flex gap-3 items-center justify-start pr-4 w-full">
                    <p class=" h-[39px] invisible w-full md:max-w-[75px]"></p>
                    <p class="invisible text-[#959595]">x</p>
                    <p class="md:max-w-[156px] text-[#7F35B2] text-[14px] text-center w-full">Brüt Tutar</p>
                    <p class="md:max-w-[156px] text-[#7F35B2] text-[14px] text-center w-full">Net Tutar</p>
            </div>
                <button class=" h-[39px] hidden md:block invisible  sm:w-[222px]  w-full">
                </button>
            </div>
            <div class="flex flex-col md:flex-row justify-between items-center gap-3 md:gap-0">
                <div class="flex justify-start items-center gap-3 w-full md:pr-4 md:w-auto">
                    <select name="birey_field_p_adet" id="birey_field_p_adet"
                            class="w-[90px] h-[39px] border border-[#E4E6E8] px-3 text-[#959595] text-[14px] rounded-[5px] focus:outline-none focus:border-[#7F35B2] focus:ring-1 focus:ring-[#7F35B2] pointer-events-none">
                        <option value="" disabled selected>Seçiniz</option>
                        @if(isset($predefinedFlexiblePriceValues[$benefit['id']]))

                            @for ($i = 0; $i <= $predefinedFlexiblePriceValues[$benefit['id']]; $i++)
                                <option value="{{ $predefinedFlexiblePriceValues[$benefit['id']] - $i }}"
                                        @if($predefinedFlexiblePriceValues[$benefit['id']] - $i == $values[$benefit['id']])
                                            selected
                                    @endif>
                                    {{ $i }}
                                </option>
                            @endfor
                        @endif

                    </select>
                    <p class="text-[#959595]">x</p>
                    @php
                        $isBulutfinNet = isset($benefit['benefit_bulutfin_mode']) && $benefit['benefit_bulutfin_mode'] == 1;
                    @endphp
                    <input
                        name="day_price_brut"
                        class="w-full md:max-w-[156px] h-[39px] border border-[#E4E6E8] px-3 text-[#959595] text-[14px] text-center rounded-[5px] {{ $isBulutfinNet ? 'outline-none border-[#E4E6E8]' : 'outline-none border-[#7F35B2] ring-1 ring-[#7F35B2]' }}"
                        value="{{ isset($benefit['day_price_brut']) ? number_format($benefit['day_price_brut'], 2, '.', ',') . ' TL' : '' }}"
                        readonly
                        id="leavePrice_brut"
                    />

                    <input
                        name="day_price_net"
                        class="w-full md:max-w-[156px] h-[39px] border border-[#E4E6E8] px-3 text-[#959595] text-[14px] text-center rounded-[5px] {{ $isBulutfinNet ? 'outline-none border-[#7F35B2] ring-1 ring-[#7F35B2]' : 'outline-none border-[#E4E6E8]' }}"
                        value="{{ isset($benefit['day_price_net']) ? number_format($benefit['day_price_net'], 2, '.', ',') . ' TL' : '' }}"
                        readonly
                        id="leavePrice_net"
                    />
                </div>
                <button id="leave_update_btn_brut"
                        class="bg-[#7F35B2] text-[#EFF1F3] rounded-[5px] w-full md:w-[222px] h-[39px] flex justify-center items-center">
                    {{ __('contents.update') }}
                </button>
            </div>
            <input type="hidden" name="benefit_id" value="{{ $benefit['id'] }}">
            <input type="hidden" name="izin" value="izin">
            <input type="hidden" name="benefit_fin_code" value="{{ $benefit['benefit_fin_code'] }}">



        </form>
        <!-- @php
            $colorLang = App::getLocale() == 'en' ? 'alert_color_en' : 'alert_color';
            $bgColorLang = App::getLocale() == 'en' ? 'alert_bg_color_en' : 'alert_bg_color';
            $color = match($benefit[$colorLang]){
                "red" => 'red',
                "green" => 'green',
                default => 'text-[#7F35B2]',
            };
            $bgColor = match($benefit[$bgColorLang]){
                "red" => 'bg-red-200',
                "green" => 'bg-green-200',
                default => 'bg-[#7f35b2]/[0.1]',
            };
        @endphp
        @if($benefit['alert_message'])
        <p
            class="{{ $color ?? 'text-[#7F35B2]' }} {{ $bgColor ?? 'bg-[#7f35b2]/[0.1]' }} text-[12px] md:text-[13px] p-3 sm:p-5 mt-6 rounded-[10px] leading-[17px]">
                {!! App::getLocale() == 'en' ? strip_tags($benefit['alert_message_en']) : strip_tags($benefit['alert_message']) !!}
        </p>
        @endif -->
    </div>
{{--    <div class="px-2 sm:px-5">--}}
{{--        <p class="text-[#7F35B2] text-[14px] font-semibold pb-2">--}}
{{--            {{ $flexible_text_brut }}--}}
{{--        </p>--}}
{{--        <form action="{{ route('member_dashboard_addAction') }}" method="post" id="frm_brut_{{ $benefit['id'] }}"--}}
{{--              class="frmEsnekHak" data-benefit_id="{{ $benefit['id'] }}" autocomplete="off">--}}
{{--            @csrf--}}
{{--            <div class="flex flex-col md:flex-row justify-between items-center gap-3 md:gap-0">--}}
{{--                <div class="flex justify-start items-center gap-3 w-full md:w-auto">--}}
{{--                    <select name="birey_field_p_adet" id="birey_field_p_adet_brut_{{ $benefit['id'] }}"--}}
{{--                            class="w-full md:w-[141px] h-[39px] border border-[#E4E6E8] px-3 text-[#959595] text-[14px] rounded-[5px] focus:outline-none focus:border-[#7F35B2] focus:ring-1 focus:ring-[#7F35B2]">--}}
{{--                        <option value="" disabled selected>Seçiniz</option>--}}
{{--                        @if(isset($predefinedFlexiblePriceValues[$benefit['id']]))--}}

{{--                            @for ($i = 0; $i <= $predefinedFlexiblePriceValues[$benefit['id']]; $i++)--}}
{{--                                <option value="{{ $predefinedFlexiblePriceValues[$benefit['id']] - $i }}"--}}
{{--                                        @if($predefinedFlexiblePriceValues[$benefit['id']] - $i == $values[$benefit['id']])--}}
{{--                                            selected--}}
{{--                                    @endif>--}}
{{--                                    {{ $i }}--}}
{{--                                </option>--}}
{{--                            @endfor--}}
{{--                        @endif--}}

{{--                    </select>--}}
{{--                    <p class="text-[#959595]">x</p>--}}
{{--                    <input--}}
{{--                        class="w-full md:w-[156px] h-[39px] border border-[#E4E6E8] px-3 text-[#959595] text-[14px] text-center rounded-[5px] focus:outline-none focus:border-[#7F35B2] focus:ring-1 focus:ring-[#7F35B2]"--}}
{{--                        value="{{ isset($benefit['final_price']) ?  number_format($benefit['final_price'], 2, '.', ',') : "" }} TL"--}}
{{--                        readonly id="leavePrice_brut_{{ $benefit['id'] }}"/>--}}
{{--                </div>--}}
{{--                <button id="leave_update_btn_brut_{{ $benefit['id'] }}"--}}
{{--                        class="bg-[#7F35B2] text-[#EFF1F3] rounded-[5px] w-full md:w-[222px] h-[39px] flex justify-center items-center">--}}
{{--                    {{ __('contents.update') }}--}}
{{--                </button>--}}
{{--            </div>--}}
{{--            <input type="hidden" name="benefit_id" value="{{ $benefit['id'] }}">--}}
{{--            <input type="hidden" name="izin" value="izin_brut">--}}

{{--        </form>--}}
{{--        @php--}}
{{--            $colorLang = App::getLocale() == 'en' ? 'alert_color_en' : 'alert_color';--}}
{{--            $bgColorLang = App::getLocale() == 'en' ? 'alert_bg_color_en' : 'alert_bg_color';--}}
{{--            $color = match($benefit[$colorLang]){--}}
{{--                "red" => 'red',--}}
{{--                "green" => 'green',--}}
{{--                default => 'text-[#7F35B2]',--}}
{{--            };--}}
{{--            $bgColor = match($benefit[$bgColorLang]){--}}
{{--                "red" => 'bg-red-200',--}}
{{--                "green" => 'bg-green-200',--}}
{{--                default => 'bg-[#7f35b2]/[0.1]',--}}
{{--            };--}}
{{--        @endphp--}}
{{--        @if($benefit['alert_message'])--}}
{{--        <p--}}
{{--            class="{{ $color ?? 'text-[#7F35B2]' }} {{ $bgColor ?? 'bg-[#7f35b2]/[0.1]' }} text-[12px] md:text-[13px] p-3 sm:p-5 mt-6 rounded-[10px] leading-[17px]">--}}
{{--                {!! App::getLocale() == 'en' ? strip_tags($benefit['alert_message_en']) : strip_tags($benefit['alert_message']) !!}--}}
{{--        </p>--}}
{{--        @endif--}}
{{--    </div>--}}

   <style>
       #loadingSpinner {
           position: fixed;
           top: 0;
           left: 0;
           width: 100%;
           height: 100%;
           background-color: rgba(0, 0, 0, 0.5); /* Yarı şeffaf siyah arka plan */
           z-index: 9999;
           display: flex;
           justify-content: center;
           align-items: center;
       }

       .spinner {
           border: 8px solid #f3f3f3; /* Açık gri */
           border-top: 8px solid #3498db; /* Mavi */
           border-radius: 50%;
           width: 60px;
           height: 60px;
           animation: spin 1s linear infinite;
       }

       @keyframes spin {
           0% {
               transform: rotate(0deg);
           }
           100% {
               transform: rotate(360deg);
           }
       }

   </style>

   <div id="loadingSpinner" style="display: none;">
       <div class="spinner"></div>
   </div>
    {{-- İZİN HAKKI ÖZEL ALAN --}}
    <script src="{{ asset('assets/js/jquery.js') }}"></script>
    <script>

        var brutLabel = "{!! __('contents.brut') !!}";
        $(document).ready(function () {
            var appLocale = "{{ App::getLocale() }}";
            $('#frm_{{ $benefit["id"] }}').on('submit', function (e) {
                $('#loadingSpinner').show();
                e.preventDefault();

                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function (response) {
                        if (response.success) {
                            $('#loadingSpinner').hide();
                            updateInitData();
                        } else {
                            $('#loadingSpinner').hide();
                            alert('Hata oluştu!');
                        }
                    },
                    error: function (xhr) {
                        $('#loadingSpinner').hide();
                        alert('Hata oluştu!');
                    }
                });
            });

            function updateInitData() {

                $('#loadingSpinner').show();

                $.ajax({
                    url: '/get-init-data',
                    method: 'GET',
                    success: function (response) {


                        processResponseIzin(response);

                        // Bakiyeyi güncelle
                        var balance = parseFloat(response.balance.balance);
                        var balanceBrut = parseFloat(response.balance.brut_balance);
                        var formattedBalance = balance.toLocaleString('tr-TR', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        });

// Brüt bakiyeyi formatla
                        var formattedBrutBalance = balanceBrut.toLocaleString('tr-TR', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        });

                        var balanceElement = $('#balanced');
                        var brutBalanceElement = $('#balanced_brut');
                        var butonBalanceElement = $('#alısverisi_tamamla');

                        balanceElement.text(formattedBalance + ' TL');
                        brutBalanceElement.text(formattedBrutBalance + ' TL');

                        if (balance < 0) {
                            balanceElement.addClass('negative-balance');
                            butonBalanceElement.addClass('negative-balance-btn');
                        } else {
                            balanceElement.removeClass('negative-balance');
                            butonBalanceElement.removeClass('negative-balance-btn');
                        }

                        if (balanceBrut < 0) {
                            brutBalanceElement.addClass('negative-balance');
                        } else {
                            brutBalanceElement.removeClass('negative-balance');
                        }

                        // if(balance < 0) {
                        //     $('#balanced').css('color', 'red');
                        // }

                        // Gruplanmış benefitleri güncelle
                        var groupedBenefits = response.grouped_benefits;
                        addNewBenefitsLeave(groupedBenefits, userInfo);
                        var totalPricesByGroup = response.totalPricesByGroup;
                        var userInfo = response.user_info;
                        // Gruplar üzerinde dön
                        $.each(groupedBenefits, function (groupId, benefits) {
                            var totalPrice = totalPricesByGroup[groupId];

                            // Grup başlığını güncelle

                            if (appLocale == "en") {
                                var groupTitleElement = $('.accordion_div[data-group-id="' + groupId + '"] .accordion_btn p');
                                if (groupTitleElement.length) {
                                    var groupName = response.groupNamesEng[groupId];
                                    groupTitleElement.text(groupName);
                                }
                            } else {
                                var groupTitleElement = $('.accordion_div[data-group-id="' + groupId + '"] .accordion_btn p');
                                if (groupTitleElement.length) {
                                    var groupName = response.groupNames[groupId];
                                    groupTitleElement.text(groupName);
                                }

                            }
                            // var groupTitleElement = $('.accordion_div[data-group-id="' + groupId + '"] .accordion_btn p');
                            // if (groupTitleElement.length) {
                            //     var groupName = response.groupNames[groupId];
                            //     groupTitleElement.text(groupName);
                            // }

                            // Toplam fiyatı güncelle
                            var totalPriceElement = $('.accordion_div[data-group-id="' + groupId + '"] .accordion_btn .company_car_total');
                            if (totalPriceElement.length) {
                                var formattedTotalPrice = parseFloat(totalPrice).toLocaleString('tr-TR', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                                totalPriceElement.text(formattedTotalPrice + ' TL');
                            }

                            $.each(benefits, function (index, benefit) {
                                var net = parseFloat(benefit.priceNet) || 0;
                                var brut = parseFloat(benefit.priceBrut) || 0;

                                var formattedNet = net.toLocaleString('tr-TR', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                                var formattedBrut = brut.toLocaleString('tr-TR', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });

                                var benefitElement = $('.accordion_item[data-benefit-id="' + benefit.id + '"]');
                                if (benefitElement.length) {
                                    var netElement = benefitElement.find('.net');
                                    if (netElement.length) {
                                        netElement.text('Net: ' + formattedNet + ' TL');
                                    }

                                    var brutElement = benefitElement.find('.brut');
                                    if (brutElement.length) {
                                        brutElement.text(`${brutLabel}:  ${formattedBrut}  TL`);
                                    }
                                }
                            });
                        });

                        $('#loadingSpinner').hide();



                    },
                    error: function () {
                        alert('Hata oluştu!');
                    }
                });
            }


            function addNewBenefitsLeave(groupedBenefits, userInfo) {

                $.each(groupedBenefits, function (groupId, benefits) {
                    var groupElement = $('.accordion_div[data-group-id="' + groupId + '"]');


                    if (groupElement.length === 0) {

                        var newGroupHTML = `
              <div class="accordion_div px-2 bg-[#48106D]/[0.04] border-b border-slate-300/[0.5] overflow-hidden" data-group-id="${groupId}">
                  <div class="accordion_btn flex justify-between items-center font-semibold cursor-pointer py-1">
                      <p class="text-[#7F35B2] text-[10px] max-w-[195px]"></p>
                      <div class="flex items-center justify-between py-1">
                          <span class="mr-1 text-[#5F5F5F] text-[10px] company_car_total" id="company_car_total"> TL</span>
                          <img src="{{ asset('assets/images/select_arrow-down.png') }}" alt="arrow" class="max-w-[11px] white-arrow" />
                      </div>
                  </div>
              </div>
          `;
                        $('#groupsContainer').append(newGroupHTML);
                        groupElement = $('.accordion_div[data-group-id="' + groupId + '"]');
                    }

                    // Her bir benefit için işlem yap
                    $.each(benefits, function (index, benefit) {
                        if(groupId=="14"){
                            groupElement.find('.accordion_item').removeClass('max-h-0').addClass('max-h-fit');

                        }

                        var benefitElement = groupElement.find('.accordion_item[data-benefit-id="' + benefit.id + '"]');

                        // Eğer benefit elementi yoksa, yenisini oluştur
                        if (benefitElement.length === 0) {
                            var benefitName = appLocale == 'en' ? benefit.name_en : benefit.name;

                            var newBenefitHTML = `
                  <div class="accordion_item max-h-fit" data-benefit-id="${benefit.id}">
                      <div class="pb-2 flex justify-between items-center cart_item">
                          <div>
                              <p class="text-black text-[11px] font-semibold">${benefitName}</p>
                              <p class="text-[#5F5F5F] text-[11px]">
                                  <span class="net">Net: ${benefit.netPrice} TL</span> -
                                  <span class="brut">${brutLabel}: ${benefit.brutPrice} TL</span>
                              </p>
                          </div>
                      </div>
                  </div>
              `;
                            groupElement.append(newBenefitHTML);
                        }
                    });
                });
            }


            function processResponseIzin(response) {

                var final_benefits = response.final_benefits;


                var benefitToRemoveId;
                for (var i = 0; i < final_benefits.length; i++) {
                    var benefit = final_benefits[i];
                    var priceNet = benefit.priceNet;

                    var amountZero = priceNet == null || priceNet === 0;

                    if (amountZero && benefit.type == 5) {
                        benefitToRemoveId = benefit.id;
                        break;
                    }
                }


                if (benefitToRemoveId !== undefined) {
                    var elementToRemove = $('div[data-benefit-id="' + benefitToRemoveId + '"]');
                    elementToRemove.remove();
                }

            }

        });
    </script>

@endif
