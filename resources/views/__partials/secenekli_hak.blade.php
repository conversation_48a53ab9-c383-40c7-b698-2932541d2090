@php

if (!empty($ozelSaglik) && isset($ozelSaglik[0])) {
$detail_id = $ozelSaglik[0]->benefit_id;
} else {
$detail_id = null;
}

$rules=$customSettings->getText('rules');
$componentsValue=$customSettings->getText('project_components');

//$selectedCustom = session('selected_custom', []);
session(['selected_rule' => $rules]);
@endphp

{{-- BURASI BENEFİT --}}
<div class="pt-7 pb-2 md:pt-10 px-1 sm:px-5 flex items-start justify-start gap-3 md:gap-5  mb-3 md:mb-0">
    <img src="{{ asset('storage/' . (App::getLocale() == 'en'  ? $benefit['image_en'] : $benefit['image'])) }}" alt="hamburger" class="w-[70px] sm:w-full sm:max-w-[130px] yemek_karti_img" />
    <div class="w-full">
        <h1 class="pb-2 text-[14px] md:text-[16px] font-bold text-[#7F35B2] yemek_karti_title">
            {{ App::getLocale() == 'en' ? $benefit['name_en'] : $benefit['name'] }}
        </h1>
        {{-- <p class="text-[12px] md:text-[13px] leading-[19.5px] text-[#707070]">
            {!! App::getLocale() == 'en' ? strip_tags($benefit['description_en']) : strip_tags($benefit['description']) !!}
        </p> --}}
        <p class="text-[12px] md:text-[13px] leading-[19.5px] text-[#707070]" id="secenekliA">
            {!! App::getLocale() == 'en' ? strip_tags($benefit['description_en'], '<a><strong><br><em>') : strip_tags($benefit['description'], '<a><strong><br><em>') !!}

        </p>
        <br>
        @if(($benefit['has_details'])==1)
        <p class="text-[12px] md:text-[13px] further_details_secenekli cursor-pointer text-[#7F35B2]" onclick="$('#futherDetailsSecenekliModal{{$benefit['id']}}').removeClass('hidden').addClass('flex')"> {{ __('contents.detailInfo') }}
            @endif
    </div>

</div>

@if($detail_id==$benefit['id'])
<p class="px-2 sm:px-5 font-semibold text-[#7F35B2] text-[12px] md:text-[13px] py-2 border-b border-slate-300/[0.5]">
    {{ __('contents.special_health_insurance_text') }}
    <strong> {!! App::getLocale() == 'en' ? strip_tags($ozelSaglik[0]->name_en, '<a><strong>') : strip_tags($ozelSaglik[0]->name, '<a><strong>') !!}</strong>
</p>
@endif

@if(!empty($benefit['desc_image']) || !empty($benefit['desc_image_en']))
<img src="{{ asset('storage/' . (App::getLocale() == 'en'  ? $benefit['desc_image_en'] : $benefit['desc_image'])) }}" alt="hamburger" class="w-full mb-1" />
@endif
{{-- BURASI BENEFİT --}}


{{-- BURASI ONA BAGLI OPTIONLAR --}}
<form action="{{ route('member_dashboard_addAction') }}" method="post" id="frmSecenekli_{{ $benefit['id'] }}" class="frmEsnekHak" data-benefit_id="{{ $benefit['id'] }}"  autocomplete="off">
    @csrf
    @foreach($benefit['options'] as $option)


    <div class="px-2 sm:px-5 border-b border-slate-300/[0.5] special_health_parent">
        <div class="flex justify-between items-center relative pt-2">
            <div class="flex justify-start items-center meal_ticket_container">
                <div class="radio-container w-[23px] h-[23px] flex justify-center items-center relative z-10 rounded-[5px]">
                    <div class="relative w-[15px] h-[15px] flex justify-center items-center">
                        <input
                            type="radio"
                            name="option_id"
                            value="{{ $option['id'] }}"
                            id="option{{ $option['id'] }}"
                            data-id="{{ $option['id'] }}"
                            data-benefit-name="{{ $option['name'] }}"
                            data-option-price="{{ $option['priceBrut'] }}"
                            data-fin-code="{{ $option['benefit_fin_code'] }}"
                            data-fin-type="{{ $option['benefit_fin_typ'] }}"
                            data-fin-nr="{{ $option['benefit_fin_nr'] }}"
                            data-price-net="{{ $option['priceNet'] }}"
                        class="hidden"
                        onchange="updateRadioContainerBackground(this)"
                        {{ !empty($selecteds[$benefit['id']]) && $option['id']==$selecteds[$benefit['id']] ? 'checked' : '' }}
                        />

                        <label for="option{{$option['id']}}" class="inline-block w-full h-full border border-[#0F56AF] cursor-pointer rounded-sm custom-radio-label"></label>
                    </div>
                </div>
                <p class="text-[#7F35B2] text-[14px] md:text-[16px] max-w-[430px] font-bold pl-2 sm:pl-4 special_health_title">
                    {{ App::getLocale() == 'en' ? $option['name_en'] : $option['name'] }}
                </p>
            </div>
            <div class="triangle-container">
                <div class="info-box">
                    <i class="fa-solid fa-circle-exclamation mr-1"></i>{{ __('contents.triangle_text') }}
                </div>
            </div>
            <p  class="w-[135px] option_price_display text-[#7F35B2] text-[15px] font-bold hidden md:block meal_ticket_fee desktop_meal_ticket" data-option-id="{{ $option['id'] }}">
                <!-- {{ number_format($option['priceBrut'], 0, ',', '.') }} TL -->
                    <span class="net_span">Net</span>: {{ number_format($option['priceNet'], 2, ',', '.') }} TL <br/>
                     <span class="brut_span">Brüt</span>: {{ number_format($option['priceBrut'], 2, ',', '.') }} TL</span>

            </p>
        </div>
        <div>

        </div>

        @if($option['has_details']=='1')
        <p class="text-[#2F2C31] text-[12px] md:text-[13px] pl-8 sm:pl-10 max-w-[725px] pt-1 pb-3 opacity-70 further_details" onclick="$('#futherDetailsModal{{$option['id']}}').removeClass('hidden').addClass('flex')">
            {!! App::getLocale() == 'en' ? strip_tags($option['description_en'],'<br><strong><em>') : strip_tags($option['description'],'<br><strong><em>') !!}
        </p>
        @else
        <p class="text-[#2F2C31] text-[12px] md:text-[13px] pl-8 sm:pl-10 max-w-[725px] py-3 further_details">
            {!! App::getLocale() == 'en' ? strip_tags($option['description_en'],'<br><strong><em>') : strip_tags($option['description'],'<br><strong><em>') !!}
        </p>
        @endif

        <p class="text-[#7F35B2] text-[15px] font-bold md:hidden pl-8 sm:pl-10 pb-3 meal_ticket_fee mobile_meal_ticket">
            <!-- {{ number_format($option['priceBrut'], 0) }} TL -->
            <span class="net_span">Net</span>: {{ number_format($option['priceNet'], 2, ',', '.') }} TL <br/>
            <span class="brut_span">Brüt</span>: {{ number_format($option['priceBrut'], 2, ',', '.') }} TL</span>
        </p>
    </div>





    {{-- BURASI ONA BAGLI OPTIONLAR --}}



    <div id="futherDetailsModal{{$option['id']}}" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 w-full h-full justify-center items-center z-[1000] hidden bg-[rgba(0,0,0,0.5)]">
        <div id="modalWh" class="flex flex-col items-center justify-center md:flex-row md:items-start md:justify-start w-full max-w-[500px] p-3 bg-white shadow-[0px_0px_20px_#00000017;] relative rounded-[10px] max-h-[550px]">
            <button type="button" class="bg-transparent rounded-lg  ml-auto inline-flex justify-center items-center absolute right-3 top-3 " onclick="$('#futherDetailsModal{{$option['id']}}').removeClass('flex').addClass('hidden')">
                <img id="closeImgBalance" src="{{ asset('assets/images/close.png') }}" alt="close" />
                <span class="sr-only">Close modal</span>
            </button>
            <div class="md:px-[1rem] lg:py-[1rem] px-1 max-h-[500px] overflow-y-auto details_inner_div w-full">
                <p class="text-[#2F2C31] max-w-[324px] md:max-w-[503px]  py-4 text-[14px]">
                    {!! App::getLocale() == 'en' ? strip_tags($option['details_en'], '<br><strong><em>') : strip_tags($option['details'], '<br><strong><em>') !!}
                </p>
            </div>
        </div>
    </div>
    @endforeach

    <div id="RulesModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 w-full h-full justify-center items-center z-[1000] hidden bg-black bg-opacity-50">
        <div id="modalWh" class="flex flex-col items-center justify-center md:flex-row md:items-start md:justify-start w-full max-w-[500px] p-3 bg-white shadow-[0px_0px_20px_#00000017;] relative rounded-[10px] max-h-[550px]">
            <button type="button" class="bg-transparent rounded-lg ml-auto inline-flex justify-center items-center absolute right-3 top-3" onclick="closeModalForRules()">
                <img id="closeImgRules" src="{{ asset('assets/images/close.png') }}" alt="close" />
                <span class="sr-only">Close modal</span>
            </button>
            <div class="md:px-[1rem] lg:py-[1rem] px-1 max-h-[500px] overflow-y-auto details_inner_div w-full">
                <p id="workRulesp" class="text-[#2F2C31] max-w-[324px] md:max-w-[503px] py-4 text-[14px]">

                </p>
            </div>
        </div>
    </div>


    <div id="RulesModal2" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 w-full h-full justify-center items-center z-[1000] hidden bg-black bg-opacity-50">
        <div id="modalWh" class="flex flex-col items-center justify-center md:flex-row md:items-start md:justify-start w-full max-w-[500px] p-3 bg-white shadow-[0px_0px_20px_#00000017;] relative rounded-[10px] max-h-[550px]">
            <button type="button" class="bg-transparent rounded-lg ml-auto inline-flex justify-center items-center absolute right-3 top-3" onclick="closeModalForRules()">
                <img id="closeImgRules" src="{{ asset('assets/images/close.png') }}" alt="close" />
                <span class="sr-only">Close modal</span>
            </button>
            <div class="md:px-[1rem] lg:py-[1rem] px-1 max-h-[500px] overflow-y-auto details_inner_div w-full">
                <p class="text-[#2F2C31] max-w-[324px] md:max-w-[503px] py-4 text-[14px]">
                    {!! App::getLocale() == 'en' ? "Whichever package the employee chooses, dependents (spouse and children) can choose a package with the same or lower value as that package. All dependents must be in the same Insurance Package in the selected low-amount package. For addicts, the I don't want option can be selected." : "Çalışan hangi paketi seçerse bağımlılar (eş ve çocuklar) o paketle aynı veya daha düşük tutarlı bir paketi seçebilir. Bağımlıların hepsi seçilen düşük tutarlı pakette de aynı Sigorta Paketinde olmak zorundadır. Bağımlılar için istemiyorum seçeneği seçilebilir. " !!}
                </p>
            </div>
        </div>
    </div>


@if(App::getLocale() == 'en' ? $benefit['alert_message_en'] : $benefit['alert_message'])
    @php
    $colorLang = App::getLocale() == 'en' ? 'alert_color_en' : 'alert_color';
    $bgColorLang = App::getLocale() == 'en' ? 'alert_bg_color_en' : 'alert_bg_color';
    $color = match($benefit[$colorLang]){
    "red" => 'red',
    "green" => 'green',
    default => 'text-[#7F35B2]',
    };
    $bgColor = match($benefit[$bgColorLang]){
    "red" => 'bg-red-200',
    "green" => 'bg-green-200',
    default => 'bg-[#7f35b2]/[0.1]',
    };
    $lines = explode(chr(10), App::getLocale() == 'en' ? $benefit['alert_message_en'] : $benefit['alert_message']);
    @endphp
    @foreach($lines as $line)
    <div class="px-2 sm:px-5 pb-3">
        <p class="{{ $color ?? 'text-[#7F35B2]' }} {{ $bgColor ?? 'bg-[#7f35b2]/[0.1]' }} text-[12px] sm:text-[14px] p-3 sm:p-5 rounded-[10px] mt-5"> {!! strip_tags($line, '<br><strong><em>') !!} </p>
    </div>
    @endforeach
    @endif

    @php
        $prevId = $selecteds[$benefit['id']] ?? null;
        $prevOption = $prevId
            ? collect($benefit['options'])->firstWhere('id', $prevId)
            : null;


    @endphp
    <input type="hidden" name="rules" value="{{$rules}}">


    <input type="hidden" name="previous_option_id"
           value="{{ $prevId }}" />

    <input type="hidden" name="previous_fin_code"
           value="{{ $prevOption['benefit_fin_code'] ?? '' }}" />

    <input type="hidden" name="previous_fin_type"
           value="{{ $prevOption['benefit_fin_type'] ?? '' }}" />

    <input type="hidden" name="previous_fin_nr"
           value="{{ $prevOption['benefit_fin_nr'] ?? '' }}" />

    <input type="hidden" name="previous_brut_price"
           value="{{ $prevOption['priceBrut'] ?? '' }}" />

    <input type="hidden" name="benefit_bulutfin_mode" value="{{ $benefit['benefit_bulutfin_mode'] }}" />



</form>
<div class="fixed bg-black bg-opacity-50 w-full h-full top-0 left-0 z-40 hidden" id="triangle_overlay">

</div>

<div id="futherDetailsSecenekliModal{{$benefit['id']}}" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 w-full h-full justify-center items-center z-[1000] hidden bg-black bg-opacity-50">
    <div id="modalWh" class="flex flex-col items-center justify-center md:flex-row md:items-start md:justify-start w-full max-w-[500px] p-3 bg-white shadow-[0px_0px_20px_#00000017;] relative rounded-[10px] max-h-[550px]">
        <button type="button" class="bg-transparent rounded-lg  ml-auto inline-flex justify-center items-center absolute right-3 top-3 " onclick="$('#futherDetailsSecenekliModal{{$benefit['id']}}').removeClass('flex').addClass('hidden')">
            <img id="closeImgBalanceSecenekli" src="{{ asset('assets/images/close.png') }}" alt="close" />
            <span class="sr-only">Close modal</span>
        </button>
        <div class="md:px-[1rem] lg:py-[1rem] px-1 max-h-[500px] overflow-y-auto details_inner_div w-full">
            <p class="text-[#2F2C31] max-w-[324px] md:max-w-[503px]  py-4 text-[14px]">
                {!! App::getLocale() == 'en' ? strip_tags($benefit['details_en']) : strip_tags($benefit['details']) !!}
            </p>
        </div>
    </div>
</div>

<div id="componentRuleModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 w-full h-full justify-center items-center z-[1000] hidden bg-black bg-opacity-50">
    <div id="modalWh" class="flex flex-col items-center justify-center md:flex-row md:items-start md:justify-start w-full max-w-[500px] p-3 bg-white shadow-[0px_0px_20px_#00000017;] relative rounded-[10px] max-h-[550px]">
        <button type="button" class="bg-transparent rounded-lg  ml-auto inline-flex justify-center items-center absolute right-3 top-3 " onclick="$('#componentRuleModal').removeClass('flex').addClass('hidden'); location.reload();">
            <img id="closeImgcomponentRule" src="{{ asset('assets/images/close.png') }}" alt="close" />
            <span class="sr-only">Close modal</span>
        </button>
        <div class="md:px-[1rem] lg:py-[1rem] px-1 max-h-[500px] overflow-y-auto details_inner_div w-full">
            <p id="textComponentRule" class="text-[#2F2C31] max-w-[324px] md:max-w-[503px]  py-4 text-[14px]">
            </p>
            <div class="flex justify-center space-x-4 mt-5">
                <button type="button" id="resetCartModalComponent" class="bg-[#7F35B2] text-[#EFF1F3] w-full sm:max-w-[160px] h-[40.44px] rounded-md flex justify-center items-center negative-balance-btn">{{ __('contents.sepetiSıfırla') }}</button>
            </div>
        </div>
    </div>
</div>


<style>
    #loadingSpinner {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5); /* Yarı şeffaf siyah arka plan */
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .spinner {
        border: 8px solid #f3f3f3; /* Açık gri */
        border-top: 8px solid #3498db; /* Mavi */
        border-radius: 50%;
        width: 60px;
        height: 60px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

</style>

<div id="loadingSpinner" style="display: none;">
    <div class="spinner"></div>
</div>

<script src="{{ asset('assets/js/jquery.js') }}"></script>
<script>
    var brutLabel = "{!! __('contents.brut') !!}";
    var rules = "{{ $rules }}";
    var appLocale = "{{ App::getLocale() }}";


    $(document).ready(function() {
        var appLocale = "{{ App::getLocale() }}";



        $('#frmSecenekli_{{ $benefit["id"] }}').on('change', 'input[type="radio"][name="option_id"]', function () {
            if (this.checked) {
                var $form = $(this).closest('form');

                // Önceki seçili ID’yi al
                var prevId = $form.find('input[name="previous_option_id"]').val();

                // Önceki radio'yu bul
                var $prevRadio = $form.find('input[type="radio"][name="option_id"][value="' + prevId + '"]');


                // Seçim sonrası form submit
                $form.submit();
            }
        });


        $('#frmSecenekli_{{ $benefit["id"] }}').on('submit', function(e) {
            e.preventDefault();
            $('#loadingSpinner').show();
            var project_component="<?=$componentsValue?>";

            var optionId = $('input[type="radio"][name="option_id"]:checked', this).data('id');

            var $form     = $(this);
            var $selected = $form.find('input[type="radio"][name="option_id"]:checked');
            var finCode   = $selected.data('fin-code');
            var finType   = $selected.data('fin-type');
            var finNr     = $selected.data('fin-nr');
            var priceBrut = $selected.data('option-price') ?? 0;
            var priceNet  = $selected.data('price-net')  ?? 0;

            var prevOptionId  = $form.find('input[name="previous_option_id"]').val();
            var prevFinCode   = $form.find('input[name="previous_fin_code"]').val();
            var prevFinType   = $form.find('input[name="previous_fin_type"]').val();
            var prevFinNr     = $form.find('input[name="previous_fin_nr"]').val();
            var prevBrutPrice = $form.find('input[name="previous_brut_price"]').val();

            var formData = $form.serializeArray();
            formData.push({ name: 'benefit_fin_code',  value: finCode   });
            formData.push({ name: 'benefit_fin_type',  value: finType   });
            formData.push({ name: 'benefit_fin_nr',    value: finNr     });
            formData.push({ name: 'price_brut',        value: priceBrut });
            formData.push({ name: 'price_net',         value: priceNet  });
            formData.push({ name: 'previous_brut_price',         value: prevBrutPrice  });


            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                data: formData,
                // processData: false,
                // contentType: false,
                success: function(response) {


                    if (response.success) {
                        if (response.multiple) {
                            if (response.benefit_ids.length === response.benefit_options_ids.length) {

                                $.each(response.benefit_ids, function(index, benefitId) {
                                    var optionId = response.benefit_options_ids[index];

                                    var form = $('#frmSecenekli_' + benefitId);

                                    var radioButton = form.find('input[type="radio"][name="option_id"][data-id="' + optionId + '"]');

                                    if (radioButton.length) {
                                        radioButton.prop('checked', true);

                                        radioButton.trigger('change');
                                    } else {

                                        $('#loadingSpinner').hide();
                                        updateInitData();

                                        console.error('Radio buton bulunamadı: benefitId:', benefitId, 'optionId:', optionId);
                                    }
                                });
                            } else {
                                console.error('benefit_ids ve benefit_option_ids dizileri aynı uzunlukta değil!');
                            }
                        } else {
                            $('#loadingSpinner').hide();
                            updateInitData();
                            const $form = $('#frmSecenekli_{{ $benefit["id"] }}');
                            const $selected = $form.find('input[type="radio"][name="option_id"]:checked');

                            // Yeni değerleri al
                            const newFinCode = $selected.data('fin-code');
                            const newFinType = $selected.data('fin-type');
                            const newFinNr   = $selected.data('fin-nr');

                            const newOptionId = $selected.val();

                            // Hidden input'ları güncelle
                            $form.find('input[name="previous_option_id"]').val(newOptionId);
                            $form.find('input[name="previous_fin_code"]').val(newFinCode);
                            $form.find('input[name="previous_fin_type"]').val(newFinType);
                            $form.find('input[name="previous_fin_nr"]').val(newFinNr);
                        }
                    }  else {
                        $('#loadingSpinner').hide();
                        if(project_component=="1" && !response.workRules){
                            if(response.ruleMatch){
                                $('#componentRuleModal').removeClass('hidden').addClass('flex');
                                if (appLocale == 'en') {
                                    $('#textComponentRule').text(response.message_en);
                                }else{
                                    $('#textComponentRule').text(response.message);
                                }
                            }else{
                                $('#MinMax').removeAttr("style").removeClass("hidden").addClass("flex");
                                if (appLocale == 'en') {
                                    $('#textMinMax').text(response.message_en);
                                } else {
                                    $('#textMinMax').text(response.message);
                                }
                            }
                        }

                        if(response.workRules){
                            $('#RulesModal').removeClass('hidden').addClass('flex');
                            if (appLocale == 'en') {
                                $('#workRulesp').text(response.message_en);
                            } else {
                                $('#workRulesp').text(response.message);
                            }
                        }
                    }
                },
                error: function(xhr) {
                    alert('Hata oluştu!');
                }
            });
        });


        function addNewBenefits(groupedBenefits, userInfo) {

            $.each(groupedBenefits, function(groupId, benefits) {
                var groupElement = $('.accordion_div[data-group-id="' + groupId + '"]');

                if (groupElement.length === 0) {

                    var newGroupHTML = `
    <div class="accordion_div px-2 bg-[#48106D]/[0.04] border-b border-slate-300/[0.5] overflow-hidden" data-group-id="${groupId}">
        <div class="accordion_btn flex justify-between items-center font-semibold cursor-pointer py-1">
            <p class="text-[#7F35B2] text-[10px] max-w-[195px]"></p>
            <div class="flex items-center justify-between py-1">
                <span class="mr-1 text-[#5F5F5F] text-[10px] company_car_total" id="company_car_total"> TL</span>
                <img src="{{ asset('assets/images/select_arrow-down.png') }}" alt="arrow" class="max-w-[11px] white-arrow" />
            </div>
        </div>
`; // Note: The closing </div> for the group is removed

                    $('#groupsContainer').append(newGroupHTML);
                    groupElement = $('.accordion_div[data-group-id="' + groupId + '"]');

                }

                $.each(benefits, function(index, benefit) {
                    groupElement.find('.accordion_item').removeClass('max-h-0').addClass('max-h-fit');

                    var benefitElement = groupElement.find('.accordion_item[data-benefit-id="' + benefit.id + '"]');

                    if (benefitElement.length === 0) {
                        var benefitName = appLocale == 'en' ? benefit.name_en : benefit.name;
                        var newBenefitHTML = `
        <div class="accordion_item max-h-fit" data-benefit-id="${benefit.id}">
            <div class="pb-2 flex justify-between items-center cart_item">
                <div>
                    <p class="text-black text-[10px] font-semibold">${benefitName}</p>
                    <p class="text-[#5F5F5F] text-[10px]">
                        <span class="net">Net: ${benefit.netPrice} TL</span> -
                        <span class="brut">${brutLabel}: ${benefit.brutPrice} TL</span>
                    </p>
                </div>
            </div>
        </div>
    `;
                        groupElement.append(newBenefitHTML);
                    }
                });

                if (groupElement.length === 0) {
                    $('#groupsContainer').append('</div>');
                }

                if(groupId==19){
                    groupElement.find('.accordion_item').each(function() {
                        $(this).removeClass('max-h-0').addClass('max-h-fit');
                    });
                }

            });
        }


        function updateInitData() {
            $('#loadingSpinner').show();
            $.get('/get-init-data', function (response) {
                processResponse(response);

                // Bakiye
                const balance = parseFloat(response.balance?.balance ?? 0);
                const brutBalance = parseFloat(response.balance?.brut_balance ?? 0);
                $('#balanced').text(balance.toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + ' TL')
                    .toggleClass('negative-balance', balance < 0);
                $('#alısverisi_tamamla').toggleClass('negative-balance-btn', balance < 0);

                $('#balanced_brut').text(brutBalance.toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + ' TL')
                    .toggleClass('negative-balance', brutBalance < 0);
                // Benefit güncellemeleri
                const groupedBenefits = response.grouped_benefits;
                const userInfo = response.user_info;
                const totalPricesByGroup = response.totalPricesByGroup;

                $.each(groupedBenefits, function (groupId, benefits) {
                    const totalPrice = totalPricesByGroup[groupId];
                    const groupSelector = `.accordion_div[data-group-id="${groupId}"]`;

                    $(`${groupSelector} .accordion_btn p`).text(appLocale === "en" ? response.groupNamesEng[groupId] : response.groupNames[groupId]);
                    $(`${groupSelector} .company_car_total`).text(
                        parseFloat(totalPrice).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + ' TL'
                    );

                    benefits.forEach(function (benefit) {
                        const net = parseFloat(benefit.net_price ?? benefit.priceNet ?? 0) || 0;
                        const brut = parseFloat(benefit.brut_price ?? benefit.priceBrut ?? 0) || 0;

                        const formattedNet = parseFloat(net).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                        const formattedBrut = parseFloat(brut).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });

                        // Sepet
                        const benefitElement = $(`.accordion_item[data-benefit-id="${benefit.id}"]`);
                        benefitElement.find('.net').text('Net: ' + formattedNet + ' TL');
                        benefitElement.find('.brut').text(`${brutLabel}: ${formattedBrut} TL`);

                        // Form
                        if (benefit.option_id) {
                            const $priceBox = $(`.option_price_display[data-option-id="${benefit.option_id}"]`);
                            if ($priceBox.length) {
                                $priceBox.html(`
                                <span class="net_span">Net</span>: ${formattedNet} TL <br/>
                                <span class="brut_span">Brüt</span>: ${formattedBrut} TL
                            `);
                            }
                        }

                        const $radio = $(`input[type="radio"][name="option_id"][data-id="${benefit.option_id}"]`);

                        if ($radio.length) {
                            // Data attribute'ları güncelle
                            $radio.attr('data-option-price', benefit.brut_price ?? 0);
                            $radio.attr('data-price-net', benefit.net_price ?? 0);

                            // Desktop görünüm (option_price_display)
                            const $desktop = $(`.option_price_display[data-option-id="${benefit.option_id}"]`);
                            $desktop.html(`
        <span class="net_span">Net</span>: ${formattedNet} TL <br/>
        <span class="brut_span">Brüt</span>: ${formattedBrut} TL
    `);

                            const $mobile = $(`.mobile_meal_ticket[data-option-id="${benefit.option_id}"]`);
                            if ($mobile.length) {
                                $mobile.html(`
            <span class="net_span">Net</span>: ${formattedNet} TL <br/>
            <span class="brut_span">Brüt</span>: ${formattedBrut} TL
        `);
                            }
                        }
                    });
                });

                $('#loadingSpinner').hide();
            });
        }
    });



    function processResponse(response) {
        var final_benefits = response.final_benefits || [];

        var benefitToRemoveId = null;
        var group14RemoveIds = [];
        var benefitToRemoveIds = [];
        var benefitToRemoveIdsCustom = [];

        var allGroup18Zero = true;
        var allGroup19Zero = true;

        final_benefits.forEach(function (benefit) {
            var priceNet = benefit.priceNet;

            var amountZero = priceNet == null || priceNet === 0;

            switch (benefit.benefit_group_id) {
                case 15:
                    if (amountZero) benefitToRemoveId = benefit.id;
                    break;

                case 14:
                    if (benefit.benefit_id != 151 && amountZero) {
                        group14RemoveIds.push(benefit.id);
                    }
                    break;

                case 18:
                    if (amountZero) {
                        benefitToRemoveIds.push(benefit.id);
                    } else {
                        allGroup18Zero = false;
                    }
                    break;

                case 19:
                    if (amountZero) {
                        benefitToRemoveIdsCustom.push(benefit.id);
                    } else {
                        allGroup19Zero = false;
                    }
                    break;
            }
        });

        if (benefitToRemoveId !== null) {
            $('div[data-benefit-id="' + benefitToRemoveId + '"]').remove();
        }

        group14RemoveIds.forEach(id =>
            $('div[data-benefit-id="' + id + '"]').remove()
        );

        benefitToRemoveIds.forEach(id =>
            $('div[data-benefit-id="' + id + '"]').remove()
        );

        benefitToRemoveIdsCustom.forEach(id =>
            $('div[data-benefit-id="' + id + '"]').remove()
        );

        if (allGroup18Zero) {
            $('div.accordion_div[data-group-id="18"]').remove();
        }

        if (allGroup19Zero) {
            $('div.accordion_div[data-group-id="19"]').remove();
        }
    }

</script>
