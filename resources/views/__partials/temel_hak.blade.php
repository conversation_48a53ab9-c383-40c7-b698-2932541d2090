    <div
      class="bg-[#585858]/[0.1] py-5 px-2 sm:px-5 flex items-start justify-start gap-3 md:gap-4">
      <img src="{{ asset('storage/' . (App::getLocale() == 'en'  ? $benefit['image_en'] : $benefit['image'])) }}" alt="car" class="w-[70px] sm:w-[130px]" />
      <div class="w-full">
        <h1 class="flex justify-between pb-2">
          <span class="text-[12px] md:text-[15px] font-semibold text-[#707070] core_benefit_text"> {{ App::getLocale() == 'en' ? $benefit['name_en'] : $benefit['name'] }}</span>
          <span class="text-[12px] md:text-[15px] font-semibold text-[#707070] car_value">
               <span class="net_brut_span">Net</span>: {{ number_format($benefit['priceNet'], 2, ',', '.') }} TL  <br>
                 <span class="net_brut_span">Brüt</span>: {{ number_format($benefit['priceBrut'], 2, ',', '.') }} TL
          </span>
        </h1>
        <div class="flex items-center justify-between sirket_araci_container">
          <p class="max-w-[410px] inline-block text-[12px] md:text-[13px] text-[#707070] leading-[17px] temel_hak_p">
        {!! App::getLocale() == 'en' ? strip_tags($benefit['description_en'], '<br><strong><em>') : strip_tags($benefit['description'], '<br><strong><em>') !!}
          </p>

          <div
            class="checkbox-container w-[23px] h-[23px] flex justify-center items-center relative z-[9] rounded-[5px] bg-[#707070]/[0.3]">
            <div class="relative w-[15px] h-[15px] flex justify-center items-center">
              <input type="checkbox" name="sirket_araci" id="sirket_araci{{$benefit['id']}}" value="sirket_araci"
              checked class="pointer-events-none"
                 onchange="updateCheckboxContainerBackground(this)" />
              {{-- <label for="sirket_araci{{$item->id}}"
                class="inline-block w-full h-full border border-slate-500 bg-[#707070] cursor-pointer rounded-sm custom-checkbox-label"></label> --}}
            </div>
          </div>
        </div>
        @if(!empty($benefit['desc_image']) || !empty($benefit['desc_image_en']))
                <img src="{{ asset('storage/' . (App::getLocale() == 'en'  ? $benefit['desc_image_en'] : $benefit['desc_image'])) }}"
                alt="hamburger" class="w-full mb-1"/>
              @endif
      </div>
    </div>
    <script src="{{ asset('assets/js/jquery.js') }}"></script>

