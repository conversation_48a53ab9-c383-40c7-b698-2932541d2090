<form action="{{ route('member_dashboard_addAction') }}" method="post" id="frmEsnekHak_{{ $benefit['id'] }}"
      class="frmEsnekHak" data-benefit_id="{{ $benefit['id'] }}" autocomplete="off">
    @csrf
    <div
        class="bg-white py-5 px-2 sm:px-5 flex items-start justify-start gap-3 md:gap-5 yan_hak_parent_div relative sm:static pb-[5rem] sm:py-5">
        <img src="{{ asset('storage/' . (App::getLocale() == 'en'  ? $benefit['image_en'] : $benefit['image'])) }}"
             alt="bp" class="w-[70px] sm:w-full sm:max-w-[130px] yan_hak_firsati_img"/>
        <div class="w-full">
            <h1 class="flex justify-between items-center md:pb-1">
                <span
                    class="text-[13px] md:text-[17px] font-bold text-[#7F35B2] yan_hak_tittle">{{ App::getLocale() == 'en' ? $benefit['name_en'] : $benefit['name'] }}</span>
            </h1>


            <p class="text-[12px] md:text-[13px] text-[#707070]" id="esnekA">
                {!! App::getLocale() == 'en' ? strip_tags($benefit['description_en'], '<a><strong><br>') : strip_tags($benefit['description'], '<a><strong><br>') !!}
            </p>
            <br>
            @if(($benefit['has_details'])==1)
                <p class="text-[12px] md:text-[13px] further_details_esnek cursor-pointer text-[#7F35B2]"
                   onclick="$('#futherDetailsEsnekModal{{$benefit['id']}}').removeClass('hidden').addClass('flex')"> {{ __('contents.detailInfo') }}
            @endif
            <div class="absolute flex gap-1 items-center justify-center left-0 px-2 sm:gap-2 sm:justify-end sm:px-0 sm:static w-full">
                <p class="px-1 sm:max-w-[156px] text-[#7F35B2] text-[14px] text-center w-full">Brüt Tutar</p>
                <p class="px-1 sm:max-w-[156px] text-[#7F35B2] text-[14px] text-center w-full">Net Tutar</p>
                <button type="submit" class="bg-[#7F35B2] flex h-[39px] invisible items-center justify-center rounded-[5px] sm:max-w-[156px] sub text-[#EFF1F3] text-[13px] w-full"> {{ __('contents.update') }} </button>
            </div>
            <div
                class="flex justify-center sm:justify-end items-center gap-1 sm:gap-2 absolute sm:static left-0 w-full px-2 sm:px-0">
                @php
                    $isBulutfinNet = isset($benefit['benefit_bulutfin_mode']) && $benefit['benefit_bulutfin_mode'] == 1;

                 $priceBrutValue = $values[$benefit['id']]['priceBrut'] ?? ($benefit['priceBrut'] ?? 0);
                 $priceNetValue  = $values[$benefit['id']]['priceNet']  ?? ($benefit['priceNet'] ?? 0);

                 $userValueBrut = number_format(round($priceBrutValue, 2), 2, ',', '.') . ' TL';
                 $userValueNet  = number_format(round($priceNetValue, 2), 2, ',', '.') . ' TL';

                 $brutSystemValue = number_format(round($benefit['priceBrut'] ?? 0, 2), 2, ',', '.') . ' TL';
                 $netSystemValue  = number_format(round($benefit['priceNet']  ?? 0, 2), 2, ',', '.') . ' TL';

                @endphp

                {{-- BRUT FIELD --}}
                <input
                    type="text"
                    id="birey_field{{ $benefit['id'] }}_brut"
                    name="birey_field_brut"
                    placeholder="{{ $brutSystemValue }}"
                    class="border w-full sm:max-w-[156px] h-[39px] text-center rounded-[5px] text-[14px]
    {{ $isBulutfinNet ? 'border-[#E4E6E8] outline-none' : 'border-[#7F35B2] outline-none ring-1 ring-[#7F35B2]' }}"
                    value="{{ $isBulutfinNet ? $brutSystemValue : $userValueBrut }}"
                    oninput="validateInput(this)"
                    {{ $isBulutfinNet ? 'readonly disabled' : '' }}
                />

                {{-- NET FIELD --}}
                <input
                    type="text"
                    id="birey_field{{ $benefit['id'] }}"
                    name="birey_field"
                    placeholder="{{ $netSystemValue }}"
                    class="border w-full sm:max-w-[156px] h-[39px] text-center rounded-[5px] text-[14px]
    {{ $isBulutfinNet ? 'border-[#7F35B2] focus:outline-none ring-1 focus:ring-[#7F35B2]' : 'border-[#E4E6E8] outline-none' }}"
                    value="{{ $isBulutfinNet ? $netSystemValue : $netSystemValue }}"
                    oninput="validateInput(this)"
                    {{ $isBulutfinNet ? '' : 'readonly disabled' }}
                />
                <input type="hidden" name="benefit_id" value="{{ $benefit['id'] }}">

                <input type="hidden" name="benefit_fin_code" value="{{ $benefit['benefit_fin_code'] }}" />

                <input type="hidden" name="benefit_bulutfin_mode" value="{{ $benefit['benefit_bulutfin_mode'] }}" />

                <button type="submit"
                        class="bg-[#7F35B2] text-[#EFF1F3] flex justify-center items-center w-full sm:max-w-[156px] h-[39px] rounded-[5px] text-[13px] sub"> {{ __('contents.update') }} </button>
            </div>

        </div>
    </div>
    @if(!empty($benefit['desc_image']) || !empty($benefit['desc_image_en']))
        <img
            src="{{ asset('storage/' . (App::getLocale() == 'en'  ? $benefit['desc_image_en'] : $benefit['desc_image'])) }}"
            alt="hamburger" class="w-full mb-1"/>
    @endif


</form>
<!-- number_format($values[$benefit['id']] ?? 0, 2) -->


@if(App::getLocale() == 'en' ? $benefit['alert_message_en'] : $benefit['alert_message'])
    @php
        $colorLang = App::getLocale() == 'en' ? 'alert_color_en' : 'alert_color';
        $bgColorLang = App::getLocale() == 'en' ? 'alert_bg_color_en' : 'alert_bg_color';
        $color = match($benefit[$colorLang]){
        "red" => 'red',
        "green" => 'green',
        default => 'text-[#7F35B2]',
        };
        $bgColor = match($benefit[$bgColorLang]){
        "red" => 'bg-red-200',
        "green" => 'bg-green-200',
        default => 'bg-[#7f35b2]/[0.1]',
        };
        $lines = explode(chr(10), App::getLocale() == 'en' ? $benefit['alert_message_en'] : $benefit['alert_message']);
    @endphp

        <!-- foreach i p nin içine aldım -->
    <div class="px-2 sm:px-5 pb-3">
        <p class="{{ $color ?? 'text-[#7F35B2]' }} {{ $bgColor ?? 'bg-[#7f35b2]/[0.1]' }} text-[12px] sm:text-[14px] p-3 sm:p-5 rounded-[10px]"
           id="pink_area">
            @foreach($lines as $line)
                {!! strip_tags($line, '<br><strong><em>') !!}
            @endforeach
        </p>
    </div>
@endif

<div id="futherDetailsEsnekModal{{$benefit['id']}}" tabindex="-1" aria-hidden="true"
     class="fixed top-0 left-0 w-full h-full justify-center items-center z-[1000] hidden bg-black bg-opacity-50">
    <div id="modalWh"
         class="flex flex-col items-center justify-center md:flex-row md:items-start md:justify-start w-full max-w-[500px] p-3 bg-white shadow-[0px_0px_20px_#00000017;] relative rounded-[10px] max-h-[550px]">
        <button type="button"
                class="bg-transparent rounded-lg  ml-auto inline-flex justify-center items-center absolute right-3 top-3 "
                onclick="$('#futherDetailsEsnekModal{{$benefit['id']}}').removeClass('flex').addClass('hidden')">
            <img id="closeImgBalance" src="{{ asset('assets/images/close.png') }}" alt="close"/>
            <span class="sr-only">Close modal</span>
        </button>
        <div class="md:px-[1rem] lg:py-[1rem] px-1 max-h-[500px] overflow-y-auto details_inner_div w-full">
            <p class="text-[#2F2C31] max-w-[324px] md:max-w-[503px]  py-4 text-[14px]">
                {!! App::getLocale() == 'en' ? strip_tags($benefit['details_en'],'<br><strong><em>') : strip_tags($benefit['details'],'<br><strong><em>') !!}
            </p>
        </div>
    </div>
</div>

<style>
    #loadingSpinner {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5); /* Yarı şeffaf siyah arka plan */
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .spinner {
        border: 8px solid #f3f3f3; /* Açık gri */
        border-top: 8px solid #3498db; /* Mavi */
        border-radius: 50%;
        width: 60px;
        height: 60px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

</style>

<div id="loadingSpinner" style="display: none;">
    <div class="spinner"></div>
</div>
<script src="{{ asset('assets/js/jquery.js') }}"></script>
<script>
    var brutLabel = "{!! __('contents.brut') !!}";
    $(document).ready(function () {

        var appLocale = "{{ App::getLocale() }}";

        $('#frmEsnekHak_{{ $benefit["id"] }}').on('submit', function (e) {
            e.preventDefault();
            $('#loadingSpinner').show();
            const input = $('#birey_field{{ $benefit["id"] }}');
            if (input.length) {
                let plainValue = input.val().replace(/\.(?=\d{3}(,|$))/g, '').replace(',', '.');

                if (plainValue.length <= 0) {
                    input.val(0);
                } else {
                    if (plainValue.includes('.')) {
                        plainValue = parseFloat(plainValue).toFixed(2);
                    } else {
                        plainValue = parseInt(plainValue);
                    }
                }

                input.val(plainValue);
            }

            function updateInitDataCustom() {
                $('#loadingSpinner').show();

                $.ajax({
                    url: '/get-init-data',
                    method: 'GET',
                    success: function (response) {
                        processResponseCustom(response);

                        let balanceValue = 0;
                        if (typeof response.balance === 'object' && response.balance !== null) {
                            balanceValue = parseFloat(response.balance.balance ?? 0);
                        } else if (typeof response.balance === 'number') {
                            balanceValue = parseFloat(response.balance);
                        }

                        let brutbalanceValue = 0;
                        if (typeof response.balance.brut_balance === 'object' && response.balance.brut_balance !== null) {
                            brutbalanceValue = parseFloat(response.balance.brut_balance ?? 0);
                        } else if (typeof response.balance.brut_balance === 'number') {
                            brutbalanceValue = parseFloat(response.balance.brut_balance);
                        }

                        const formattedBalance = balanceValue.toLocaleString('tr-TR', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        });

                        const formattedBalanceBrut = brutbalanceValue.toLocaleString('tr-TR', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        });

                        $('#balanced').text(formattedBalance + ' TL').toggleClass('negative-balance', balanceValue < 0);
                        $('#balanced_brut').text(formattedBalanceBrut + ' TL').toggleClass('negative-balance', brutbalanceValue < 0);
                        $('#alısverisi_tamamla').toggleClass('negative-balance-btn', balanceValue < 0);

                        // 💡 Grup ve fayda güncellemeleri
                        const groupedBenefits = response.grouped_benefits;
                        const totalPricesByGroup = response.totalPricesByGroup;
                        const userInfo = response.user_info;

                        addNewBenefitsCustom(groupedBenefits, userInfo, totalPricesByGroup);

                        Object.entries(totalPricesByGroup).forEach(([groupId, total]) => {
                            const totalFormatted = parseFloat(total).toLocaleString('tr-TR', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            });

                            const $groupTotal = $(`.accordion_div[data-group-id="${groupId}"] .company_car_total`);
                            if ($groupTotal.length) {
                                $groupTotal.text(`${totalFormatted} TL`);
                            }
                        });

                        response.final_benefits.forEach(function (benefit) {
                            const fNet = parseFloat(benefit.priceNet || 0).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + ' TL';
                            const fBrut = parseFloat(benefit.priceBrut || 0).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + ' TL';

                            const $brutAcc = $(`#birey_field${benefit.id}_brut`);
                            const $netAcc = $(`#birey_field${benefit.id}`);
                            const $brutForm = $(`#frmEsnekHak_${benefit.id} #birey_field${benefit.id}_brut`);
                            const $netForm = $(`#frmEsnekHak_${benefit.id} #birey_field${benefit.id}`);

                            const updates = [
                                { $el: $brutAcc, val: fBrut, disable: benefit.benefit_bulutfin_mode == 1 },
                                { $el: $netAcc, val: fNet, disable: benefit.benefit_bulutfin_mode != 1 },
                                { $el: $brutForm, val: fBrut, disable: benefit.benefit_bulutfin_mode == 1 },
                                { $el: $netForm, val: fNet, disable: benefit.benefit_bulutfin_mode != 1 },
                            ];

                            updates.forEach(function (item) {
                                if (!item.$el.length) return;
                                item.$el
                                    .val(item.val)
                                    .attr('value', item.val)
                                    .attr('placeholder', item.val)
                                    .prop('readonly', item.disable)
                                    .prop('disabled', item.disable);
                            });

                            const $cardNet = $(`.accordion_item[data-benefit-id="${benefit.id}"] .net`);
                            const $cardBrut = $(`.accordion_item[data-benefit-id="${benefit.id}"] .brut`);
                            if ($cardNet.length) $cardNet.text('Net: ' + fNet);
                            if ($cardBrut.length) $cardBrut.text(`${brutLabel}: ${fBrut}`);
                        });

                        $('#loadingSpinner').hide();
                    },
                    error: function () {
                        alert('Hata oluştu!');
                        $('#loadingSpinner').hide();
                    }
                });
            }

            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                data: $(this).serialize(),
                success: function (response) {
                    if (response.success) {
                        $('#loadingSpinner').hide();
                        updateInitDataCustom();
                    } else {
                        $('#loadingSpinner').hide();
                        $('#MinMax').removeAttr("style").removeClass("hidden").addClass("flex");

                        if (appLocale == 'en') {
                            $('#textMinMax').text(response.message_en);
                        } else {
                            $('#textMinMax').text(response.message);
                        }
                    }
                },
                error: function (xhr) {
                    $('#loadingSpinner').hide();
                    alert('Hata oluştu!');
                }
            });
        });

        function addNewBenefitsCustom(groupedBenefits, userInfo, totalPricesByGroup) {
            $.each(groupedBenefits, function (groupId, benefits) {
                var groupElement = $('.accordion_div[data-group-id="' + groupId + '"]');

                if (groupElement.length === 0) {
                    const firstBenefit = benefits[0];
                    const groupName = appLocale === 'en' ? firstBenefit.group_name_en : firstBenefit.group_name;
                    const totalPrice = parseFloat(totalPricesByGroup?.[groupId] ?? 0).toLocaleString('tr-TR', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });

                    var newGroupHTML = `
                <div class="accordion_div px-2 bg-[#48106D]/[0.04] border-b border-slate-300/[0.5] overflow-hidden" data-group-id="${groupId}">
                    <div class="accordion_btn flex justify-between items-center font-semibold cursor-pointer py-1">
                        <p class="text-[#7F35B2] text-[10px] max-w-[195px]">${groupName}</p>
                        <div class="flex items-center justify-between py-1">
                            <span class="mr-1 text-[#5F5F5F] text-[10px] company_car_total" id="company_car_total">${totalPrice} TL</span>
                            <img src="{{ asset('assets/images/select_arrow-down.png') }}" alt="arrow" class="max-w-[11px] white-arrow" />
                        </div>
                    </div>
            `;
                    $('#groupsContainer').append(newGroupHTML);
                    groupElement = $('.accordion_div[data-group-id="' + groupId + '"]');
                }

                $.each(benefits, function (index, benefit) {
                    var benefitElement = groupElement.find('.accordion_item[data-benefit-id="' + benefit.id + '"]');
                    if (benefit.group_id == 15) {
                        benefitElement.removeClass('max-h-0').addClass('max-h-fit');
                    }

                    if (benefitElement.length === 0) {
                        var benefitName = appLocale == 'en' ? benefit.name_en : benefit.name;
                        var newBenefitHTML = `
                    <div class="accordion_item max-h-fit" data-benefit-id="${benefit.id}">
                        <div class="pb-2 flex justify-between items-center cart_item">
                            <div>
                                <p class="text-black text-[11px] font-semibold">${benefitName}</p>
                                <p class="text-[#5F5F5F] text-[11px]">
                                    <span class="net">Net: ${benefit.priceNet} TL</span> -
                                    <span class="brut">${brutLabel}: ${benefit.priceBrut} TL</span>
                                </p>
                            </div>
                        </div>
                    </div>
                `;
                        groupElement.append(newBenefitHTML);
                    }
                });

                if (groupElement.length === 0) {
                    $('#groupsContainer').append('</div>');
                }
            });
        }
        function processResponseCustom(response) {
            var final_benefits = response.final_benefits;
            var benefitsToRemove = [];

            for (var i = 0; i < final_benefits.length; i++) {
                var benefit = final_benefits[i];

                if (benefit.group_id == 15) {
                    var net = parseFloat(benefit.priceNet);

                    // priceNet yoksa veya 0'dan küçükse sepete alma
                    if (isNaN(net) || net <= 0) {
                        benefitsToRemove.push(benefit.id);
                    }
                }
            }

            function groupShouldDelete(final_benefits) {
                for (var i = 0; i < final_benefits.length; i++) {
                    var b = final_benefits[i];
                    if (b.group_id == 15) {
                        var net = parseFloat(b.priceNet);
                        if (!isNaN(net) && net > 0) {
                            return false;
                        }
                    }
                }
                return true;
            }

            if (groupShouldDelete(final_benefits)) {
                $('div[data-group-id="15"]').remove();
            } else if (benefitsToRemove.length > 0) {
                benefitsToRemove.forEach(function (id) {
                    $('div[data-benefit-id="' + id + '"]').remove();
                });
            }
        }
        function validateInputA(input) {
            input.value = input.value.replace(/[^0-9.,]/g, '');
            input.value = input.value.replace(/([.,])[.,]+/g, '$1');
            if (input.value.indexOf('.') !== -1) {
                const dotIndex = input.value.indexOf('.');
                input.value = input.value.slice(0, dotIndex + 1) + input.value.slice(dotIndex + 1).replace('.', '');
            }
            if (input.value.indexOf(',') !== -1) {
                const commaIndex = input.value.indexOf(',');
                input.value = input.value.slice(0, commaIndex + 1) + input.value.slice(commaIndex + 1).replace(',', '');
            }
        }

        $('#esnekA a').attr('target', '_blank');
    });
</script>
