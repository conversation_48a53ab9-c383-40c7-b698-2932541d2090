@extends('theme.layout')
@section('title')
{{ __('contents.menuHome') }}
@endsection @section('content')
    @php
        $tt = number_format($grand_total, 2, '.', ',');
        $tt = explode('.', $tt);

        $rules = $customSettings->getText('rules');
    @endphp




<main class="main_login bg-[#FBFAFC] flex justify-center items-center pt-[3rem] pb-[5rem] sm:px-[2rem] px-3">
    <div class="container m-auto  flex items-start justify-between gap-6 max-w-[960px]">
        <section class="w-full max-w-[679px] m-auto xl:m-0">

            <!-- SLIDERS -->
            <div id="default-carousel" class="relative w-full" data-carousel="slide" data-carousel-interval="6000">
                <!-- Carousel wrapper -->
                <div class="relative h-72 overflow-hidden rounded-lg md:h-96 z-[1]" id="slider_parent_div">
                    <!-- Item 1 -->
                    @foreach ($sliders as $group)
                    <div class=" duration-[2s] ease-in-out slide_1_div" data-carousel-item>
                        <img src="{{ asset('storage/' . (App::getLocale() == 'en' ? $group->image_en : $group->image)) }}" class="absolute block w-full h-full object-cover -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2" alt="slider-img">
                        <p class="absolute bottom-[30%] text-3xl md:text-4xl font-bold text-white slider_title">
                            {{ App::getLocale() == 'en' ? $group->name_en : $group->name }}
                        </p>
                    </div>
                    @endforeach
                    <!-- <div class="hidden duration-[2s] ease-in-out slide_2_div" data-carousel-item><img src="images/Slide-2.png" class="absolute block w-full h-full object-cover -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2"
                                                                          alt="slider-img"><p class="absolute left-[10%] bottom-[25%] text-3xl md:text-4xl font-bold text-white">Senin <br/>hakların <br/>senin kararın</p></div><div class="hidden duration-[2s] ease-in-out slide_3_div" data-carousel-item><img src="images/Slide-3.png" class="absolute block w-full h-full object-cover -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2"
                                                                          alt="slider-img"><p class="absolute left-[10%] bottom-[25%] text-3xl md:text-4xl font-bold text-white">Haklarını sen seç,<br/> istediğin gibi<br/> kullan</p></div><div class="hidden duration-[2s] ease-in-out slide_4_div" data-carousel-item><img src="images/Slide-4.png" class="absolute block w-full h-full object-cover -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2"
                                                                          alt="slider-img"><p class="absolute left-[10%] bottom-[25%] text-3xl md:text-4xl font-bold text-white">Sen <br/>ihtiyacını söyle,<br/> biz hakkını verelim</p></div> -->
                </div>
                <button type="button" class="absolute top-0 left-[-15px] z-[1] flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none" data-carousel-prev>
                    <span class="inline-flex items-center justify-center w-10 h-10 rounded-full">
                        <svg class="w-5 h-5 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4" />
                        </svg>
                        <span class="sr-only">Previous</span>
                    </span>
                </button>
                <button type="button" class="absolute top-0 right-[-15px] z-[1] flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none" data-carousel-next>
                    <span class="inline-flex items-center justify-center w-10 h-10 rounded-full">
                        <svg class="w-5 h-5 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="sr-only">Next</span>
                    </span>
                </button>
            </div>
            <!-- SLIDERS -->

            @foreach ($benefits as $key => $group)
            @if (isset($group['benefits']))
            <div class="accordion_div mt-8 shadow-[0px_0px_10px_#00000010] rounded-br-[10px] rounded-bl-[10px]" id="accordion{{ $group['id'] }}">
                <div class="flex justify-between items-center py-3 px-2 sm:px-5 bg-[#7F35B2] rounded-tl-[10px] rounded-tr-[10px] cursor-pointer accordion_btn w-full acorD">
                    <div class="flex justify-start items-center w-full">
                        @if (isset($group['group_image']) && !empty($group['group_image']))
                        <img src="{{ asset('storage/' . $group['group_image']) }}" alt="lock" class="w-full max-w-[25px]" />
                        @else
                        <img src="{{ asset('storage/' . $group['image']) }}" alt="lock" class="w-full max-w-[25px]" />
                        @endif
                        <span class="text-white text-[15px] md:text-[17px] font-semibold pl-3">{{ App::getLocale() == 'en' ? $group['name_en'] : $group['name'] }}</span>
                    </div>
                    <button>
                        <img src="{{ asset('assets/images/white_arrow.png') }}" alt="arrow" class="white-arrow rotate-0" />
                    </button>
                </div>

                <div class="accordion_item max-h-fit transition-all duration-700 ease-linear">
                    @if ($group['id'] == 1)
                    <p class="bg-white px-2 md:px-5 py-5 text-[13px] md:text-[14px] font-normal text-[#2F2C31]">
                        {!! App::getLocale() == 'en' ? strip_tags($group['description_en']) : strip_tags($group['description']) !!}
                    </p>
                    @endif

                    @foreach ($group['benefits'] as $benefit)
                    @if ($benefit['type'] == 1)
                    @include('__partials/temel_hak', ['benefit' => $benefit])
                    @elseif ($benefit['type'] == 2)
                    @if ($benefit['has_flexible_price'])
                    @include('__partials/esnek_hak', ['benefit' => $benefit])
                    @else
                    @include('__partials/secenekli_hak', ['benefit' => $benefit])
                    @endif
                    @elseif ($benefit['type'] == 3)
                    @if ($benefit['has_flexible_price'])
                    @include('__partials/esnek_hak', ['benefit' => $benefit])
                    @else
                    @include('__partials/secenekli_hak', ['benefit' => $benefit])
                    @endif
                    @elseif ($benefit['type'] == 4)
                    @include('__partials/secenekli_hak', [
                    'benefit' => $benefit,
                    'ozelSaglik' => $ozelSaglik
                    ])
                    @elseif ($benefit['type'] == 5)
                    @include('__partials/izin_hak', ['benefit' => $benefit])
                    @endif

                    @if (isset($family_benefits[$benefit['id']]))
                    @foreach ($family_benefits[$benefit['id']] as $family_benefit)
                    @include('__partials/aile_secenekli_hak', [
                    'family_benefit' => $family_benefit,
                    ])
                    @endforeach
                    @endif
                    @endforeach

                </div>
            </div>
            @endif
            @endforeach
        </section>

        <!-- ******************************************************************************* -->
        @include('payment')
        <!-- -------------------------------MODAL ------------------------------------------->
        <div id="staticModal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-black bg-opacity-50 ">
            <div class="relative w-full max-w-[400px] md:max-w-[683px] max-h-full">
                <!-- Modal content -->
                <div class="relative rounded-lg shadow-[0px_0px_20px_#00000017] bg-white p-4">
                    <!-- Modal header -->
                    <div class="flex items-start justify-between rounded-t">
                        <button type="button" class="bg-transparent rounded-lg ml-auto inline-flex justify-center items-center" data-modal-hide="staticModal">
                            <img src="{{ asset('assets/images/close.png') }}" alt="close" />
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <!-- Modal body -->
                    <div class="flex flex-col md:flex-row justify-center md:justify-start items-center md:items-start gap-5">
                        <img src="{{ asset('assets/images/144x144px-5.png') }}" alt="lightening" />
                        <div>
                            <h1 class="text-[#7F35B2] text-[26px] font-semibold leading-[26px] text-center md:text-left">
                                {{ __('contents.alert') }}
                            </h1>
                            <p class="text-[#2F2C31] py-3 max-w-[400px] text-center md:text-left">
                                {{ __('contents.basketQuestion') }}
                            </p>
                            <div class="flex items-center justify-center md:justify-start space-x-2 pt-4 rounded-b ">
                                <button data-modal="staticModal" type="button" class="text-[#EFF1F3] bg-[#7F35B2] focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm text-center w-full max-w-[121px] h-[30px]" id="resetCartButton"> {{ __('contents.yes') }} </button>
                                <button data-modal-hide="staticModal" type="button" class="focus:outline-none rounded-lg text-sm font-medium w-full max-w-[121px] h-[30px] border border-[#7F35B2] text-[#7F35B2]">
                                    {{ __('contents.cancel') }} </button>
                            </div>
                        </div>
                    </div>
                    <!-- Modal footer -->
                </div>
            </div>
        </div>
        <!-- -------------------------------MODAL ------------------------------------------->
        <!-- -------------------------------MODAL TAMAMLA------------------------------------------->
        <div id="staticModalTamamla" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full modal_parent_div bg-black bg-opacity-50 ">
            <div class="relative w-full max-w-[400px] md:max-w-[683px] max-h-full" id="relativeModal">
                <!-- Modal content -->
                <div class="relative rounded-lg shadow-[0px_0px_20px_#00000017] bg-white p-4">
                    <!-- Modal header -->
                    <div class="flex items-start justify-between   rounded-t ">
                        <button type="button" class="bg-transparent rounded-lg  ml-auto inline-flex justify-center items-center ">
                            <img id="staticModalTamamlaImg" src="{{ asset('assets/images/close.png') }}" alt="close" class="close" />
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <!-- Modal body -->
                    <div class=" flex flex-col md:flex-row items-center justify-center md:justify-start md:items-start gap-5">
                        <img src="{{ asset('assets/images/144x144px-5.png') }}" alt="lightening" class="w-[80px] sm:w-[130px]">
                        <div>
                            <h1 class="text-[#7F35B2] text-[18px] md:text-[26px] font-semibold leading-[26px] text-center md:text-left">
                                {{ __('contents.alert') }}
                            </h1>
                            <p id="MemberCanNegative" class="text-[#2F2C31] py-3 max-w-[400px] text-[12px] md:text-[16px] text-center md:text-left">
                                {{ __('contents.basketQuestion2') }}
                            </p>
                            <div class="flex items-center justify-center md:justify-start space-x-2 pt-4 rounded-b">
                                <a id="sideBenefitsApproval" href="{{ route('side_benefits_approval') }}" type="button" class="text-[#EFF1F3] bg-[#7F35B2] focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm text-center w-full max-w-[121px] h-[30px] flex items-center justify-center">{{ __('contents.yes') }}
                                </a>
                                <button id="staticModalCloseBtn" type="button" class="focus:outline-none  rounded-lg text-sm font-medium w-full max-w-[121px] h-[30px] border border-[#7F35B2] text-[#7F35B2]">{{ __('contents.cancel') }}</button>
                            </div>
                        </div>
                    </div>
                    <!-- Modal footer -->
                </div>
            </div>
        </div>
        <!-- -------------------------------MODAL TAMAMLA------------------------------------------->

        <!-- -------------------------------MODAL MEMBER BALANCE -------------------------------->
        <div id="staticModalTamamlaBalance" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full modal_parent_div bg-black bg-opacity-50 justify-center items-center">
            <div class="relative w-full max-w-[400px] md:max-w-[683px] max-h-full" id="relativeModalBalance">
                <!-- Modal content -->
                <div class="relative rounded-lg shadow-[0px_0px_20px_#00000017] bg-white p-4">
                    <!-- Modal header -->
                    <div class="flex items-start justify-between   rounded-t ">
                        <button type="button" class="bg-transparent rounded-lg  ml-auto inline-flex justify-center items-center ">
                            <img src="{{ asset('assets/images/close.png') }}" alt="close" class="close-btn" id="closeImgBalance" />
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <!-- Modal body -->
                    <div class=" flex flex-col md:flex-row items-center justify-center md:justify-start  gap-5">
                        <img src="{{ asset('assets/images/144x144px-5.png') }}" alt="lightening" class="w-[80px] sm:w-[130px]">
                        <div class="flex flex-col justify-center items-center md:items-start">
                            <h3 id="canMemberBalance" class="text-[#7F35B2] text-[18px] md:text-[26px] font-semibold leading-[26px] text-center md:text-left">
                                {{ __('contents.harcayabileceginiz_tutar') }}
                            </h3>
                            <!-- <p
                                                                class="text-[#2F2C31] py-3 max-w-[400px] text-[12px] md:text-[16px] text-center md:text-left">
                                                                {{ __('contents.harcayabileceginiz_tutar_metin') }}</p> -->
                            <div class="flex items-center justify-center md:justify-start space-x-2 pt-4 rounded-b">
                                <!-- {{-- <a href=" side_benefits_approval.html" data-modal-hide="staticModalTamamlaBalance"
                                    type="button"
                                    class="text-[#EFF1F3] bg-[#7F35B2] focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm text-center w-full max-w-[121px] h-[30px] flex items-center justify-center">{{ __('contents.yes') }}
                                </a> --}} -->
                                <button id="balanceCloseBtn" type="button" class="focus:outline-none  rounded-lg text-sm font-medium w-full max-w-[121px] h-[30px] border border-[#7F35B2] text-[#7F35B2] px-3">
                                    {{ __('contents.back') }}</button>
                            </div>
                        </div>
                    </div>
                    <!-- Modal footer -->
                </div>
            </div>
        </div>
    </div>
</main>



<div id="MinMax" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full modal_parent_div bg-black bg-opacity-50 justify-center items-center">
    <div class="relative w-full max-w-[400px] md:max-w-[683px] max-h-full" id="relativeMinMax">
        <!-- Modal content -->
        <div class="relative rounded-lg shadow-[0px_0px_20px_#00000017] bg-white p-4">
            <!-- Modal header -->
            <div class="flex items-start justify-between   rounded-t ">
                <button type="button" class="bg-transparent rounded-lg  ml-auto inline-flex justify-center items-center ">
                    <img id="closeImgMinMax" src="{{ asset('assets/images/close.png') }}" alt="close" class="close">
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="flex flex-col md:flex-row items-center justify-center md:justify-start  gap-5">
                <div class="flex flex-col justify-center items-center md:flex-row">
                    {{-- <h3 id="canMemberBalanceMinMax"
                        class="text-[#7F35B2] text-[18px] md:text-[26px] font-semibold leading-[26px] text-center md:text-left">
                        {{ __('contents.harcayabileceginiz_tutar') }} </h3> --}}

                    <img src="{{ asset('assets/images/144x144px-5.png') }}" alt="alert" class="w-full max-w-[80px] sm:max-w-[160px]">
                    <div class="md:pl-3">
                        <p id="textMinMax" class="text-[#2F2C31] py-3 max-w-[400px] text-[12px] md:text-[16px] text-center md:text-left">

                        </p>
                        <div class="flex items-center justify-center md:justify-start space-x-2 pt-4 rounded-b">
                            <!-- {{-- <a href=" side_benefits_approval.html" data-modal-hide="staticModalTamamlaBalance"
                        type="button"
                        class="text-[#EFF1F3] bg-[#7F35B2] focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm text-center w-full max-w-[121px] h-[30px] flex items-center justify-center">{{ __('contents.yes') }}
                    </a> --}} -->
                            <button id="CloseBtnMinMax" type="button" class="focus:outline-none  rounded-lg text-sm font-medium w-full max-w-[121px] h-[30px] border border-[#7F35B2] text-[#7F35B2] px-3">
                                {{ __('contents.back') }}</button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Modal footer -->
        </div>
    </div>
</div>

@php
$sliderMessage=[];
$defaultMessage=[];

foreach($welcome_messages as $key => $welcome_message){

if($welcome_message['in_slider']==1){
$sliderMessage[]=$welcome_message;
}else{
$defaultMessage[]=$welcome_message;
}

}
@endphp

<!-- <div id="modalSlider" class="fixed hidden top-0 left-0 w-full h-full bg-black bg-opacity-50 justify-center items-center modal_parent_div z-[1000] p-3">
    <div class="bg-white w-full max-w-[400px] md:max-w-[914px] md:h-[276px] h-[500px]  relative rounded-lg px-2 py-5" id="modalSlider_innerdiv" >
        <img id="closeSlider" src="{{ asset('assets/images/close.png') }}" class="absolute top-3 right-3 cursor-pointer close z-50" />
        <div id="default-carousel1" class="relative w-full h-full" data-carousel="static">
            <div class="relative overflow-hidden rounded-lg h-full p-3">
                @foreach ($sliderMessage as $key => $welcome_message)
                <div class="hidden duration-[2s] ease-in-out slide_1_div" data-carousel-item>
                    <div class="flex flex-col items-center justify-center md:flex-row  md:justify-start max-w-[380px] md:max-w-[914px] lg:w-[914px] p-3 bg-white relative rounded-[10px] welcomeSlide_div">
                        <img src="{{ asset('storage/' . $welcome_message->image) }}" alt="welcome" class="w-full max-w-[175px]
                                md:max-w-[210px]">
                        <div class="md:px-[1rem] px-1">
                            <h3 class="text-[20px] md:text-[23px] lg:text-[26px] font-semibold text-[#7F35B2] pt-2 max-w-[305px] md:max-w-[600px] text-left welcomeSlide_title">
                                {{ App::getLocale() == 'en' ? $welcome_message['title_en'] : $welcome_message['title'] }}
                            </h3>

                            <p class="text-[#2F2C31] max-w-[324px] md:max-w-[650px] py-2 lg:py-4 text-[14px] md:text-[16px] text-left welcomeSlide_text">

                                {!! App::getLocale() == 'en' ? strip_tags($welcome_message['description_en'], '<a><strong><br>') : strip_tags($welcome_message['description'], '<a><strong><br>') !!}
                            </p>

                            @if ($welcome_message['show_again'] == 1)
                            <div class="flex items-center gap-2"><input type="checkbox" id="do_not_show_again_slider" name="do_not_show_again_slider" class="cursor-pointer accent-[#7F35B2]"><label for="do_not_show_again_slider" class="text-[15px] cursor-pointer">{{ __('contents.donotShowAgain') }}</label></div>
                            <button id="closeModal_slider" class="bg-[#7F35B2] text-[#EFF1F3] w-full max-w-[300px] md:max-w-[329.14px] h-[40.44px] rounded-md mt-5 mb-2 flex justify-center items-center">{{ __('contents.anladim') }}</button>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <p class="absolute bottom-[-12px] right-8 z-30 text-lg">
                <span id="slide_num">1</span>/{{count($sliderMessage)}}
            </p>
            <button type="button" class="absolute bottom-[-18px] right-[-25px] z-30 flex items-center justify-center px-4 cursor-pointer group focus:outline-none" data-carousel-next id="popup_next_btn">
                <span class="inline-flex items-center justify-center w-10 h-10 rounded-full">
                    <svg class="w-5 h-5 text-[#7F35B2]" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                    </svg>
                    <span class="sr-only">Next</span>
                </span>
            </button>
        </div>
    </div>
</div> -->
<div id="modalSlider" class="fixed hidden top-0 left-0 w-full h-full bg-black bg-opacity-50 justify-center items-center modal_parent_div z-[1000] p-3">
    <div id="slider" class="w-full max-w-[400px] md:max-w-[914px]">
        <img id="closeSlider" src="{{ asset('assets/images/close.png') }}" class="absolute top-3 right-3 cursor-pointer close z-50" />
        <div id="slides">
            @foreach ($sliderMessage as $key => $welcome_message)
            <div class="slide">
                <div class="flex flex-col items-center md:flex-row gap-5 welcomeSlide_div">
                    <img src="{{ asset('storage/' . $welcome_message->image) }}" alt="welcome" class="w-full max-w-[175px]
                        md:max-w-[210px]">
                    <div>
                        <h3 class="text-[18px] md:text-[23px] lg:text-[26px] font-semibold text-[#7F35B2] pt-2  md:max-w-[600px] text-left welcomeSlide_title  pl-3 md:pl-0">
                            {{ App::getLocale() == 'en' ? $welcome_message['title_en'] : $welcome_message['title'] }}
                        </h3>
                        <p class="text-[#2F2C31]  py-2 lg:py-4 text-[14px] md:text-[16px] text-left welcomeSlide_text pl-3 md:pl-0">
                            {!! App::getLocale() == 'en' ? strip_tags($welcome_message['description_en'], '<a><strong><br>') : strip_tags($welcome_message['description'], '<a><strong><br>') !!}
                        </p>

                        @if ($welcome_message['show_again'] == 1)
                        <div class="flex items-center gap-2 welcome_input_div pl-3"><input type="checkbox" id="do_not_show_again_slider" name="do_not_show_again_slider" class="cursor-pointer accent-[#7F35B2]"><label for="do_not_show_again_slider" class="text-[15px] cursor-pointer">{{ __('contents.donotShowAgain') }}</label></div>
                        <button id="closeModal_slider" class="bg-[#7F35B2] text-[#EFF1F3] w-full max-w-[300px] md:max-w-[329.14px] h-[40.44px] rounded-md mt-5 mb-2 flex justify-center items-center ml-3">{{ __('contents.anladim') }}</button>
                        @endif
                    </div>

                </div>

            </div>
            @endforeach



        </div>
        <p class="absolute bottom-3 right-8 z-30 text-lg mr-3">
            <span id="slide_num">1</span>/{{count($sliderMessage)}}
        </p>
        <a href="#" class="control_next" id="popup_next_btn">
            <svg class="w-5 h-5 text-[#7F35B2]" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
            </svg>
        </a>
    </div>

</div>

@foreach ($defaultMessage as $key => $welcome_message)
<div id="modal_{{ $welcome_message['id'] }}" class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 justify-center items-center hidden modal_parent_div_{{$welcome_message['id']}} z-[1000] p-3">
    <div id="modalWh" class="flex flex-col items-center justify-center md:flex-row md:justify-start max-w-[380px] md:max-w-[914px] lg:w-[914px] p-3 bg-white shadow-[0px_0px_20px_#00000017;] relative rounded-[10px] surpriseSlide_div">
        <img id="defaultClose_{{$welcome_message['id']}}" src="{{ asset('assets/images/close.png') }}" class="absolute top-3 right-3 cursor-pointer  close">
        <img src="{{ asset('storage/' . $welcome_message->image) }}" alt="welcome" class="w-full max-w-[175px] md:max-w-[210px]">
        <div class="md:px-[1rem] lg:px-[2rem] lg:py-[1rem] px-1">
            <h3 class="text-[20px] md:text-[26px] font-semibold text-[#7F35B2] pt-2 max-w-[305px] md:max-w-[600px] text-left surpriseSlide_title">
                {{ App::getLocale() == 'en' ? $welcome_message['title_en'] : $welcome_message['title'] }}
            </h3>

            <p class="text-[#2F2C31] max-w-[324px] md:max-w-[650px]  py-4 text-[14px] md:text-[16px] text-left surpriseSlide_text">
                {!! App::getLocale() == 'en' ? strip_tags($welcome_message['description_en'], '<a><strong><br>') : strip_tags($welcome_message['description'], '<a><strong><br>') !!}
            </p>
            @if ($welcome_message['show_again'] == 1)
            <div class="flex items-center gap-2"><input type="checkbox" id="do_not_show_again_{{ $welcome_message['id'] }}" name="do_not_show_again" class="cursor-pointer accent-[#7F35B2]"><label for="do_not_show_again" class="text-[15px] cursor-pointer">{{ __('contents.donotShowAgain') }}</label></div>
            <button id="closeModal_{{ $welcome_message['id'] }}" class="bg-[#7F35B2] text-[#EFF1F3] w-full sm:max-w-[330px] h-[40.44px] rounded-md mt-5 mb-2 flex justify-center items-center ">{{ __('contents.anladim') }}</button>
            @endif
        </div>
    </div>
</div>
@endforeach
<script src="{{ asset('assets/js/jquery.js') }}"></script>

<script>

    var modalOpen = false;
    var rules = "{{ $rules }}";


    function closeModalForRules() {
        var modal = document.getElementById('RulesModal');
        modal.classList.remove('flex');
        modal.classList.add('hidden');
        modalOpen = false;
        window.location.reload();
    }


    $(document).ready(function() {
        var modalIds = @json(array_column($defaultMessage, 'id'));
        var currentModalIndex = 0;
        var sliderMessagesCount = @json(count($sliderMessage));

        // İlk modalı açan fonksiyon
        function openNextModal() {
            if (currentModalIndex < modalIds.length) {
                var modalId = modalIds[currentModalIndex];
                if (!sessionStorage.getItem('welcome_popup_' + modalId)) {
                    $('#modal_' + modalId).removeClass("hidden").addClass("flex");
                    // console.log($('#modal_' + modalId).attr('style'));
                } else {
                    currentModalIndex++;
                    openNextModal();
                }
            }
        }

        function openNextModalSlider() {
            if (currentModalIndex < modalIds.length) {
                var modalId = modalIds[currentModalIndex];
                if (!sessionStorage.getItem('welcome_popup_slider')) {
                    $('#modalSlider').removeClass("hidden").addClass("flex");
                } else {
                    currentModalIndex++;
                    openNextModalSlider();
                }
            }
        }

        if (sliderMessagesCount > 0) {
            if (!sessionStorage.getItem('welcome_popup_slider')) {
                openNextModalSlider();
            } else {
                openNextModal();
            }

            // $("#modalSlider").removeClass("hidden").addClass("flex");
            $("#closeSlider").on("click", function() {
                $("#modalSlider").removeClass("flex").addClass("hidden");
                openNextModal();
            });
        } else {

            openNextModal();
        }


        modalIds.forEach(function(modalId) {
            $('#defaultClose_' + modalId).on('click', function() {
                $('#modal_' + modalId).removeClass("flex").addClass("hidden");
                currentModalIndex++;
                openNextModal();
            });

            $('#closeModal_' + modalId).on('click', function() {
                $('#modal_' + modalId).removeClass("flex").addClass("hidden");
                if ($('#do_not_show_again_' + modalId).is(':checked')) {
                    sessionStorage.setItem('welcome_popup_' + modalId, 'shown');
                }
                currentModalIndex++;
                openNextModal();
            });

            $('#closeModal_slider').on('click', function() {

                $("#modalSlider").removeClass("flex").addClass("hidden");
                if ($('#do_not_show_again_slider').is(':checked')) {
                    sessionStorage.setItem('welcome_popup_slider', 'shown');
                }

                openNextModal();
            });
        });


        let slideNum = Number($("#slide_num").text());

        $("#popup_next_btn").on("click", function() {
            if (slideNum == sliderMessagesCount) {
                slideNum = 1;
            } else {
                slideNum = slideNum + 1;
            }
            $("#slide_num").text(slideNum);
        });


    });




    $('#resetCartButton').click(function(e) {
        e.preventDefault();
        var resetCartUrl = "{{ route('reset.cart') }}";
        var csrfToken = '{{ csrf_token() }}';

        $.ajax({
            url: resetCartUrl,
            type: 'POST',
            data: {
                _token: csrfToken
            },
            success: function(response) {
                $('#staticModal').hide();
                window.location.reload();
            },
            error: function(xhr, status, error) {
                alert('Bir hata oluştu!');
            }
        });
    });
    $('#resetCartModalComponent').click(function(e) {
        e.preventDefault();

        $(this).prop('disabled', true);

        var resetCartUrl = "{{ route('reset.cart') }}";
        var csrfToken = '{{ csrf_token() }}';

        $.ajax({
            url: resetCartUrl,
            type: 'POST',
            data: {
                _token: csrfToken
            },
            success: function(response) {
                window.location.reload();
            },
            error: function(xhr, status, error) {
                alert('Bir hata oluştu!');
                // Re-enable the button in case of an error
                $('#resetCartModalComponent').prop('disabled', false);
            }
        });
    });


    $('#alısverisi_tamamla').click(function(e) {
        e.preventDefault();
        var alisverisiTamamlaUrl = "{{ route('member_dashboard_checkAction') }}";
        var csrfToken = '{{ csrf_token() }}';

        $.ajax({
            url: alisverisiTamamlaUrl,
            type: 'POST',
            data: {
                _token: csrfToken
            },
            success: function(response) {
                if (response.rule2Active && response.customRuleMsg) {
                    $('#staticModalTamamlaBalance').removeClass("hidden").addClass("flex");
                    $('#canMemberBalance').text(response.message);
                }else if(response.component1Active && response.customRuleMsg){
                    $('#staticModalTamamlaBalance').removeClass("hidden").addClass("flex");
                    $('#canMemberBalance').text(response.message);
                } else{
                    var balance = parseFloat(response.member_balance);
                    if (balance > 0) {
                        $('#staticModalTamamlaBalance').removeClass("hidden").addClass("flex");
                        $('#canMemberBalance').text(
                            ' {{ __("contents.harcayabileceginiz_tutar") }} ' + balance + ' TL');
                    } else {
                        if (response.eksiBakiye && response.checkStep) {
                            $('#staticModalTamamlaBalance').removeClass("hidden").addClass("flex");
                            $('#canMemberBalance').text(response.message);
                        } else if (response.eksiBakiye && !response.checkStep) {
                            // $('#staticModalTamamla').removeClass("hidden").addClass("flex");
                            $('#staticModalTamamla').show();
                            $('#MemberCanNegative').text(response.message);
                        } else {

                            $('#staticModalTamamla').show();
                        }
                    }
                }

            },
            error: function(xhr, status, error) {

            }
        });
    });

    $(document).ready(function() {
        $('#CloseBtnMinMax').on('click', function() {
            $('#MinMax').removeClass("flex").addClass("hidden");
            window.location.reload();
        });

        $('#balanceCloseBtn').on('click', function() {
            $('#staticModalTamamlaBalance').removeClass("flex").addClass("hidden");
        });

        $('#closeImgMinMax').on('click', function() {
            $('#MinMax').removeClass("flex").addClass("hidden");
            window.location.reload();
        });


        $('#staticModalCloseBtn').on('click', function() {
            $('#staticModalTamamla').hide();
        });

        $('#staticModalTamamlaImg').on('click', function() {
            $('#staticModalTamamla').hide();
        });


        $('#sideBenefitsApproval').on('click', function(event) {
            event.preventDefault();

            $('#staticModalTamamla').hide();

            window.location.href = $(this).attr('href');
        });

        $('#balanceCloseBtn').on('click', function() {
            $('#staticModalTamamlaBalance').removeClass("flex").addClass("hidden");
        });
    });
    // $('.close').on('click', function() {
    //     $('.modal_parent_div').hide();
    // });
    $(document).on('click', '#closeImgBalance', function() {
        $('#staticModalTamamlaBalance').removeClass("flex").addClass("hidden");
    });



    $(document).ready(function() {

        $('.acorD').click(function() {
            var arrowImage = $(this).find('.white-arrow');
            if (arrowImage.hasClass('rotate-0')) {
                arrowImage.removeClass('rotate-0').addClass('rotate-180');
            } else {
                arrowImage.removeClass('rotate-180').addClass('rotate-0');
            }
        });
        $(document).ready(function() {
            $('#hamburger_menu').click(function() {
                // Toggle the 'z-[1]' class on #slider_parent_div
                $('#slider_parent_div button').removeClass('z-30').addClass("z-[1]");
            });
            $('#mobile_close_btn').click(function() {
                // Toggle the 'z-[1]' class on #slider_parent_div
                $('#slider_parent_div button').removeClass('z-[1]').addClass("z-30");
            });
        });


    });

    /*************************************************************************/
    document.addEventListener("DOMContentLoaded", function() {
        let options = {
            root: null,
            rootMargin: "-100px",
            threshold: 0.1, // Trigger when any part of the targets is visible
        };

        let observer = new IntersectionObserver(callback, options);
        let targets = document.querySelectorAll(
            "#accordion1, #accordion14,#accordion17,#accordion18,#accordion15");

        targets.forEach(target => {
            observer.observe(target);
        });

        function callback(entries, observer) {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    let itemId = (entry.target.id).replace("accordion", "");
                    $(`.accordion_div[data-group-id='${itemId}'] .accordion_item`).removeClass(
                        "max-h-0").addClass("max-h-fit");
                    $(`.accordion_div[data-group-id='${itemId}']`).find(".white-arrow").removeClass(
                        "rotate-0").addClass("rotate-180");
                } else {
                    let itemId = (entry.target.id).replace("accordion", "");
                    $(`.accordion_div[data-group-id='${itemId}'] .accordion_item`).removeClass(
                        "max-h-fit").addClass("max-h-0");
                    $(`.accordion_div[data-group-id='${itemId}']`).find(".white-arrow").removeClass(
                        "rotate-180").addClass("rotate-0");
                }
            });
        }
    });
    /*************************************************************************/

    /*Sadece sayıları kabul eden, virgülleri noktaya çeviren fonk.*/
    function validateInput(input) {
        const cleanedInput = input.value.replace(/[^0-9.,]/g, '');
        input.value = cleanedInput;
    }

    // document.addEventListener("DOMContentLoaded", function () {
    //     document.querySelectorAll('input[id^="birey_field"]').forEach(function (input) {
    //         console.log(input);
    //         input.removeAttribute('readonly');
    //     });
    // });


    $(window).on('load', function() {
        setTimeout(function() {
            $('input').not('#leavePrice').prop('readonly', false);
        }, 1000);
    });


    $(window).on('load', function() {
        setTimeout(function() {
            document.querySelectorAll('select[id^="birey_field_p_"]').forEach(function (select) {
                select.classList.remove('pointer-events-none');
            }); }, 1000);
    });

</script>
<script>
    var currentSlide = 0;
    var totalSlides = document.querySelectorAll('.slide').length;

    document.querySelector('.control_next').addEventListener('click', function() {
        if (currentSlide < totalSlides - 1) {
            currentSlide++;
        } else {
            currentSlide = 0;
        }
        updateSlider();
    });


    function updateSlider() {
        var newTransformValue = -currentSlide * 100 + '%';
        document.querySelector('#slides').style.transition = 'transform 0.5s ease-in-out';
        document.querySelector('#slides').style.transform = 'translateX(' + newTransformValue + ')';

        // Geçiş sonrası transition'ı kaldır
        setTimeout(function() {
            document.querySelector('#slides').style.transition = '';
        }, 500);
    }

    $(document).ready(function() {
        $("#closeSlider").on("click", function() {
            $("#modalSlider").removeClass("flex").addClass("hidden");
        });
    });
</script>
<script>
    $(document).ready(updateLandscape);

    $(window).on('resize', updateLandscape);

    function updateLandscape() {
        var isLandscape = $(window).width() > $(window).height() && $(window).width() < 800;


        if (isLandscape) {
            $("#slider").removeClass("max-w-[400px]");
            $(".welcomeSlide_div").removeClass("flex-col justify-center");
            $(".surpriseSlide_div").removeClass("max-w-[380px] flex-col justify-center");
            $(".surpriseSlide_title").removeClass("max-w-[305px]");
            $(".surpriseSlide_text").removeClass("max-w-[324px]");

        } else {
            $("#slider").addClass(" max-w-[400px]");
            $(".welcomeSlide_div").addClass("flex-col justify-center ");
            $(".surpriseSlide_div").addClass("max-w-[380px] flex-col justify-center");
            $(".surpriseSlide_title").addClass("max-w-[305px]");
            $(".surpriseSlide_text").addClass("max-w-[324px]");
        }
    }
</script>

@endsection
