<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Benefit Report</title>
    <style>
        * {
            font-family: DejaVu Sans !important;
            font-size: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid black;
            padding: 5px;
            text-align: left;
        }
        h1, h3 {
            page-break-before: avoid;
            page-break-after: avoid;
        }
        p {
            page-break-inside: avoid;
        }
        .page-break {
            page-break-after: always;
        }
        .no-page-break {
            page-break-before: avoid;
        }
        @page {
            size: auto;
        }
    </style>
</head>
<body>
@php
    $content = $customSettings->getText('beyanname_taahhutname');
@endphp

@if(isset($title))
    <h3 style="text-align:center">{{ $title }}</h3>
    {!! $content !!}
@endif

@foreach ($lines as $line)
    <p>{{ $line }}</p>
@endforeach
{{--

@if($is_table)
    <h1>{{ $table['title'][0] ?? 'Rapor' }}</h1>
    <table class="page-break">
        <thead>
        <tr>
            @foreach($table['headers'] ?? [] as $header)
                <th colspan="1" style="width: {{ $table['headers_width'][$loop->index] ?? 'auto' }};">{{ $header }}</th>
            @endforeach
        </tr>
        </thead>
        <tbody>
        @foreach($table['rows'] ?? [] as $row)
            <tr>
                @foreach($row as $cell)
                    <td>{{ $cell }}</td>
                @endforeach
            </tr>
        @endforeach
        </tbody>
    </table>
@endif
--}}
@if(isset($table2))
    <br><br>
    <table class="no-page-break">
        <thead>
        <tr>
            @foreach($table2['headers'] ?? [] as $header)
                <th style="width: {{ $table2['headers_width'][$loop->index] ?? 'auto' }};">{{ $header }}</th>
            @endforeach
        </tr>
        </thead>
        <tbody>
        @foreach($table2['rows'] as $row)
            <tr>
                @foreach($row as $cell)
                    <td>{{ $cell }}</td>
                @endforeach
            </tr>
        @endforeach
        </tbody>
        @if(!empty($table2['totals']))
            <tfoot>
            <tr>
                @foreach($table2['totals'] as $total)
                    <td>{{ $total }}</td>
                @endforeach
            </tr>
            </tfoot>
        @endif
    </table>
@endif
</body>
</html>
