
<style>
    .alert-success{
        background: green;
        color: white;
        padding: 10px;
    }

    .alert-danger{
        background: red;
        color: white;
        padding: 10px;

    }

</style>

<x-filament::page>

@if (session('success'))
<div class="alert alert-success">
    {{ session('success') }}
</div>
@endif


@if (session('error'))
    <div class="alert alert-danger">
        {{ session('error') }}
    </div>
@endif

    <form action="{{ route('import-members.import') }}" method="post" enctype="multipart/form-data" autocomplete="off">
        @csrf
        <div>
            <label for="excel_file"><b>Excel Dosyası</b>:</label>
            <x-filament::input.wrapper class="mt-3">
                <x-filament::input
                    type="file"
                    id="excel_file"
                    name="excel_file"
                    accept=".xlsx"
                    required
                />
            </x-filament::input.wrapper>
        </div>
        <div>
                <x-filament::button class="my-4" size="lg" type="submit">Yükle ve İçe Aktar</x-filament::button>
        </div>
    </form>

    <form action="{{ route('import-members.update') }}" method="post" enctype="multipart/form-data" autocomplete="off" >
        @csrf
        <div>
            <label for="update_excel_file"><b>Update Members Excel Dosyası</b>:</label>
            <x-filament::input.wrapper class="mt-3">
                <x-filament::input
                    type="file"
                    id="update_excel_file"
                    name="excel_file"
                    accept=".xlsx"
                    required
                />
            </x-filament::input.wrapper>
        </div>
        <div>
            <x-filament::button class="my-4" size="lg" type="submit">Güncelle</x-filament::button>
        </div>
    </form>


    <form action="{{ route('import-members.export') }}" method="get" enctype="multipart/form-data">
        @csrf
        <div>
            <label for="excel_file"><b>Members Excel Dosyası</b>:</label>

        </div>
        <div>
                <x-filament::button class="my-4" size="lg" type="submit">Dışa Aktar</x-filament::button>
        </div>
    </form>

</x-filament::page>
