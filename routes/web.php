<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
// use App\Filament\Resources\MemberResource\Pages\ImportMembersController;
use App\Http\Controllers\ImportMembersController;
use App\Http\Controllers\ImportExportBenefitsController;
use App\Http\Controllers\MemberAuthController;
use App\Http\Controllers\HelpPageController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\MemberDashboardController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\MemberExcelController;
use App\Http\Controllers\MemberBenefitCustom;
use App\Http\Controllers\PositiveBalanceController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SiteMovementController;
use App\Filament\Resources\MemberBenefitPrefResource\Pages\ListMemberBenefitPrefs;
use App\Filament\Resources\MemberBenefitPrefResource\Pages\SortBenefitMember;
use App\Http\Controllers\CreatePDFController;

use App\Models\QuestionAnswer;
use App\Http\Middleware\CheckSiteMovement;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Route::get('/', function () {
//     return Inertia::render('Dashboard', [
//         'canLogin' => Route::has('login'),
//         'laravelVersion' => Application::VERSION,
//         'phpVersion' => PHP_VERSION,
//     ]);
// })->middleware(['auth'])->name('home');

// Route::get('/dashboard', function () {
//     return Inertia::render('Dashboard');
// })->middleware(['auth'])->name('dashboard');
use Illuminate\Support\Facades\Mail;

Route::get('/site_closed', [SiteMovementController::class, 'show'])->name('site_closed');

Route::middleware([
    'auth'
])->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::post('/import-members/import', [ImportMembersController::class, 'import'])->name('import-members.import');

    Route::post('/import-mailings/import', [SiteMovementController::class, 'import'])->name('import-mailings.import');

    Route::get('import-members/export', [ImportMembersController::class, 'export'])->name('import-members.export');
    Route::post('import-members/update', [ImportMembersController::class, 'updateMembers'])->name('import-members.update');


    Route::get('export-benefits/export', [ImportExportBenefitsController::class, 'exportExcel'])->name('export-benefits.export');
    Route::post('import-benefits/import', [ImportExportBenefitsController::class, 'importExcel'])->name('import-benefits.import');
    Route::post('update-benefits/update', [ImportExportBenefitsController::class, 'updateExcel'])->name('update-benefits.update');

    Route::get('/report-download/{id}', [ReportController::class, 'index'])->name('report.download');
});

Route::post('/forgot-password-process', [MemberAuthController::class, 'forgotten_password'])->name('forgot.password.process');


Route::middleware([CheckSiteMovement::class])->group(function () {

Route::middleware('member.auth')->group(function () {
    Route::get('/member_dashboard', [MemberDashboardController::class, 'show'])->name('member_dashboard');

    Route::post('/member_dashboard', [MemberDashboardController::class, 'addAction'])->name('member_dashboard_addAction');

    Route::post('/member_dashboard/checkAction', [PaymentController::class, 'checkAction'])->name('member_dashboard_checkAction');

    Route::get('/help', [HelpPageController::class, 'show'])->name('help');

    Route::get('/side_benefits_approval', [PositiveBalanceController::class, 'show'])->name('side_benefits_approval');

    Route::post('/reset-cart', [MemberDashboardController::class, 'resetCart'])->name('reset.cart');

    Route::get('/get-init-data', [PaymentController::class, 'getInitData']);

    Route::get('/get-export-pdf', [CreatePDFController::class, 'exportBenefitsToPDF'])->name('get-export-pdf');

    Route::post('/get-init-data-positive', [PositiveBalanceController::class, 'getInitData']);

    Route::post('/approve', [PaymentController::class, 'approve'])->name('approve');

    Route::get('/approval', [PositiveBalanceController::class, 'showApproval'])->name('approval');
    // Route::get('/survey', [QuestionController::class, 'init'])->name('survey');

    Route::post('/approval_question', [QuestionController::class, 'answer'])->name('approval_question');
});


Route::get('set-language/{language}', [LanguageController::class, 'setLanguage'])
    ->name('set.language')
    ->middleware('setLanguage');


Route::get('/', function () {
    if (Auth::guard('member')->check()) {
        return redirect('/member_dashboard');
    }
    return redirect()->route('member_login');
});



Route::get('/member_login', [MemberAuthController::class, 'showLoginForm'])->name('member_login');
Route::post('/member_login', [MemberAuthController::class, 'member_login_process'])->name('member_login_process');
Route::get('/otp', [MemberAuthController::class, 'showOtpForm'])->name('verify_otp');
Route::post('/otp', [MemberAuthController::class, 'verifyOtp'])->name('verify_otp');
Route::post('/otp/resend', [MemberAuthController::class, 'resendOtp'])->name('resend_otp');
Route::get('/member_logout', [MemberAuthController::class, 'logout'])->name('member_logout');

});

require __DIR__.'/auth.php';
